version: '3.1'

services:
  web_prod:
    image: odoo:18.0
    container_name: odoo_prod
    depends_on:
      - pgbouncer
      - redis
    environment:
      - HOST=pgbouncer
      - PORT=6432
      - USER=odoo
      - PASSWORD=odoo
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    ports:
      - "6090:8069"  # Production Odoo on port 8090
    volumes:
      - odoo-prod-data:/var/lib/odoo
      - prod-logs:/var/log/odoo
      - ./config_prod:/etc/odoo
      - ./custom-addons-prod:/mnt/extra-addons
      - odoo-prod-addons:/usr/lib/python3/dist-packages/odoo/addons
    networks:
      - odoo-net

  web_qa:
    image: odoo:18.0
    container_name: odoo_qa
    depends_on:
      - pgbouncer
      - redis
    environment:
      - HOST=pgbouncer
      - PORT=6432
      - USER=odoo
      - PASSWORD=odoo
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    ports:
      - "6080:8069"  # QA Odoo on port 8080
    volumes:
      - odoo-qa-data:/var/lib/odoo
      - qa-logs:/var/log/odoo
      - ./config_qa:/etc/odoo
      - ./custom-addons-qa:/mnt/extra-addons
      - odoo-qa-addons:/usr/lib/python3/dist-packages/odoo/addons
    networks:
      - odoo-net

  web_test:
    image: odoo:18.0
    container_name: odoo_test
    depends_on:
      - pgbouncer
      - redis
    environment:
      - HOST=pgbouncer
      - PORT=6432
      - USER=odoo
      - PASSWORD=odoo
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    ports:
      - "6070:8069"  # Test Odoo on port 8070
    volumes:
      - odoo-test-data:/var/lib/odoo
      - test-logs:/var/log/odoo
      - ./config_test:/etc/odoo
      - ./custom-addons-test:/mnt/extra-addons
      - odoo-test-addons:/usr/lib/python3/dist-packages/odoo/addons
    networks:
      - odoo-net

  pgbouncer:
    image: edoburu/pgbouncer:1.18.0
    container_name: pgbouncer
    environment:
      - DB_USER=odoo
      - DB_PASSWORD=odoo
      - DB_HOST=db
      - DB_NAME=postgres
    volumes:
      - ./pgbouncer/pgbouncer.ini:/etc/pgbouncer/pgbouncer.ini:ro
      - ./pgbouncer/userlist.txt:/etc/pgbouncer/userlist.txt:ro
    ports:
      - "6432:6432"
    depends_on:
      - db
    networks:
      - odoo-net

  db:
    image: postgres:15
    container_name: postgres-db
    environment:
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=odoo
      - POSTGRES_DB=postgres
    volumes:
      - odoo-db-data:/var/lib/postgresql/data
    networks:
      - odoo-net

  redis:
    image: redis:alpine
    container_name: redis
    command: ["redis-server", "--appendonly", "yes", "--appendfsync", "everysec"]
    volumes:
      - redis-data:/data
    networks:
      - odoo-net

volumes:
  odoo-prod-data:
  odoo-qa-data:
  odoo-test-data:
  odoo-prod-addons:
  odoo-qa-addons:
  odoo-test-addons:
  odoo-db-data:
  redis-data:
  qa-logs:
  prod-logs:
  test-logs:
  
networks:
  odoo-net:
    driver: bridge

