[tool.poetry]
name = "SalesGPT"
version = "0.1.2"  
description = "SalesGPT - Your Context-Aware AI Sales Assistant"
authors = ["<PERSON><PERSON> "]
license = "Apache-2.0"
readme = "README.md"
homepage = "https://github.com/filip-michalsky/SalesGPT"
repository = "https://github.com/filip-michalsky/SalesGPT"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
keywords = ["openai", "sales", "gpt", "autonomous", "agi"]


[tool.poetry.dependencies]
python = "^3.8.1"
langchain = "0.1.0"
openai = "1.7.0"
chromadb = "^0.4.18"
tiktoken = "^0.5.2"
pydantic = "^2.5.2"
litellm = "^1.10.2"
ipykernel = "^6.27.1"
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.23.1"
langchain-openai = "0.0.2"
tokenizers = "^0.15.2"
boto3 = ">=1.33.2,<1.34.35"
aioboto3 = "^12.3.0"

[tool.poetry.group.dev.dependencies]
black = "^23.11.0"
flake8 = "^6.1.0"
isort = "^5.12.0"
pytest = "^7.4.3"
pytest-cov = "^4.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
