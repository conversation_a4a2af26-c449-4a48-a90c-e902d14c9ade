#LLM/AWS config
OPENAI_API_KEY=xx
AWS_ACCESS_KEY_ID=xx
AWS_SECRET_ACCESS_KEY=xx
AWS_REGION_NAME=xx
GPT_MODEL=gpt-3.5-turbo-0613
HUGGGINGFACE_API_KEY=xx

#Agent setup
USE_TOOLS_IN_API=True
CONFIG_PATH=examples/example_agent_setup.json
PRODUCT_CATALOG=examples/sample_product_catalog.txt
PRODUCT_PRICE_MAPPING=examples/example_product_price_id_mapping.json

#Gmail API config for sending emails
GMAIL_APP_PASSWORD=xx
GMAIL_MAIL=yy

#Stripe config for payments
STRIPE_API_KEY=xx
PAYMENT_GATEWAY_URL=https://agent-payments-gateway.vercel.app/payment

#Calendly config for scheduling meetings
CALENDLY_API_KEY=xx
CALENDLY_EVENT_UUID=yy

#Enable local api startup
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_ENVIRONMENT=development

