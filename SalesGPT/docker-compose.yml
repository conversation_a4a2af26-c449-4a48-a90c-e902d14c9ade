version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.frontend  # Make sure this file exists in ./frontend directory
    volumes:
      - ./frontend:/usr/src/app  # Mount the frontend directory to the container
    container_name: frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000  # Ensure this API URL is correct
    env_file:
      - .env.fe  # Ensure .env.fe file exists and is properly formatted
    ports:
      - "3000:3000"  # Expose port 3000 for the frontend
    depends_on:
      - backend  # Ensure backend service is started before frontend
    stdin_open: true
    tty: true

  backend:
    build:
      context: ./  # Backend Dockerfile located in the root directory
      dockerfile: Dockerfile.backend  # Make sure this file exists
    volumes:
      - .:/app  # Mount the current directory to /app in the container
    container_name: backend
    env_file:
      - .env  # Ensure .env file exists and is properly configured
    ports:
      - "8000:8000"  # Expose port 8000 for the backend service
    # No need to specify additional ports if only backend is being exposed internally


