<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" />
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-VH1ZNBVHSP"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-VH1ZNBVHSP');
    </script>
    
        <script async src="https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            gtag('config', 'GTM-NX3SZD79');
            
        </script>
    
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Python Module Index &mdash; SalesGPT  documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=a36129ee" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=5929fcd5"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NX3SZD79');</script>
<!-- End Google Tag Manager -->
</script>
        <script>https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79</script>
        <script>google_analytics_tracker.js</script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 


</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            SalesGPT
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
          <div class="wy-menu-extra">
            <a href="https://salesgpt.vercel.app" class="btn-primary">Docs</a>
          </div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.agents.html">Agents</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.chains.html">Chains</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.logger.html">Logger</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.parsers.html">Parsers</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts.html">Prompts</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts_cn.html">Prompts_cn</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.salesgptapi.html">SalesGPT API</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.stages.html">Stages</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.templates.html">Templates</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.tools.html">Tools</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.version.html">Version</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">SalesGPT</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Python Module Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

   <h1>Python Module Index</h1>

   <div class="modindex-jumpbox">
   <a href="#cap-s"><strong>s</strong></a>
   </div>

   <table class="indextable modindextable">
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-s"><td></td><td>
       <strong>s</strong></td><td></td></tr>
     <tr>
       <td><img src="_static/minus.png" class="toggler"
              id="toggle-1" style="display: none" alt="-" /></td>
       <td>
       <a href="salesgpt/salesgpt.html#module-salesgpt"><code class="xref">salesgpt</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.agents.html#module-salesgpt.agents"><code class="xref">salesgpt.agents</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.chains.html#module-salesgpt.chains"><code class="xref">salesgpt.chains</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.logger.html#module-salesgpt.logger"><code class="xref">salesgpt.logger</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.parsers.html#module-salesgpt.parsers"><code class="xref">salesgpt.parsers</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.prompts.html#module-salesgpt.prompts"><code class="xref">salesgpt.prompts</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.prompts_cn.html#module-salesgpt.prompts_cn"><code class="xref">salesgpt.prompts_cn</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.salesgptapi.html#module-salesgpt.salesgptapi"><code class="xref">salesgpt.salesgptapi</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.stages.html#module-salesgpt.stages"><code class="xref">salesgpt.stages</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.templates.html#module-salesgpt.templates"><code class="xref">salesgpt.templates</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.tools.html#module-salesgpt.tools"><code class="xref">salesgpt.tools</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="salesgpt/salesgpt.version.html#module-salesgpt.version"><code class="xref">salesgpt.version</code></a></td><td>
       <em></em></td></tr>
   </table>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Filip-Michalsky.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>



    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>