<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>salesgpt &mdash; SalesGPT  documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=4698285b" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=5929fcd5"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            SalesGPT
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">salesgpt</a></li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">SalesGPT</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">salesgpt</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/modules.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="salesgpt">
<h1>salesgpt<a class="headerlink" href="#salesgpt" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.html">salesgpt package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#salesgpt-agents-module">salesgpt.agents module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#salesgpt-chains-module">salesgpt.chains module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt.logger">salesgpt.logger module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.html#salesgpt.logger.TimeFilter"><code class="docutils literal notranslate"><span class="pre">TimeFilter</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.logger.TimeFilter.filter"><code class="docutils literal notranslate"><span class="pre">TimeFilter.filter()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.html#salesgpt.logger.time_logger"><code class="docutils literal notranslate"><span class="pre">time_logger()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt.parsers">salesgpt.parsers module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.html#salesgpt.parsers.SalesConvoOutputParser"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.parsers.SalesConvoOutputParser.ai_prefix"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.ai_prefix</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.parsers.SalesConvoOutputParser.get_format_instructions"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.get_format_instructions()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.parsers.SalesConvoOutputParser.parse"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.parse()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.parsers.SalesConvoOutputParser.verbose"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.verbose</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt.prompts">salesgpt.prompts module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt.prompts_cn">salesgpt.prompts_cn module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#salesgpt-salesgptapi-module">salesgpt.salesgptapi module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt.stages">salesgpt.stages module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt.templates">salesgpt.templates module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.html#salesgpt.templates.CustomPromptTemplateForTools"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.templates.CustomPromptTemplateForTools.format"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.format()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.templates.CustomPromptTemplateForTools.template"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.template</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="salesgpt.html#salesgpt.templates.CustomPromptTemplateForTools.tools_getter"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.tools_getter</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#salesgpt-tools-module">salesgpt.tools module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt.version">salesgpt.version module</a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.html#module-salesgpt">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Filip-Michalsky.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>