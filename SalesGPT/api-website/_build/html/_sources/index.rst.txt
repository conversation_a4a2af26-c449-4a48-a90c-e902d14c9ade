.. SalesGPT documentation master file, created by
   sphinx-quickstart on Thu Jan 18 11:25:41 2024.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Welcome to SalesGPT's documentation!
====================================

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   Agents <salesgpt/salesgpt.agents>
   Chains <salesgpt/salesgpt.chains>
   Logger <salesgpt/salesgpt.logger> 
   Parsers <salesgpt/salesgpt.parsers>
   Prompts <salesgpt/salesgpt.prompts>
   Prompts_cn <salesgpt/salesgpt.prompts_cn>
   SalesGPT API <salesgpt/salesgpt.salesgptapi>
   Stages <salesgpt/salesgpt.stages>
   Templates <salesgpt/salesgpt.templates>
   Tools <salesgpt/salesgpt.tools>
   Version <salesgpt/salesgpt.version>

.. role:: red-text

:red-text:`We have just begun building this website and are still actively working on it. Any suggestions to improve readability / desired contents are welcome! Please contact @chemik-bit on Github.`

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

