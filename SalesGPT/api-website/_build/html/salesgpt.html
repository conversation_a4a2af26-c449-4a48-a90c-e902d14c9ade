<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>salesgpt package &mdash; SalesGPT  documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=4698285b" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=5929fcd5"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            SalesGPT
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">salesgpt package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#salesgpt-agents-module">salesgpt.agents module</a></li>
<li><a class="reference internal" href="#salesgpt-chains-module">salesgpt.chains module</a></li>
<li><a class="reference internal" href="#module-salesgpt.logger">salesgpt.logger module</a><ul>
<li><a class="reference internal" href="#salesgpt.logger.TimeFilter"><code class="docutils literal notranslate"><span class="pre">TimeFilter</span></code></a><ul>
<li><a class="reference internal" href="#salesgpt.logger.TimeFilter.filter"><code class="docutils literal notranslate"><span class="pre">TimeFilter.filter()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#salesgpt.logger.time_logger"><code class="docutils literal notranslate"><span class="pre">time_logger()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-salesgpt.parsers">salesgpt.parsers module</a><ul>
<li><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser</span></code></a><ul>
<li><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.ai_prefix"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.ai_prefix</span></code></a></li>
<li><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.get_format_instructions"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.get_format_instructions()</span></code></a></li>
<li><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.parse"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.parse()</span></code></a></li>
<li><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.verbose"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.verbose</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-salesgpt.prompts">salesgpt.prompts module</a></li>
<li><a class="reference internal" href="#module-salesgpt.prompts_cn">salesgpt.prompts_cn module</a></li>
<li><a class="reference internal" href="#salesgpt-salesgptapi-module">salesgpt.salesgptapi module</a></li>
<li><a class="reference internal" href="#module-salesgpt.stages">salesgpt.stages module</a></li>
<li><a class="reference internal" href="#module-salesgpt.templates">salesgpt.templates module</a><ul>
<li><a class="reference internal" href="#salesgpt.templates.CustomPromptTemplateForTools"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools</span></code></a><ul>
<li><a class="reference internal" href="#salesgpt.templates.CustomPromptTemplateForTools.format"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.format()</span></code></a></li>
<li><a class="reference internal" href="#salesgpt.templates.CustomPromptTemplateForTools.template"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.template</span></code></a></li>
<li><a class="reference internal" href="#salesgpt.templates.CustomPromptTemplateForTools.tools_getter"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.tools_getter</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#salesgpt-tools-module">salesgpt.tools module</a></li>
<li><a class="reference internal" href="#module-salesgpt.version">salesgpt.version module</a></li>
<li><a class="reference internal" href="#module-salesgpt">Module contents</a></li>
</ul>
</li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">SalesGPT</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">salesgpt package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/salesgpt.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="salesgpt-package">
<h1>salesgpt package<a class="headerlink" href="#salesgpt-package" title="Link to this heading"></a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
</section>
<section id="salesgpt-agents-module">
<h2>salesgpt.agents module<a class="headerlink" href="#salesgpt-agents-module" title="Link to this heading"></a></h2>
</section>
<section id="salesgpt-chains-module">
<h2>salesgpt.chains module<a class="headerlink" href="#salesgpt-chains-module" title="Link to this heading"></a></h2>
</section>
<section id="module-salesgpt.logger">
<span id="salesgpt-logger-module"></span><h2>salesgpt.logger module<a class="headerlink" href="#module-salesgpt.logger" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="salesgpt.logger.TimeFilter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">salesgpt.logger.</span></span><span class="sig-name descname"><span class="pre">TimeFilter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#salesgpt.logger.TimeFilter" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">Filter</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="salesgpt.logger.TimeFilter.filter">
<span class="sig-name descname"><span class="pre">filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">record</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#salesgpt.logger.TimeFilter.filter" title="Link to this definition"></a></dt>
<dd><p>Determine if the specified record is to be logged.</p>
<p>Returns True if the record should be logged, or False otherwise.
If deemed appropriate, the record may be modified in-place.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="salesgpt.logger.time_logger">
<span class="sig-prename descclassname"><span class="pre">salesgpt.logger.</span></span><span class="sig-name descname"><span class="pre">time_logger</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#salesgpt.logger.time_logger" title="Link to this definition"></a></dt>
<dd><p>Decorator function to log time taken by any function.</p>
</dd></dl>

</section>
<section id="module-salesgpt.parsers">
<span id="salesgpt-parsers-module"></span><h2>salesgpt.parsers module<a class="headerlink" href="#module-salesgpt.parsers" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">salesgpt.parsers.</span></span><span class="sig-name descname"><span class="pre">SalesConvoOutputParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ai_prefix</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'AI'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">verbose</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">AgentOutputParser</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.ai_prefix">
<span class="sig-name descname"><span class="pre">ai_prefix</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.ai_prefix" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.get_format_instructions">
<span class="sig-name descname"><span class="pre">get_format_instructions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.get_format_instructions" title="Link to this definition"></a></dt>
<dd><p>Instructions on how the LLM output should be formatted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.parse">
<span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">AgentAction</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">AgentFinish</span></span></span><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.parse" title="Link to this definition"></a></dt>
<dd><p>Parse text into agent action/finish.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.verbose">
<span class="sig-name descname"><span class="pre">verbose</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.verbose" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-salesgpt.prompts">
<span id="salesgpt-prompts-module"></span><h2>salesgpt.prompts module<a class="headerlink" href="#module-salesgpt.prompts" title="Link to this heading"></a></h2>
</section>
<section id="module-salesgpt.prompts_cn">
<span id="salesgpt-prompts-cn-module"></span><h2>salesgpt.prompts_cn module<a class="headerlink" href="#module-salesgpt.prompts_cn" title="Link to this heading"></a></h2>
</section>
<section id="salesgpt-salesgptapi-module">
<h2>salesgpt.salesgptapi module<a class="headerlink" href="#salesgpt-salesgptapi-module" title="Link to this heading"></a></h2>
</section>
<section id="module-salesgpt.stages">
<span id="salesgpt-stages-module"></span><h2>salesgpt.stages module<a class="headerlink" href="#module-salesgpt.stages" title="Link to this heading"></a></h2>
</section>
<section id="module-salesgpt.templates">
<span id="salesgpt-templates-module"></span><h2>salesgpt.templates module<a class="headerlink" href="#module-salesgpt.templates" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="salesgpt.templates.CustomPromptTemplateForTools">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">salesgpt.templates.</span></span><span class="sig-name descname"><span class="pre">CustomPromptTemplateForTools</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">input_variables</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">input_types</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Any</span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">output_parser</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">BaseOutputParser</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">partial_variables</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Mapping</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">template</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tools_getter</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#salesgpt.templates.CustomPromptTemplateForTools" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">StringPromptTemplate</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="salesgpt.templates.CustomPromptTemplateForTools.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="headerlink" href="#salesgpt.templates.CustomPromptTemplateForTools.format" title="Link to this definition"></a></dt>
<dd><p>Format the prompt with the inputs.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>kwargs: Any arguments to be passed to the prompt template.</p>
</dd>
<dt>Returns:</dt><dd><p>A formatted string.</p>
</dd>
</dl>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">prompt</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">variable1</span><span class="o">=</span><span class="s2">&quot;foo&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="salesgpt.templates.CustomPromptTemplateForTools.template">
<span class="sig-name descname"><span class="pre">template</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#salesgpt.templates.CustomPromptTemplateForTools.template" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="salesgpt.templates.CustomPromptTemplateForTools.tools_getter">
<span class="sig-name descname"><span class="pre">tools_getter</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span></em><a class="headerlink" href="#salesgpt.templates.CustomPromptTemplateForTools.tools_getter" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="salesgpt-tools-module">
<h2>salesgpt.tools module<a class="headerlink" href="#salesgpt-tools-module" title="Link to this heading"></a></h2>
</section>
<section id="module-salesgpt.version">
<span id="salesgpt-version-module"></span><h2>salesgpt.version module<a class="headerlink" href="#module-salesgpt.version" title="Link to this heading"></a></h2>
<p>Version information.</p>
</section>
<section id="module-salesgpt">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-salesgpt" title="Link to this heading"></a></h2>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Filip-Michalsky.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>