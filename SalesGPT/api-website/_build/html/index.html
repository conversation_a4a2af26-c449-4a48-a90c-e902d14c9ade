<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-VH1ZNBVHSP"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-VH1ZNBVHSP');
    </script>
    
        <script async src="https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            gtag('config', 'GTM-NX3SZD79');
            
        </script>
    
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Welcome to SalesGPT’s documentation! &mdash; SalesGPT  documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=a36129ee" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=5929fcd5"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NX3SZD79');</script>
<!-- End Google Tag Manager -->
</script>
        <script>https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79</script>
        <script>google_analytics_tracker.js</script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="salesgpt.agents module" href="salesgpt/salesgpt.agents.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="#" class="icon icon-home">
            SalesGPT
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
          <div class="wy-menu-extra">
            <a href="https://salesgpt.vercel.app" class="btn-primary">Docs</a>
          </div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.agents.html">Agents</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.chains.html">Chains</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.logger.html">Logger</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.parsers.html">Parsers</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts.html">Prompts</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts_cn.html">Prompts_cn</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.salesgptapi.html">SalesGPT API</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.stages.html">Stages</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.templates.html">Templates</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.tools.html">Tools</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.version.html">Version</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">SalesGPT</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Welcome to SalesGPT’s documentation!</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="welcome-to-salesgpt-s-documentation">
<h1>Welcome to SalesGPT’s documentation!<a class="headerlink" href="#welcome-to-salesgpt-s-documentation" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.agents.html">Agents</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT"><code class="docutils literal notranslate"><span class="pre">SalesGPT</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.chains.html">Chains</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.chains.html#salesgpt.chains.SalesConversationChain"><code class="docutils literal notranslate"><span class="pre">SalesConversationChain</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.chains.html#salesgpt.chains.StageAnalyzerChain"><code class="docutils literal notranslate"><span class="pre">StageAnalyzerChain</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.logger.html">Logger</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.logger.html#salesgpt.logger.TimeFilter"><code class="docutils literal notranslate"><span class="pre">TimeFilter</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.logger.html#salesgpt.logger.time_logger"><code class="docutils literal notranslate"><span class="pre">time_logger()</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.parsers.html">Parsers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts.html">Prompts</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts_cn.html">Prompts_cn</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.salesgptapi.html">SalesGPT API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI"><code class="docutils literal notranslate"><span class="pre">SalesGPTAPI</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.stages.html">Stages</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.templates.html">Templates</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.tools.html">Tools</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.completion_bedrock"><code class="docutils literal notranslate"><span class="pre">completion_bedrock()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.generate_calendly_invitation_link"><code class="docutils literal notranslate"><span class="pre">generate_calendly_invitation_link()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.generate_stripe_payment_link"><code class="docutils literal notranslate"><span class="pre">generate_stripe_payment_link()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.get_mail_body_subject_from_query"><code class="docutils literal notranslate"><span class="pre">get_mail_body_subject_from_query()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.get_product_id_from_query"><code class="docutils literal notranslate"><span class="pre">get_product_id_from_query()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.get_tools"><code class="docutils literal notranslate"><span class="pre">get_tools()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.send_email_tool"><code class="docutils literal notranslate"><span class="pre">send_email_tool()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.send_email_with_gmail"><code class="docutils literal notranslate"><span class="pre">send_email_with_gmail()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt/salesgpt.tools.html#salesgpt.tools.setup_knowledge_base"><code class="docutils literal notranslate"><span class="pre">setup_knowledge_base()</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.version.html">Version</a></li>
</ul>
</div>
<p><span class="red-text">We have just begun building this website and are still actively working on it. Any suggestions to improve readability / desired contents are welcome! Please contact &#64;chemik-bit on Github.</span></p>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="salesgpt/salesgpt.agents.html" class="btn btn-neutral float-right" title="salesgpt.agents module" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Filip-Michalsky.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>



    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>