<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" />
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-VH1ZNBVHSP"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-VH1ZNBVHSP');
    </script>
    
        <script async src="https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            gtag('config', 'GTM-NX3SZD79');
            
        </script>
    
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Index &mdash; SalesGPT  documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=a36129ee" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=5929fcd5"></script>
        <script src="_static/doctools.js?v=888ff710"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NX3SZD79');</script>
<!-- End Google Tag Manager -->
</script>
        <script>https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79</script>
        <script>google_analytics_tracker.js</script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            SalesGPT
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
          <div class="wy-menu-extra">
            <a href="https://salesgpt.vercel.app" class="btn-primary">Docs</a>
          </div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.agents.html">Agents</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.chains.html">Chains</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.logger.html">Logger</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.parsers.html">Parsers</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts.html">Prompts</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.prompts_cn.html">Prompts_cn</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.salesgptapi.html">SalesGPT API</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.stages.html">Stages</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.templates.html">Templates</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.tools.html">Tools</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt/salesgpt.version.html">Version</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">SalesGPT</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#A"><strong>A</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#K"><strong>K</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 | <a href="#V"><strong>V</strong></a>
 
</div>
<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.acall">acall() (salesgpt.agents.SalesGPT method)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.acompletion_with_retry">acompletion_with_retry() (salesgpt.agents.SalesGPT method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.adetermine_conversation_stage">adetermine_conversation_stage() (salesgpt.agents.SalesGPT method)</a>
</li>
      <li><a href="salesgpt/salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.ai_prefix">ai_prefix (salesgpt.parsers.SalesConvoOutputParser attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.astep">astep() (salesgpt.agents.SalesGPT method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.company_business">company_business (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.company_name">company_name (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.company_values">company_values (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.completion_bedrock">completion_bedrock() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_history">conversation_history (salesgpt.agents.SalesGPT attribute)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_purpose">conversation_purpose (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_stage_dict">conversation_stage_dict (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_stage_id">conversation_stage_id (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_type">conversation_type (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.current_conversation_stage">current_conversation_stage (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools">CustomPromptTemplateForTools (class in salesgpt.templates)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.determine_conversation_stage">determine_conversation_stage() (salesgpt.agents.SalesGPT method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI.do">do() (salesgpt.salesgptapi.SalesGPTAPI method)</a>
</li>
      <li><a href="salesgpt/salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI.do_stream">do_stream() (salesgpt.salesgptapi.SalesGPTAPI method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.logger.html#salesgpt.logger.TimeFilter.filter">filter() (salesgpt.logger.TimeFilter method)</a>
</li>
      <li><a href="salesgpt/salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools.format">format() (salesgpt.templates.CustomPromptTemplateForTools method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.from_llm">from_llm() (salesgpt.agents.SalesGPT class method)</a>

      <ul>
        <li><a href="salesgpt/salesgpt.chains.html#salesgpt.chains.SalesConversationChain.from_llm">(salesgpt.chains.SalesConversationChain class method)</a>
</li>
        <li><a href="salesgpt/salesgpt.chains.html#salesgpt.chains.StageAnalyzerChain.from_llm">(salesgpt.chains.StageAnalyzerChain class method)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.generate_calendly_invitation_link">generate_calendly_invitation_link() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.generate_stripe_payment_link">generate_stripe_payment_link() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.get_format_instructions">get_format_instructions() (salesgpt.parsers.SalesConvoOutputParser method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.get_mail_body_subject_from_query">get_mail_body_subject_from_query() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.get_product_id_from_query">get_product_id_from_query() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.get_tools">get_tools() (in module salesgpt.tools)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.human_step">human_step() (salesgpt.agents.SalesGPT method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI.initialize_agent">initialize_agent() (salesgpt.salesgptapi.SalesGPTAPI method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.input_keys">input_keys (salesgpt.agents.SalesGPT property)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="K">K</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.knowledge_base">knowledge_base (salesgpt.agents.SalesGPT attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.model_name">model_name (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li>
    module

      <ul>
        <li><a href="salesgpt/salesgpt.html#module-salesgpt">salesgpt</a>
</li>
        <li><a href="salesgpt/salesgpt.agents.html#module-salesgpt.agents">salesgpt.agents</a>
</li>
        <li><a href="salesgpt/salesgpt.chains.html#module-salesgpt.chains">salesgpt.chains</a>
</li>
        <li><a href="salesgpt/salesgpt.logger.html#module-salesgpt.logger">salesgpt.logger</a>
</li>
        <li><a href="salesgpt/salesgpt.parsers.html#module-salesgpt.parsers">salesgpt.parsers</a>
</li>
        <li><a href="salesgpt/salesgpt.prompts.html#module-salesgpt.prompts">salesgpt.prompts</a>
</li>
        <li><a href="salesgpt/salesgpt.prompts_cn.html#module-salesgpt.prompts_cn">salesgpt.prompts_cn</a>
</li>
        <li><a href="salesgpt/salesgpt.salesgptapi.html#module-salesgpt.salesgptapi">salesgpt.salesgptapi</a>
</li>
        <li><a href="salesgpt/salesgpt.stages.html#module-salesgpt.stages">salesgpt.stages</a>
</li>
        <li><a href="salesgpt/salesgpt.templates.html#module-salesgpt.templates">salesgpt.templates</a>
</li>
        <li><a href="salesgpt/salesgpt.tools.html#module-salesgpt.tools">salesgpt.tools</a>
</li>
        <li><a href="salesgpt/salesgpt.version.html#module-salesgpt.version">salesgpt.version</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.output_keys">output_keys (salesgpt.agents.SalesGPT property)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.parse">parse() (salesgpt.parsers.SalesConvoOutputParser method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.retrieve_conversation_stage">retrieve_conversation_stage() (salesgpt.agents.SalesGPT method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.sales_agent_executor">sales_agent_executor (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.sales_conversation_utterance_chain">sales_conversation_utterance_chain (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.chains.html#salesgpt.chains.SalesConversationChain">SalesConversationChain (class in salesgpt.chains)</a>
</li>
      <li><a href="salesgpt/salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser">SalesConvoOutputParser (class in salesgpt.parsers)</a>
</li>
      <li>
    salesgpt

      <ul>
        <li><a href="salesgpt/salesgpt.html#module-salesgpt">module</a>
</li>
      </ul></li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT">SalesGPT (class in salesgpt.agents)</a>
</li>
      <li>
    salesgpt.agents

      <ul>
        <li><a href="salesgpt/salesgpt.agents.html#module-salesgpt.agents">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.chains

      <ul>
        <li><a href="salesgpt/salesgpt.chains.html#module-salesgpt.chains">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.logger

      <ul>
        <li><a href="salesgpt/salesgpt.logger.html#module-salesgpt.logger">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.parsers

      <ul>
        <li><a href="salesgpt/salesgpt.parsers.html#module-salesgpt.parsers">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.prompts

      <ul>
        <li><a href="salesgpt/salesgpt.prompts.html#module-salesgpt.prompts">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.prompts_cn

      <ul>
        <li><a href="salesgpt/salesgpt.prompts_cn.html#module-salesgpt.prompts_cn">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    salesgpt.salesgptapi

      <ul>
        <li><a href="salesgpt/salesgpt.salesgptapi.html#module-salesgpt.salesgptapi">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.stages

      <ul>
        <li><a href="salesgpt/salesgpt.stages.html#module-salesgpt.stages">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.templates

      <ul>
        <li><a href="salesgpt/salesgpt.templates.html#module-salesgpt.templates">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.tools

      <ul>
        <li><a href="salesgpt/salesgpt.tools.html#module-salesgpt.tools">module</a>
</li>
      </ul></li>
      <li>
    salesgpt.version

      <ul>
        <li><a href="salesgpt/salesgpt.version.html#module-salesgpt.version">module</a>
</li>
      </ul></li>
      <li><a href="salesgpt/salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI">SalesGPTAPI (class in salesgpt.salesgptapi)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.salesperson_name">salesperson_name (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.salesperson_role">salesperson_role (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.seed_agent">seed_agent() (salesgpt.agents.SalesGPT method)</a>
</li>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.send_email_tool">send_email_tool() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.send_email_with_gmail">send_email_with_gmail() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.tools.html#salesgpt.tools.setup_knowledge_base">setup_knowledge_base() (in module salesgpt.tools)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.stage_analyzer_chain">stage_analyzer_chain (salesgpt.agents.SalesGPT attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.chains.html#salesgpt.chains.StageAnalyzerChain">StageAnalyzerChain (class in salesgpt.chains)</a>
</li>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.step">step() (salesgpt.agents.SalesGPT method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools.template">template (salesgpt.templates.CustomPromptTemplateForTools attribute)</a>
</li>
      <li><a href="salesgpt/salesgpt.logger.html#salesgpt.logger.time_logger">time_logger() (in module salesgpt.logger)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.logger.html#salesgpt.logger.TimeFilter">TimeFilter (class in salesgpt.logger)</a>
</li>
      <li><a href="salesgpt/salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools.tools_getter">tools_getter (salesgpt.templates.CustomPromptTemplateForTools attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.agents.html#salesgpt.agents.SalesGPT.use_tools">use_tools (salesgpt.agents.SalesGPT attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="salesgpt/salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.verbose">verbose (salesgpt.parsers.SalesConvoOutputParser attribute)</a>
</li>
  </ul></td>
</tr></table>



           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Filip-Michalsky.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>



    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>