<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-VH1ZNBVHSP"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-VH1ZNBVHSP');
    </script>
    
        <script async src="https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            gtag('config', 'GTM-NX3SZD79');
            
        </script>
    
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>salesgpt.parsers module &mdash; SalesGPT  documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=a36129ee" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=5929fcd5"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NX3SZD79');</script>
<!-- End Google Tag Manager -->
</script>
        <script>https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79</script>
        <script>google_analytics_tracker.js</script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="salesgpt.prompts module" href="salesgpt.prompts.html" />
    <link rel="prev" title="salesgpt.logger module" href="salesgpt.logger.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            SalesGPT
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
          <div class="wy-menu-extra">
            <a href="https://salesgpt.vercel.app" class="btn-primary">Docs</a>
          </div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="salesgpt.agents.html">Agents</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.chains.html">Chains</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.logger.html">Logger</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Parsers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.ai_prefix"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.ai_prefix</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.get_format_instructions"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.get_format_instructions()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.parse"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.parse()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#salesgpt.parsers.SalesConvoOutputParser.verbose"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.verbose</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.prompts.html">Prompts</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.prompts_cn.html">Prompts_cn</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.salesgptapi.html">SalesGPT API</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.stages.html">Stages</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.templates.html">Templates</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.tools.html">Tools</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.version.html">Version</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">SalesGPT</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">salesgpt.parsers module</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/salesgpt/salesgpt.parsers.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-salesgpt.parsers">
<span id="salesgpt-parsers-module"></span><h1>salesgpt.parsers module<a class="headerlink" href="#module-salesgpt.parsers" title="Link to this heading"></a></h1>
<dl class="py class">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">salesgpt.parsers.</span></span><span class="sig-name descname"><span class="pre">SalesConvoOutputParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ai_prefix</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'AI'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">verbose</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">AgentOutputParser</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.ai_prefix">
<span class="sig-name descname"><span class="pre">ai_prefix</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.ai_prefix" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.get_format_instructions">
<span class="sig-name descname"><span class="pre">get_format_instructions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.get_format_instructions" title="Link to this definition"></a></dt>
<dd><p>Instructions on how the LLM output should be formatted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.parse">
<span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">AgentAction</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">AgentFinish</span></span></span><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.parse" title="Link to this definition"></a></dt>
<dd><p>Parse text into agent action/finish.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="salesgpt.parsers.SalesConvoOutputParser.verbose">
<span class="sig-name descname"><span class="pre">verbose</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#salesgpt.parsers.SalesConvoOutputParser.verbose" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="salesgpt.logger.html" class="btn btn-neutral float-left" title="salesgpt.logger module" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="salesgpt.prompts.html" class="btn btn-neutral float-right" title="salesgpt.prompts module" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Filip-Michalsky.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>



    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>