<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-VH1ZNBVHSP"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-VH1ZNBVHSP');
    </script>
    
        <script async src="https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            gtag('config', 'GTM-NX3SZD79');
            
        </script>
    
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>salesgpt package &mdash; SalesGPT  documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=a36129ee" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=5929fcd5"></script>
        <script src="../_static/doctools.js?v=888ff710"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NX3SZD79');</script>
<!-- End Google Tag Manager -->
</script>
        <script>https://www.googletagmanager.com/gtag/js?id=GTM-NX3SZD79</script>
        <script>google_analytics_tracker.js</script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            SalesGPT
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
          <div class="wy-menu-extra">
            <a href="https://salesgpt.vercel.app" class="btn-primary">Docs</a>
          </div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.agents.html">Agents</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.chains.html">Chains</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.logger.html">Logger</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.parsers.html">Parsers</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.prompts.html">Prompts</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.prompts_cn.html">Prompts_cn</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.salesgptapi.html">SalesGPT API</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.stages.html">Stages</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.templates.html">Templates</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.tools.html">Tools</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.version.html">Version</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">SalesGPT</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">salesgpt package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/salesgpt/salesgpt.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="salesgpt-package">
<h1>salesgpt package<a class="headerlink" href="#salesgpt-package" title="Link to this heading"></a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.agents.html">salesgpt.agents module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT"><code class="docutils literal notranslate"><span class="pre">SalesGPT</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.acall"><code class="docutils literal notranslate"><span class="pre">SalesGPT.acall()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.acompletion_with_retry"><code class="docutils literal notranslate"><span class="pre">SalesGPT.acompletion_with_retry()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.adetermine_conversation_stage"><code class="docutils literal notranslate"><span class="pre">SalesGPT.adetermine_conversation_stage()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.astep"><code class="docutils literal notranslate"><span class="pre">SalesGPT.astep()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.company_business"><code class="docutils literal notranslate"><span class="pre">SalesGPT.company_business</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.company_name"><code class="docutils literal notranslate"><span class="pre">SalesGPT.company_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.company_values"><code class="docutils literal notranslate"><span class="pre">SalesGPT.company_values</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_history"><code class="docutils literal notranslate"><span class="pre">SalesGPT.conversation_history</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_purpose"><code class="docutils literal notranslate"><span class="pre">SalesGPT.conversation_purpose</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_stage_dict"><code class="docutils literal notranslate"><span class="pre">SalesGPT.conversation_stage_dict</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_stage_id"><code class="docutils literal notranslate"><span class="pre">SalesGPT.conversation_stage_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.conversation_type"><code class="docutils literal notranslate"><span class="pre">SalesGPT.conversation_type</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.current_conversation_stage"><code class="docutils literal notranslate"><span class="pre">SalesGPT.current_conversation_stage</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.determine_conversation_stage"><code class="docutils literal notranslate"><span class="pre">SalesGPT.determine_conversation_stage()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.from_llm"><code class="docutils literal notranslate"><span class="pre">SalesGPT.from_llm()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.human_step"><code class="docutils literal notranslate"><span class="pre">SalesGPT.human_step()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.input_keys"><code class="docutils literal notranslate"><span class="pre">SalesGPT.input_keys</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.knowledge_base"><code class="docutils literal notranslate"><span class="pre">SalesGPT.knowledge_base</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.model_name"><code class="docutils literal notranslate"><span class="pre">SalesGPT.model_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.output_keys"><code class="docutils literal notranslate"><span class="pre">SalesGPT.output_keys</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.retrieve_conversation_stage"><code class="docutils literal notranslate"><span class="pre">SalesGPT.retrieve_conversation_stage()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.sales_agent_executor"><code class="docutils literal notranslate"><span class="pre">SalesGPT.sales_agent_executor</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.sales_conversation_utterance_chain"><code class="docutils literal notranslate"><span class="pre">SalesGPT.sales_conversation_utterance_chain</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.salesperson_name"><code class="docutils literal notranslate"><span class="pre">SalesGPT.salesperson_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.salesperson_role"><code class="docutils literal notranslate"><span class="pre">SalesGPT.salesperson_role</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.seed_agent"><code class="docutils literal notranslate"><span class="pre">SalesGPT.seed_agent()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.stage_analyzer_chain"><code class="docutils literal notranslate"><span class="pre">SalesGPT.stage_analyzer_chain</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.step"><code class="docutils literal notranslate"><span class="pre">SalesGPT.step()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.agents.html#salesgpt.agents.SalesGPT.use_tools"><code class="docutils literal notranslate"><span class="pre">SalesGPT.use_tools</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.chains.html">salesgpt.chains module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.chains.html#salesgpt.chains.SalesConversationChain"><code class="docutils literal notranslate"><span class="pre">SalesConversationChain</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.chains.html#salesgpt.chains.SalesConversationChain.from_llm"><code class="docutils literal notranslate"><span class="pre">SalesConversationChain.from_llm()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.chains.html#salesgpt.chains.StageAnalyzerChain"><code class="docutils literal notranslate"><span class="pre">StageAnalyzerChain</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.chains.html#salesgpt.chains.StageAnalyzerChain.from_llm"><code class="docutils literal notranslate"><span class="pre">StageAnalyzerChain.from_llm()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.logger.html">salesgpt.logger module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.logger.html#salesgpt.logger.TimeFilter"><code class="docutils literal notranslate"><span class="pre">TimeFilter</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.logger.html#salesgpt.logger.TimeFilter.filter"><code class="docutils literal notranslate"><span class="pre">TimeFilter.filter()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.logger.html#salesgpt.logger.time_logger"><code class="docutils literal notranslate"><span class="pre">time_logger()</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.parsers.html">salesgpt.parsers module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.ai_prefix"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.ai_prefix</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.get_format_instructions"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.get_format_instructions()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.parse"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.parse()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.parsers.html#salesgpt.parsers.SalesConvoOutputParser.verbose"><code class="docutils literal notranslate"><span class="pre">SalesConvoOutputParser.verbose</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.prompts.html">salesgpt.prompts module</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.prompts_cn.html">salesgpt.prompts_cn module</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.salesgptapi.html">salesgpt.salesgptapi module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI"><code class="docutils literal notranslate"><span class="pre">SalesGPTAPI</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI.do"><code class="docutils literal notranslate"><span class="pre">SalesGPTAPI.do()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI.do_stream"><code class="docutils literal notranslate"><span class="pre">SalesGPTAPI.do_stream()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.salesgptapi.html#salesgpt.salesgptapi.SalesGPTAPI.initialize_agent"><code class="docutils literal notranslate"><span class="pre">SalesGPTAPI.initialize_agent()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.stages.html">salesgpt.stages module</a></li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.templates.html">salesgpt.templates module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools.format"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.format()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools.template"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.template</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="salesgpt.templates.html#salesgpt.templates.CustomPromptTemplateForTools.tools_getter"><code class="docutils literal notranslate"><span class="pre">CustomPromptTemplateForTools.tools_getter</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.tools.html">salesgpt.tools module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.completion_bedrock"><code class="docutils literal notranslate"><span class="pre">completion_bedrock()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.generate_calendly_invitation_link"><code class="docutils literal notranslate"><span class="pre">generate_calendly_invitation_link()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.generate_stripe_payment_link"><code class="docutils literal notranslate"><span class="pre">generate_stripe_payment_link()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.get_mail_body_subject_from_query"><code class="docutils literal notranslate"><span class="pre">get_mail_body_subject_from_query()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.get_product_id_from_query"><code class="docutils literal notranslate"><span class="pre">get_product_id_from_query()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.get_tools"><code class="docutils literal notranslate"><span class="pre">get_tools()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.send_email_tool"><code class="docutils literal notranslate"><span class="pre">send_email_tool()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.send_email_with_gmail"><code class="docutils literal notranslate"><span class="pre">send_email_with_gmail()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="salesgpt.tools.html#salesgpt.tools.setup_knowledge_base"><code class="docutils literal notranslate"><span class="pre">setup_knowledge_base()</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="salesgpt.version.html">salesgpt.version module</a></li>
</ul>
</div>
</section>
<section id="module-salesgpt">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-salesgpt" title="Link to this heading"></a></h2>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Filip-Michalsky.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>



    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>