Search.setIndex({"docnames": ["googleanalytics/README", "index", "salesgpt/modules", "salesgpt/salesgpt", "salesgpt/salesgpt.agents", "salesgpt/salesgpt.chains", "salesgpt/salesgpt.logger", "salesgpt/salesgpt.parsers", "salesgpt/salesgpt.prompts", "salesgpt/salesgpt.prompts_cn", "salesgpt/salesgpt.salesgptapi", "salesgpt/salesgpt.stages", "salesgpt/salesgpt.templates", "salesgpt/salesgpt.tools", "salesgpt/salesgpt.version"], "filenames": ["googleanalytics/README.rst", "index.rst", "salesgpt/modules.rst", "salesgpt/salesgpt.rst", "salesgpt/salesgpt.agents.rst", "salesgpt/salesgpt.chains.rst", "salesgpt/salesgpt.logger.rst", "salesgpt/salesgpt.parsers.rst", "salesgpt/salesgpt.prompts.rst", "salesgpt/salesgpt.prompts_cn.rst", "salesgpt/salesgpt.salesgptapi.rst", "salesgpt/salesgpt.stages.rst", "salesgpt/salesgpt.templates.rst", "salesgpt/salesgpt.tools.rst", "salesgpt/salesgpt.version.rst"], "titles": ["Google Analytics extension for Sphinx", "Welcome to SalesGPT\u2019s documentation!", "salesgpt", "salesgpt package", "salesgpt.agents module", "salesgpt.chains module", "salesgpt.logger module", "salesgpt.parsers module", "salesgpt.prompts module", "salesgpt.prompts_cn module", "salesgpt.salesgptapi module", "salesgpt.stages module", "salesgpt.templates module", "salesgpt.tools module", "salesgpt.version module"], "terms": {"index": 1, "modul": [0, 1, 2], "search": 1, "page": 1, "run": [], "run_api": [], "messagelist": [], "conversation_histori": [3, 4, 10], "human_sai": [], "model_config": [], "model_field": [], "chat_with_sales_ag": [], "say_hello": [], "packag": 2, "submodul": 2, "agent": [1, 2, 3, 5, 7], "acal": [3, 4], "acompletion_with_retri": [3, 4], "astep": [3, 4], "company_busi": [3, 4], "company_nam": [3, 4], "company_valu": [3, 4], "conversation_purpos": [3, 4], "conversation_stage_dict": [3, 4], "conversation_stage_id": [3, 4], "conversation_typ": [3, 4], "current_conversation_stag": [3, 4], "determine_conversation_stag": [3, 4], "from_llm": [3, 4, 5], "human_step": [3, 4], "input_kei": [3, 4], "knowledge_bas": [3, 4], "model_nam": [3, 4, 10, 13], "output_kei": [3, 4, 5], "retrieve_conversation_stag": [3, 4], "sales_agent_executor": [3, 4], "sales_conversation_utterance_chain": [3, 4], "salesperson_nam": [3, 4], "salesperson_rol": [3, 4], "seed_ag": [3, 4], "stage_analyzer_chain": [3, 4], "step": [3, 4], "use_tool": [3, 4, 10], "chain": [1, 2, 3, 4], "salesconversationchain": [1, 2, 3, 4, 5], "stageanalyzerchain": [1, 2, 3, 4, 5], "logger": [1, 2, 3], "timefilt": [1, 2, 3, 6], "filter": [3, 6], "time_logg": [1, 2, 3, 6], "parser": [1, 2, 3, 5], "salesconvooutputpars": [1, 2, 3, 7], "ai_prefix": [3, 7], "get_format_instruct": [3, 7], "pars": [3, 7], "verbos": [3, 4, 5, 7, 10], "prompt": [1, 2, 3, 4, 5, 12], "prompts_cn": [1, 2, 3], "salesgptapi": [1, 2, 3], "do": [3, 10], "stage": [1, 2, 3, 4, 5], "templat": [1, 2, 3], "customprompttemplatefortool": [1, 2, 3, 12], "format": [3, 4, 7, 12], "tools_gett": [3, 12], "tool": [1, 2, 3, 4], "get_tool": [1, 2, 3, 13], "setup_knowledge_bas": [1, 2, 3, 13], "version": [1, 2, 3], "content": 2, "class": [4, 5, 6, 7, 10, 12], "list": [0, 4, 5, 12], "str": [4, 5, 7, 10, 12, 13], "base": [4, 5, 6, 7, 10, 12, 13], "basemodel": [], "classvar": [], "configdict": [], "configur": [], "model": 4, "should": [4, 5, 6, 7, 13], "dictionari": 4, "conform": [], "pydant": [], "config": [], "dict": [4, 5, 12], "fieldinfo": 4, "annot": 4, "requir": 4, "true": [0, 4, 5, 6, 10], "metadata": [4, 5], "about": [], "field": [], "defin": [], "map": 12, "name": [4, 5, 6, 7, 12], "thi": [0, 1, 4, 5, 6], "replac": [], "__fields__": [], "from": 4, "v1": [], "async": [4, 10], "req": [], "memori": [4, 5], "basememori": [4, 5], "none": [4, 5, 7, 10, 12, 13], "callback": [4, 5], "basecallbackhandl": [4, 5], "basecallbackmanag": [4, 5], "callback_manag": [4, 5], "bool": [0, 4, 5, 7, 10], "tag": [4, 5], "ani": [1, 4, 5, 6, 12], "1": 4, "introduct": 4, "start": [4, 6], "convers": [4, 5], "introduc": 4, "yourself": 4, "your": [0, 4], "compani": 4, "Be": 4, "polit": 4, "respect": 4, "while": 4, "keep": 4, "tone": 4, "profession": 4, "greet": 4, "welcom": 4, "alwai": 4, "clarifi": 4, "reason": 4, "why": 4, "you": [0, 4, 5], "ar": [1, 4, 5, 6], "call": [4, 13], "nonetyp": 4, "agentexecutor": [], "retrievalqa": 4, "2": 4, "qualif": 4, "qualifi": 4, "prospect": 4, "confirm": 4, "thei": 4, "right": 4, "person": 4, "talk": 4, "regard": 4, "product": [4, 13], "servic": [0, 4], "ensur": 4, "have": [1, 4], "author": [0, 4], "make": 4, "purchas": 4, "decis": 4, "3": [4, 10, 13], "valu": 4, "proposit": 4, "briefli": 4, "explain": 4, "how": [4, 7], "can": [0, 4], "benefit": 4, "focu": 4, "uniqu": 4, "sell": [4, 5], "point": 4, "set": [0, 4], "apart": 4, "competitor": 4, "4": 4, "need": 4, "analysi": 4, "ask": 4, "open": 4, "end": [4, 6], "question": 4, "uncov": 4, "": 4, "pain": 4, "listen": 4, "carefulli": 4, "respons": [4, 5], "take": 4, "note": 4, "5": [4, 10, 13], "solut": 4, "present": 4, "address": 4, "6": 4, "object": [4, 10], "handl": 4, "mai": [4, 6], "prepar": 4, "provid": 4, "evid": 4, "testimoni": 4, "support": 4, "claim": 4, "7": 4, "close": 4, "sale": [4, 5], "propos": 4, "next": [4, 5], "could": 4, "demo": 4, "trial": 4, "meet": 4, "maker": 4, "summar": 4, "what": 4, "ha": 4, "been": 4, "discuss": 4, "reiter": 4, "8": 4, "It": [0, 4, 6], "time": [4, 6], "i": [0, 4, 6, 13], "noth": 4, "els": 4, "said": 4, "gpt": [4, 10, 13], "turbo": [4, 10, 13], "0613": 4, "fals": [4, 5, 6, 7], "ted": 4, "lasso": 4, "busi": 4, "develop": 4, "repres": 4, "sleep": 4, "haven": 4, "premium": 4, "mattress": 4, "custom": [4, 13], "most": 4, "comfort": 4, "experi": 4, "possibl": 4, "we": [1, 4, 13], "offer": 4, "rang": 4, "high": [4, 13], "qualiti": 4, "pillow": 4, "bed": 4, "accessori": 4, "design": 4, "our": 4, "mission": 4, "help": 4, "peopl": 4, "achiev": 4, "better": 4, "night": 4, "them": 4, "best": 4, "believ": 4, "essenti": 4, "overal": 4, "health": 4, "well": 4, "being": 4, "commit": 4, "optim": 4, "except": 4, "find": 4, "out": 4, "whether": 4, "look": 4, "via": 4, "bui": 4, "premier": 4, "control": 4, "arg": [4, 6, 12], "kwarg": [4, 12], "asynchron": 4, "execut": [4, 6], "input": [4, 12], "singl": [4, 13], "expect": [], "onli": [], "one": [0, 4], "param": [], "contain": [], "all": [], "specifi": 6, "return_only_output": [], "return": [6, 12], "output": [4, 7], "If": [4, 6], "new": [], "kei": 4, "gener": [0, 4, 5, 13], "both": [], "default": [0, 4], "us": [0, 4], "These": [], "addit": 4, "pass": [4, 12], "dure": [], "construct": [], "runtim": [], "propag": [], "other": [], "string": [0, 4, 12, 13], "option": [0, 4], "associ": [], "include_run_info": [], "includ": 13, "info": 6, "A": [4, 12], "llm": [4, 5, 7], "tenac": 4, "retri": 4, "complet": 4, "stream": 4, "manipul": 4, "chunk": 4, "downstream": 4, "applic": 4, "classmethod": [4, 5], "chatlitellm": [4, 5], "initi": 4, "human_input": [4, 10], "properti": 4, "baseprompttempl": 5, "runnabl": 5, "promptvalu": 5, "basemessag": 5, "text": [5, 7, 13], "output_pars": [5, 12], "basellmoutputpars": 5, "return_final_onli": 5, "llm_kwarg": 5, "llmchain": 5, "utter": [4, 5], "use_custom_prompt": 5, "custom_prompt": 5, "an": [4, 5, 13], "ai": [4, 5, 7], "me": 5, "pencil": 5, "get": [4, 5], "analyz": [4, 5], "which": [4, 5], "move": 5, "record": 6, "determin": [4, 6], "log": 6, "otherwis": [4, 6], "deem": 6, "appropri": 6, "modifi": 6, "place": 6, "func": 6, "decor": [4, 6], "function": [4, 6], "taken": 6, "agentoutputpars": 7, "instruct": 7, "agentact": 7, "agentfinish": 7, "action": 7, "finish": 7, "config_path": 10, "max_num_turn": 10, "int": 10, "10": [], "input_vari": 12, "input_typ": 12, "baseoutputpars": 12, "partial_vari": 12, "callabl": [6, 12], "stringprompttempl": 12, "argument": [4, 12], "exampl": [0, 10, 12], "variable1": 12, "foo": 12, "product_catalog": [10, 13], "assum": 13, "catalog": 13, "simpli": 13, "inform": 14, "red": [], "just": [0, 1], "begun": 1, "build": 1, "websit": 1, "still": 1, "activ": 1, "work": [1, 13], "suggest": 1, "improv": 1, "readabl": 1, "desir": 1, "pleas": 1, "contact": 1, "chemik": 1, "bit": 1, "github": [0, 1], "more": [], "add": [0, 4], "toctre": [], "maxdepth": [], "caption": [], "type": [], "variabl": [], "sequenc": 5, "tupl": [], "ellipsi": [], "extra": [], "deprec": [], "sinc": [], "0": [], "ainvok": [], "instead": [], "method": 4, "implement": [], "yet": [], "length": [], "arbitrari": [], "keyword": 4, "rais": [], "notimplementederror": [], "current": 4, "indic": 4, "librari": 4, "case": 4, "failur": 4, "creat": 4, "_create_retry_decor": 4, "appli": 4, "_completion_with_retri": 4, "actual": 4, "The": [4, 6], "languag": 4, "result": 4, "fail": 4, "after": [4, 6], "maximum": 4, "number": 4, "_acal": 4, "empti": 4, "flag": 4, "histori": 4, "join": 4, "each": 4, "entri": 4, "separ": 4, "newlin": 4, "charact": 4, "id": [0, 4], "also": 4, "print": 4, "retriev": 4, "correspond": 4, "final": 4, "process": 4, "human": 4, "append": 4, "ad": [0, 4], "user": 4, "begin": 4, "end_of_turn": 4, "overridden": 4, "subclass": 4, "extract": 4, "data": 4, "up": 4, "found": 4, "seed": 4, "clear": 4, "reset": 4, "_call": 4, "befor": 6, "calcul": 6, "level": [6, 13], "given": 4, "instanc": 4, "check": 4, "knowledg": 4, "executor": 4, "enabl": 4, "api": [1, 13], "doc": [], "raw": [], "html": 0, "div": [], "wy": [], "menu": [], "href": [], "http": 0, "vercel": [], "app": [], "btn": [], "primari": [], "do_stream": [3, 10], "initialize_ag": [3, 10], "customagentexecutor": 4, "20": 10, "completion_bedrock": [1, 2, 3, 13], "generate_calendly_invitation_link": [1, 2, 3, 13], "generate_stripe_payment_link": [1, 2, 3, 13], "get_mail_body_subject_from_queri": [1, 2, 3, 13], "get_product_id_from_queri": [1, 2, 3, 13], "send_email_tool": [1, 2, 3, 13], "send_email_with_gmail": [1, 2, 3, 13], "adetermine_conversation_stag": [3, 4], "overrid": 4, "temporarili": 4, "state": 4, "either": 4, "messag": [4, 13], "sample_product_catalog": 10, "txt": 10, "model_id": 13, "system_prompt": 13, "max_token": 13, "1000": 13, "anthrop": 13, "claud": 13, "queri": 13, "calendli": 13, "invit": 13, "link": 13, "stripe": 13, "payment": 13, "product_price_id_mapping_path": 13, "send": 13, "email": 13, "email_detail": 13, "env": 13, "gmail_mail": 13, "gmail_app_password": 13, "correctli": 13, "domen": 0, "ko\u017ear": 0, "dev": 0, "si": 0, "allow": 0, "track": 0, "file": 0, "web": 0, "googleanalyt": 0, "git": 0, "clone": 0, "com": 0, "chang": 0, "directori": 0, "cd": 0, "python": 0, "setup": 0, "py": 0, "sphinxcontrib": 0, "conf": 0, "For": 0, "now": 0, "googleanalytics_id": 0, "ua": 0, "site": 0, "123": 0, "googleanalytics_en": 0, "turn": 0, "off": 0}, "objects": {"": [[3, 0, 0, "-", "salesgpt"]], "salesgpt": [[4, 0, 0, "-", "agents"], [5, 0, 0, "-", "chains"], [6, 0, 0, "-", "logger"], [7, 0, 0, "-", "parsers"], [8, 0, 0, "-", "prompts"], [9, 0, 0, "-", "prompts_cn"], [10, 0, 0, "-", "salesgptapi"], [11, 0, 0, "-", "stages"], [12, 0, 0, "-", "templates"], [13, 0, 0, "-", "tools"], [14, 0, 0, "-", "version"]], "salesgpt.agents": [[4, 1, 1, "", "SalesGPT"]], "salesgpt.agents.SalesGPT": [[4, 2, 1, "", "acall"], [4, 2, 1, "", "acompletion_with_retry"], [4, 2, 1, "", "adetermine_conversation_stage"], [4, 2, 1, "", "astep"], [4, 3, 1, "", "company_business"], [4, 3, 1, "", "company_name"], [4, 3, 1, "", "company_values"], [4, 3, 1, "", "conversation_history"], [4, 3, 1, "", "conversation_purpose"], [4, 3, 1, "", "conversation_stage_dict"], [4, 3, 1, "", "conversation_stage_id"], [4, 3, 1, "", "conversation_type"], [4, 3, 1, "", "current_conversation_stage"], [4, 2, 1, "", "determine_conversation_stage"], [4, 2, 1, "", "from_llm"], [4, 2, 1, "", "human_step"], [4, 4, 1, "", "input_keys"], [4, 3, 1, "", "knowledge_base"], [4, 3, 1, "", "model_name"], [4, 4, 1, "", "output_keys"], [4, 2, 1, "", "retrieve_conversation_stage"], [4, 3, 1, "", "sales_agent_executor"], [4, 3, 1, "", "sales_conversation_utterance_chain"], [4, 3, 1, "", "salesperson_name"], [4, 3, 1, "", "salesperson_role"], [4, 2, 1, "", "seed_agent"], [4, 3, 1, "", "stage_analyzer_chain"], [4, 2, 1, "", "step"], [4, 3, 1, "", "use_tools"]], "salesgpt.chains": [[5, 1, 1, "", "SalesConversationChain"], [5, 1, 1, "", "StageAnalyzerChain"]], "salesgpt.chains.SalesConversationChain": [[5, 2, 1, "", "from_llm"]], "salesgpt.chains.StageAnalyzerChain": [[5, 2, 1, "", "from_llm"]], "salesgpt.logger": [[6, 1, 1, "", "TimeFilter"], [6, 5, 1, "", "time_logger"]], "salesgpt.logger.TimeFilter": [[6, 2, 1, "", "filter"]], "salesgpt.parsers": [[7, 1, 1, "", "SalesConvoOutputParser"]], "salesgpt.parsers.SalesConvoOutputParser": [[7, 3, 1, "", "ai_prefix"], [7, 2, 1, "", "get_format_instructions"], [7, 2, 1, "", "parse"], [7, 3, 1, "", "verbose"]], "salesgpt.salesgptapi": [[10, 1, 1, "", "SalesGPTAPI"]], "salesgpt.salesgptapi.SalesGPTAPI": [[10, 2, 1, "", "do"], [10, 2, 1, "", "do_stream"], [10, 2, 1, "", "initialize_agent"]], "salesgpt.templates": [[12, 1, 1, "", "CustomPromptTemplateForTools"]], "salesgpt.templates.CustomPromptTemplateForTools": [[12, 2, 1, "", "format"], [12, 3, 1, "", "template"], [12, 3, 1, "", "tools_getter"]], "salesgpt.tools": [[13, 5, 1, "", "completion_bedrock"], [13, 5, 1, "", "generate_calendly_invitation_link"], [13, 5, 1, "", "generate_stripe_payment_link"], [13, 5, 1, "", "get_mail_body_subject_from_query"], [13, 5, 1, "", "get_product_id_from_query"], [13, 5, 1, "", "get_tools"], [13, 5, 1, "", "send_email_tool"], [13, 5, 1, "", "send_email_with_gmail"], [13, 5, 1, "", "setup_knowledge_base"]]}, "objtypes": {"0": "py:module", "1": "py:class", "2": "py:method", "3": "py:attribute", "4": "py:property", "5": "py:function"}, "objnames": {"0": ["py", "module", "Python module"], "1": ["py", "class", "Python class"], "2": ["py", "method", "Python method"], "3": ["py", "attribute", "Python attribute"], "4": ["py", "property", "Python property"], "5": ["py", "function", "Python function"]}, "titleterms": {"welcom": 1, "salesgpt": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "": 1, "document": 1, "indic": 1, "tabl": 1, "run": [], "modul": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "run_api": [], "packag": 3, "submodul": 3, "agent": 4, "chain": 5, "logger": 6, "parser": 7, "prompt": 8, "prompts_cn": 9, "salesgptapi": 10, "stage": 11, "templat": 12, "tool": 13, "version": 14, "content": [1, 3], "note": [], "paramet": 4, "rais": 4, "return": 4, "googl": 0, "analyt": 0, "extens": 0, "sphinx": 0, "about": 0, "instal": 0, "from": 0, "contrib": 0, "checkout": 0, "enabl": 0, "configur": 0}, "envversion": {"sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx": 60}, "alltitles": {"Google Analytics extension for Sphinx": [[0, "google-analytics-extension-for-sphinx"]], "About": [[0, "about"]], "Installing from sphinx-contrib checkout": [[0, "installing-from-sphinx-contrib-checkout"]], "Enabling the extension in Sphinx": [[0, "enabling-the-extension-in-sphinx"]], "Configuration": [[0, "configuration"]], "Welcome to SalesGPT\u2019s documentation!": [[1, "welcome-to-salesgpt-s-documentation"]], "Contents:": [[1, null], [3, null]], "Indices and tables": [[1, "indices-and-tables"]], "salesgpt": [[2, "salesgpt"]], "salesgpt package": [[3, "salesgpt-package"]], "Submodules": [[3, "submodules"]], "Module contents": [[3, "module-salesgpt"]], "salesgpt.agents module": [[4, "module-salesgpt.agents"]], "Parameters": [[4, "parameters"], [4, "id1"], [4, "id3"]], "Returns": [[4, "returns"], [4, "id2"], [4, "id4"]], "Raises": [[4, "raises"]], "salesgpt.chains module": [[5, "module-salesgpt.chains"]], "salesgpt.logger module": [[6, "module-salesgpt.logger"]], "salesgpt.parsers module": [[7, "module-salesgpt.parsers"]], "salesgpt.prompts module": [[8, "module-salesgpt.prompts"]], "salesgpt.prompts_cn module": [[9, "module-salesgpt.prompts_cn"]], "salesgpt.salesgptapi module": [[10, "module-salesgpt.salesgptapi"]], "salesgpt.stages module": [[11, "module-salesgpt.stages"]], "salesgpt.templates module": [[12, "module-salesgpt.templates"]], "salesgpt.tools module": [[13, "module-salesgpt.tools"]], "salesgpt.version module": [[14, "module-salesgpt.version"]]}, "indexentries": {"module": [[3, "module-salesgpt"], [4, "module-salesgpt.agents"], [5, "module-salesgpt.chains"], [6, "module-salesgpt.logger"], [7, "module-salesgpt.parsers"], [8, "module-salesgpt.prompts"], [9, "module-salesgpt.prompts_cn"], [10, "module-salesgpt.salesgptapi"], [11, "module-salesgpt.stages"], [12, "module-salesgpt.templates"], [13, "module-salesgpt.tools"], [14, "module-salesgpt.version"]], "salesgpt": [[3, "module-salesgpt"]], "salesgpt (class in salesgpt.agents)": [[4, "salesgpt.agents.SalesGPT"]], "acall() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.acall"]], "acompletion_with_retry() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.acompletion_with_retry"]], "adetermine_conversation_stage() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.adetermine_conversation_stage"]], "astep() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.astep"]], "company_business (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.company_business"]], "company_name (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.company_name"]], "company_values (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.company_values"]], "conversation_history (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.conversation_history"]], "conversation_purpose (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.conversation_purpose"]], "conversation_stage_dict (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.conversation_stage_dict"]], "conversation_stage_id (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.conversation_stage_id"]], "conversation_type (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.conversation_type"]], "current_conversation_stage (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.current_conversation_stage"]], "determine_conversation_stage() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.determine_conversation_stage"]], "from_llm() (salesgpt.agents.salesgpt class method)": [[4, "salesgpt.agents.SalesGPT.from_llm"]], "human_step() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.human_step"]], "input_keys (salesgpt.agents.salesgpt property)": [[4, "salesgpt.agents.SalesGPT.input_keys"]], "knowledge_base (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.knowledge_base"]], "model_name (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.model_name"]], "output_keys (salesgpt.agents.salesgpt property)": [[4, "salesgpt.agents.SalesGPT.output_keys"]], "retrieve_conversation_stage() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.retrieve_conversation_stage"]], "sales_agent_executor (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.sales_agent_executor"]], "sales_conversation_utterance_chain (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.sales_conversation_utterance_chain"]], "salesgpt.agents": [[4, "module-salesgpt.agents"]], "salesperson_name (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.salesperson_name"]], "salesperson_role (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.salesperson_role"]], "seed_agent() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.seed_agent"]], "stage_analyzer_chain (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.stage_analyzer_chain"]], "step() (salesgpt.agents.salesgpt method)": [[4, "salesgpt.agents.SalesGPT.step"]], "use_tools (salesgpt.agents.salesgpt attribute)": [[4, "salesgpt.agents.SalesGPT.use_tools"]], "salesconversationchain (class in salesgpt.chains)": [[5, "salesgpt.chains.SalesConversationChain"]], "stageanalyzerchain (class in salesgpt.chains)": [[5, "salesgpt.chains.StageAnalyzerChain"]], "from_llm() (salesgpt.chains.salesconversationchain class method)": [[5, "salesgpt.chains.SalesConversationChain.from_llm"]], "from_llm() (salesgpt.chains.stageanalyzerchain class method)": [[5, "salesgpt.chains.StageAnalyzerChain.from_llm"]], "salesgpt.chains": [[5, "module-salesgpt.chains"]], "timefilter (class in salesgpt.logger)": [[6, "salesgpt.logger.TimeFilter"]], "filter() (salesgpt.logger.timefilter method)": [[6, "salesgpt.logger.TimeFilter.filter"]], "salesgpt.logger": [[6, "module-salesgpt.logger"]], "time_logger() (in module salesgpt.logger)": [[6, "salesgpt.logger.time_logger"]], "salesconvooutputparser (class in salesgpt.parsers)": [[7, "salesgpt.parsers.SalesConvoOutputParser"]], "ai_prefix (salesgpt.parsers.salesconvooutputparser attribute)": [[7, "salesgpt.parsers.SalesConvoOutputParser.ai_prefix"]], "get_format_instructions() (salesgpt.parsers.salesconvooutputparser method)": [[7, "salesgpt.parsers.SalesConvoOutputParser.get_format_instructions"]], "parse() (salesgpt.parsers.salesconvooutputparser method)": [[7, "salesgpt.parsers.SalesConvoOutputParser.parse"]], "salesgpt.parsers": [[7, "module-salesgpt.parsers"]], "verbose (salesgpt.parsers.salesconvooutputparser attribute)": [[7, "salesgpt.parsers.SalesConvoOutputParser.verbose"]], "salesgpt.prompts": [[8, "module-salesgpt.prompts"]], "salesgpt.prompts_cn": [[9, "module-salesgpt.prompts_cn"]], "salesgptapi (class in salesgpt.salesgptapi)": [[10, "salesgpt.salesgptapi.SalesGPTAPI"]], "do() (salesgpt.salesgptapi.salesgptapi method)": [[10, "salesgpt.salesgptapi.SalesGPTAPI.do"]], "do_stream() (salesgpt.salesgptapi.salesgptapi method)": [[10, "salesgpt.salesgptapi.SalesGPTAPI.do_stream"]], "initialize_agent() (salesgpt.salesgptapi.salesgptapi method)": [[10, "salesgpt.salesgptapi.SalesGPTAPI.initialize_agent"]], "salesgpt.salesgptapi": [[10, "module-salesgpt.salesgptapi"]], "salesgpt.stages": [[11, "module-salesgpt.stages"]], "customprompttemplatefortools (class in salesgpt.templates)": [[12, "salesgpt.templates.CustomPromptTemplateForTools"]], "format() (salesgpt.templates.customprompttemplatefortools method)": [[12, "salesgpt.templates.CustomPromptTemplateForTools.format"]], "salesgpt.templates": [[12, "module-salesgpt.templates"]], "template (salesgpt.templates.customprompttemplatefortools attribute)": [[12, "salesgpt.templates.CustomPromptTemplateForTools.template"]], "tools_getter (salesgpt.templates.customprompttemplatefortools attribute)": [[12, "salesgpt.templates.CustomPromptTemplateForTools.tools_getter"]], "completion_bedrock() (in module salesgpt.tools)": [[13, "salesgpt.tools.completion_bedrock"]], "generate_calendly_invitation_link() (in module salesgpt.tools)": [[13, "salesgpt.tools.generate_calendly_invitation_link"]], "generate_stripe_payment_link() (in module salesgpt.tools)": [[13, "salesgpt.tools.generate_stripe_payment_link"]], "get_mail_body_subject_from_query() (in module salesgpt.tools)": [[13, "salesgpt.tools.get_mail_body_subject_from_query"]], "get_product_id_from_query() (in module salesgpt.tools)": [[13, "salesgpt.tools.get_product_id_from_query"]], "get_tools() (in module salesgpt.tools)": [[13, "salesgpt.tools.get_tools"]], "salesgpt.tools": [[13, "module-salesgpt.tools"]], "send_email_tool() (in module salesgpt.tools)": [[13, "salesgpt.tools.send_email_tool"]], "send_email_with_gmail() (in module salesgpt.tools)": [[13, "salesgpt.tools.send_email_with_gmail"]], "setup_knowledge_base() (in module salesgpt.tools)": [[13, "salesgpt.tools.setup_knowledge_base"]], "salesgpt.version": [[14, "module-salesgpt.version"]]}})