---
sidebar_position: 1

---

# SalesGPT as a Mattress Salesman

SalesGPT can be configured to work as a mattress salesman by setting up a custom prompt and conversation flow. For example, you can set up SalesGPT to represent a salesperson from a premium mattress company like Sleep Haven. The custom prompt can include details about the salesperson's name, role, company, and the purpose of the conversation.

Here is an example setup:

- **Salesperson Name**: <PERSON>
- **Salesperson Role**: Business Development Representative
- **Company Name**: Sleep Haven
- **Company Business**: Sleep Haven is a premium mattress company that provides customers with the most comfortable and supportive sleeping experience possible. We offer a range of high-quality mattresses, pillows, and bedding accessories that are designed to meet the unique needs of our customers.
- **Company Values**: Our mission at Sleep Haven is to help people achieve a better night's sleep by providing them with the best possible sleep solutions. We believe that quality sleep is essential to overall health and well-being, and we are committed to helping our customers achieve optimal sleep by offering exceptional products and customer service.
- **Conversation Purpose**: Find out whether they are looking to achieve better sleep via buying a premier mattress.
- **Conversation Type**: Call

The conversation flow can be structured into stages such as Introduction, Qualification, Value Proposition, Needs Analysis, Solution Presentation, Objection Handling, Close, and End Conversation. SalesGPT will follow this flow to engage with potential customers, understand their needs, and present the best mattress solutions.

![Correct Functioning](/img/correct.png)

