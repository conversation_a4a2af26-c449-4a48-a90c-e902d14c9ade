---
sidebar_position: 3

---

# Roadmap

1) Documenting the Repo better - Done
2) Documenting the API - Done
3) Code Documentation
4) Refactor
5) Improve reliability of the parser [issue here](https://github.com/filip-michalsky/SalesGPT/issues/26) and [here](https://github.com/filip-michalsky/SalesGPT/issues/25)
7) Improve Deployment Instructions
8) Calling Functionality - sample code
9) Enterprise-Grade Security - integration with [PromptArmor](https://promptarmor.com/) to protect your AI Sales Agents against security vulnerabilities
10) LLM evaluations 
11) Resolve tickets and PRs (ongoing)
12) Add example implementation of OpenAI functions agent[issue here](https://github.com/filip-michalsky/SalesGPT/issues/17)
13) Add support for multiple tools [issue here](https://github.com/filip-michalsky/SalesGPT/issues/10)
14) Add an agent controller for when stages need to be traversed linearly without skips [issue here](https://github.com/filip-michalsky/SalesGPT/issues/19)
15) Add `tool_getter` to choose a tool based on vector distance to the tasks needed to be done
16) What tools should the agent have? (e.g., the ability to search the internet)
17) Add the ability of Sales Agent to interact with AI plugins on your website (.well-known/ai-plugin.json)
18) More SalesGPT examples
