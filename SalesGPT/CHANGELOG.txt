Updates to the SalesGPT project: Building the world's best AI Sales Agents and virtual workers.

May 8, 2024
---------------
Version 0.1.3
- SalesGPT has analytics tracking built into documentation for improved understanding of important features and update necessities
- Added option for local frontend startup with ENVIRONMENT variable

March 22, 2024
---------------
Version 0.1.2
- SalesGPT can now sell by negotiation with users and then sending them a stripe link to pay.
- Added SalesGPT frontend for testing

January 29, 2024
---------------
Version 0.1.1
- updated compatibility to match LangChain 0.1 including examples
- WIP of the website and API documentation.

December 10, 2023
---------------
Version 0.1.0
- Migrated dependency management to Poetry.
- added makefile for easy installation and testing.
- expanded compatibility for python 3.8, 3.9, 3.10, and 3.11
- detailed installation + contribution instructions

November 25, 2023
---------------
Version 0.0.9
- Fixed dependency on openai 1.0.0 SDK which works with our streaming module.

October 4, 2023
---------------
Version 0.0.8
- Improved streaming endpoint, now accessible with simple `stream=True` kwarg.
- Fixed dependency issues with `pydantic` version.
- Removed the duplicate calling of `model_name`.
- Improved docstrings.
-  Split `requirements.txt` into dev and production requirements.

September 8, 2023
---------------
Version 0.0.7
- SalesGPT is now compatible with LiteLLM - choose any closed/open-sourced LLM 
to work with SalesGPT.

August 23, 2023
---------------
Version 0.0.6
- "use_tools" as a keyword argument (kwarg) is treated as STRING i.e., agent_config["use_tools"]="True"
to be consistent with JSON payloads.
- add asynchronous streaming generator.

August 18, 2023
---------------
- SalesGPT API - added chat as a service and Chinese language SalesGPT prompt, huge thanks to @janewu77!

July 29, 2023
-------------
Version 0.0.5
- Minor update, remove unneccesary verbosity from `SalesGPT.from_llm` method.

July 15, 2023
-------------

Version 0.0.4
- Added tools to SalesGPT, creating a true agent.
- Added product knowledge base as an example tool

-------------