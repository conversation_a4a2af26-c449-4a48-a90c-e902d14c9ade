name: tests

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  check_skip:
    runs-on: ubuntu-latest
    if: "! contains(github.event.head_commit.message, '[ci skip]')"
    steps:
      - run: echo "${{ github.event.head_commit.message }}"

  test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11"] # Testing across multiple Python versions
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          architecture: x64
          
      - name: Check OS
        run: cat /etc/os-release

      - name: Install/upgrade Python setup tools
        run: python3 -m pip install --upgrade pip setuptools wheel

      - name: Install additional system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y --no-install-recommends git make gcc


      - name: Install Poetry
        run: |
          pip install poetry

      - name: Configure Poetry
        run: |
          poetry config virtualenvs.create false

      - name: Install dependencies using Poetry
        run: |
          poetry install


      - name: Run Unit Tests
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION_NAME: ${{ secrets.AWS_REGION_NAME }}
          AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}
        run: |
          export OPENAI_API_KEY=$OPENAI_API_KEY
          export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
          export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
          export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION
          export AWS_REGION_NAME=$AWS_REGION_NAME
          make test # Executing tests with Poetry

