.bubble-tail {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 10px 0;
  border-color: #ffffff transparent transparent;
  position: relative;
  top: -10px;
  left: 30px;
}

.bubble-tail-left {
  transform: rotate(45deg);
}

.bubble-tail-right {
  transform: rotate(-45deg);
}

.chat-messages, .thinking-process {
  overflow-y: auto !important;
  scroll-behavior: smooth;
}

/* Hide scrollbar for WebKit browsers */
.chat-messages::-webkit-scrollbar, .thinking-process::-webkit-scrollbar {
  display: none !important;
}

/* Hide scrollbar for Firefox */
.chat-messages, .thinking-process {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}
.typingBubble {
  display: inline-block;
  margin-left: 8px;
}

.typingDot {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 4px;
  border-radius: 50%;
  background-color: black; /* Change color to ensure visibility */
  animation: typing 1.4s infinite both;
}

.typingDot:nth-child(1) {
  animation-delay: 0s;
}

.typingDot:nth-child(2) {
  animation-delay: 0.2s;
}

.typingDot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.hideScrollbar {
  scrollbar-width: none; 
  -ms-overflow-style: none;  
}

.hideScrollbar::-webkit-scrollbar {
  display: none;
}