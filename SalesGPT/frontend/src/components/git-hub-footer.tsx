/**
 * This code was generated by v0 by Vercel.
 * @see https://v0.dev/t/WhR4kNrAN2M
 */
import Link from "next/link"
import React from 'react'; // Ensure React is imported if not already

export function GitHubFooter() {
  return (
    <footer className="w-full h-20 flex items-center justify-center bg-white border-t border-animated">
      <a
        className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-900 bg-white border-2 border-transparent rounded-md hover:border-animated transition-all"
        href="https://github.com/filip-michalsky/SalesGPT"
        target="_blank"
        rel="noopener noreferrer"
      >
        <GithubIcon className="h-5 w-5 mr-2" />
        Learn More
      </a>
    </footer>
  )
}



function GithubIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
      <path d="M9 18c-4.51 2-5-2-7-2" />
    </svg>
  )
}
