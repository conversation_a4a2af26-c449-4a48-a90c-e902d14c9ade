{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "./start-dev.sh", "vercel-dev": "nodemon --watch pages --watch components --exec \"next dev\"", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.340.0", "next": "^14.1.0", "posthog-node": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "rehype-raw": "^7.0.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "nodemon": "^3.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}