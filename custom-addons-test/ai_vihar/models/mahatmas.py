from odoo import models, fields

class <PERSON><PERSON><PERSON>(models.Model):
    _name = 'ai.mahatma'
    _description = 'Mahatma'

    code = fields.Char(string='Code', required=True)
    name = fields.Text(string='Name (All Languages)', required=True)
    name_en = fields.Char(string='Name in English')
    name_gu = fields.Char(string='Name in Gujarati')
    name_hi = fields.Char(string='Name in Hindi')
    samuday_id = fields.Many2one('ai.samuday', string='Samuday')
    contact_person = fields.Char(string='Contact Person')
    contact_number = fields.Char(string='Contact Number')
