from odoo import models, fields

class Salutation(models.Model):
    _name = 'ai.salutation'
    _description = 'Salutation'

    name = fields.Text(string='Name (All Languages)', required=True)
    name_en = fields.Char(string='Name in English')
    name_gu = fields.Char(string='Name in Gujarati')
    name_hi = fields.Char(string='Name in Hindi')
    status = fields.Selection([('active', 'Active'), ('inactive', 'Inactive')], string='Status', default='active')
