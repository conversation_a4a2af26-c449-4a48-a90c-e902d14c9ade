from odoo import models, fields

class Village(models.Model):
    _name = 'ai.village'
    _description = 'Village'

    name = fields.Text(string='Name (All Languages)', required=True)
    name_en = fields.Char(string='Name in English')
    name_gu = fields.Char(string='Name in Gujarati')
    name_hi = fields.Char(string='Name in Hindi')
    district_id = fields.Many2one('ai.district', string='District')
    status = fields.Selection([('active', 'Active'), ('inactive', 'Inactive')], string='Status', default='active')
