from odoo import models, fields

class District(models.Model):
    _name = 'ai.district'
    _description = 'District'

    name = fields.Text(string='Name (All Languages)', required=True)
    name_en = fields.Char(string='Name in English')
    name_gu = fields.Char(string='Name in Gujarati')
    name_hi = fields.Char(string='Name in Hindi')
    state_id = fields.Many2one('ai.state', string='State')
    status = fields.Selection([('active', 'Active'), ('inactive', 'Inactive')], string='Status', default='active')
