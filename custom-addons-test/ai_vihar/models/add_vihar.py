from odoo import models, fields, api
from odoo.exceptions import UserError

class AddVihar(models.Model):
    _name = 'ai.add.vihar'
    _description = 'Add Vihar'

    code = fields.Many2one('ai.mahatma', string='Code')
    letter_no = fields.Char(string='Letter No')
    ms_name_en = fields.Char(string='M. S. Name in English')
    ms_name_gu = fields.Char(string='M. S. Name in Gujarati')
    ms_name_hi = fields.Char(string='M. S. Name in Hindi')
    samuday_id = fields.Many2one('ai.samuday', string='Samuday')
    sevak_name = fields.Char(string='Sevak Name')
    thana_no = fields.Char(string='Thana No')
    contact_number = fields.Char(string='Contact Number')
    district_incharge_ids = fields.Many2many('res.users', string='District Incharge')
    vihar_route_ids = fields.Many2many('ai.vihar.route', string='Vihar Routes')
    salutation_ids = fields.Many2many('ai.salutation', string='Salutations')

    def send_email(self, language):
        template_id = self.env.ref('ai_vihar.email_template_add_vihar_' + language)
        if template_id:
            template_id.send_mail(self.id, force_send=True)
        else:
            raise UserError("Email template not found.")

    def action_send_email_english(self):
        self.send_email('en')

    def action_send_email_gujarati(self):
        self.send_email('gu')

    def action_send_email_hindi(self):
        self.send_email('hi')
