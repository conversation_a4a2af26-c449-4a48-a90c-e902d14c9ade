<odoo>
    <record id="email_template_add_vihar" model="mail.template">
        <field name="name">Add Vihar Email Template (English)</field>
        <field name="model_id" ref="model_ai_add_vihar"/>
        <field name="subject">Vihar Route Notification</field>
        <field name="email_from">${object.sevak_name}@example.com</field>
        <field name="email_to">${object.district_incharge_ids.email}</field>
        <field name="body_html">
            <![CDATA[
            <div>
                <div class="vihar-preview" id="print-block-data">
                    <div class="vihar-top-header">
                        <div class="ant-row ant-row-space-between ant-row-bottom css-1ja3ll3">
                            <div class="ant-col vps-detail-section ant-col-xs-24 ant-col-sm-12 css-1ja3ll3">
                                <div class="vps-logo-wrapper">
                                    <div class="ant-image css-1ja3ll3" style="height: 60px;">
                                        <img alt="" class="ant-image-img css-1ja3ll3" src="/static/media/logo.1eb54803c0c7519215e3.png" height="60px" style="height: 60px;">
                                    </div>
                                    <div class="name-title ml3">Vihar Police Suraksha</div>
                                </div>
                                <div class="address-section mt1 ml15">A7 - 301, Aksharjyot App.,<br>Near Bhulka Bhavan School, <br>Adajan, Surat - 395009</div>
                            </div>
                            <div class="ant-col letter-wrapper mt4 ant-col-xs-24 ant-col-sm-10 css-1ja3ll3">
                                <div class="letter-no">Letter number : ${object.letter_no}</div>
                                <div class="mahatma-name">(${object.code}) ${object.ms_name_en}</div>
                                <div class="date-no">Date : ${object.create_date.strftime('%d/%m/%Y')}</div>
                            </div>
                        </div>
                    </div>
                    <div class="letter-content my6">
                        <div class="salutations">
                            <div class="salutation-name">Respected C.P. Surat City,</div>
                        </div>
                        <div class="letter-text mt4">
                            <div class="mb3">JAI HIND, JAI BHARAT.</div>
                            We kindly inform you that our Jain Saint Shree <strong>${object.ms_name_en}</strong> and other <strong>9</strong> saints along with helping person is doing padyatra from <strong>${object.from_location_id.name}</strong> to <strong>${object.to_location_id.name}</strong>. We are indebted to the government and the police system who support us, and make our Jain saint feel safe during their padyatra and take care of them as the invaluable assets of the society. We request you to give police protection during this padyatra.
                        </div>
                        <div class="sevak-details mt4">
                            <div class="sevak-name">Sevak Name: <strong>${object.sevak_name}</strong></div>
                            <div class="contact-number">Sevak Contact: <div class="contact-list ml2">${object.contact_number}</div></div>
                        </div>
                        <div class="vihar-route mt4">
                            <div class="ant-table-wrapper shift-table leaderboard-table css-1ja3ll3">
                                <table style="table-layout: auto;">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>From</th>
                                            <th>To</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>${object.vihar_route_ids.vihar_date_time}</td>
                                            <td>${object.from_location_id.name}</td>
                                            <td>${object.to_location_id.name}</td>
                                            <td>${object.vihar_route_ids.time}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="end-note mt4">
                            <div class="head">Written By,</div>
                            <div class="name">Team VPS</div>
                        </div>
                    </div>
                </div>
            </div>
            ]]>
        </field>
    </record>

    <record id="email_template_add_vihar_gujarati" model="mail.template">
        <field name="name">Add Vihar Email Template (Gujarati)</field>
        <field name="model_id" ref="model_ai_add_vihar"/>
        <field name="subject">Vihar Route Notification (Gujarati)</field>
        <field name="email_from">${object.sevak_name}@example.com</field>
        <field name="email_to">${object.district_incharge_ids.email}</field>
        <field name="body_html">
            <![CDATA[
            <div>
                <div class="vihar-preview" id="print-block-data">
                    <div class="vihar-top-header">
                        <div class="ant-row ant-row-space-between ant-row-bottom css-1ja3ll3">
                            <div class="ant-col vps-detail-section ant-col-xs-24 ant-col-sm-12 css-1ja3ll3">
                                <div class="vps-logo-wrapper">
                                    <div class="ant-image css-1ja3ll3" style="height: 60px;">
                                        <img alt="" class="ant-image-img css-1ja3ll3" src="/static/media/logo.1eb54803c0c7519215e3.png" height="60px" style="height: 60px;">
                                    </div>
                                    <div class="name-title ml3">Vihar Police Suraksha</div>
                                </div>
                                <div class="address-section mt1 ml15">A7 - 301, Aksharjyot App.,<br>Near Bhulka Bhavan School, <br>Adajan, Surat - 395009</div>
                            </div>
                            <div class="ant-col letter-wrapper mt4 ant-col-xs-24 ant-col-sm-10 css-1ja3ll3">
                                <div class="letter-no">લેટર સંખ્યા : ${object.letter_no}</div>
                                <div class="mahatma-name">(${object.code}) ${object.ms_name_gu}</div>
                                <div class="date-no">તારીખ : ${object.create_date.strftime('%d/%m/%Y')}</div>
                            </div>
                        </div>
                    </div>
                    <div class="letter-content my6">
                        <div class="salutations">
                            <div class="salutation-name">માનનીય શ્રી  સી.પી. સુરત સિટી,</div>
                        </div>
                        <div class="letter-text mt4">
                            <div class="mb3">જય ભારત.</div>
                            અમે આપને જણાવવા માગીએ છીએ કે અમારા જૈન <strong>${object.ms_name_gu}</strong> અને અન્ય <strong>9</strong> સંતો સાથે સહાયક <strong>${object.from_location_id.name}</strong> થી <strong>${object.to_location_id.name}</strong> તરફની પદયાત્રા કરી રહ્યા છે. આ પદયાત્રા દરમિયાન આપની સુરક્ષા આપવા વિનંતી છે.
                        </div>
                        <div class="sevak-details mt4">
                            <div class="sevak-name">Sevak Name: <strong>${object.sevak_name}</strong></div>
                            <div class="contact-number">Sevak Contact: <div class="contact-list ml2">${object.contact_number}</div></div>
                        </div>
                        <div class="vihar-route mt4">
                            <div class="ant-table-wrapper shift-table leaderboard-table css-1ja3ll3">
                                <table style="table-layout: auto;">
                                    <thead>
                                        <tr>
                                            <th>તારીખ</th>
                                            <th>ક્યાં થી</th>
                                            <th>ક્યાં સુધી</th>
                                            <th>સમય</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>${object.vihar_route_ids.vihar_date_time}</td>
                                            <td>${object.from_location_id.name}</td>
                                            <td>${object.to_location_id.name}</td>
                                            <td>${object.vihar_route_ids.time}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="end-note mt4">
                            <div class="head">Written By,</div>
                            <div class="name">Team VPS</div>
                        </div>
                    </div>
                </div>
            </div>
            ]]>
        </field>
    </record>

    <record id="email_template_add_vihar_hindi" model="mail.template">
        <field name="name">Add Vihar Email Template (Hindi)</field>
        <field name="model_id" ref="model_ai_add_vihar"/>
        <field name="subject">Vihar Route Notification (Hindi)</field>
        <field name="email_from">${object.sevak_name}@example.com</field>
        <field name="email_to">${object.district_incharge_ids.email}</field>
        <field name="body_html">
            <![CDATA[
            <div>
                <div class="vihar-preview" id="print-block-data">
                    <div class="vihar-top-header">
                        <div class="ant-row ant-row-space-between ant-row-bottom css-1ja3ll3">
                            <div class="ant-col vps-detail-section ant-col-xs-24 ant-col-sm-12 css-1ja3ll3">
                                <div class="vps-logo-wrapper">
                                    <div class="ant-image css-1ja3ll3" style="height: 60px;">
                                        <img alt="" class="ant-image-img css-1ja3ll3" src="/static/media/logo.1eb54803c0c7519215e3.png" height="60px" style="height: 60px;">
                                    </div>
                                    <div class="name-title ml3">Vihar Police Suraksha</div>
                                </div>
                                <div class="address-section mt1 ml15">A7 - 301, Aksharjyot App.,<br>Near Bhulka Bhavan School, <br>Adajan, Surat - 395009</div>
                            </div>
                            <div class="ant-col letter-wrapper mt4 ant-col-xs-24 ant-col-sm-10 css-1ja3ll3">
                                <div class="letter-no">लेटर संख्या : ${object.letter_no}</div>
                                <div class="mahatma-name">(${object.code}) ${object.ms_name_hi}</div>
                                <div class="date-no">तारीख : ${object.create_date.strftime('%d/%m/%Y')}</div>
                            </div>
                        </div>
                    </div>
                    <div class="letter-content my6">
                        <div class="salutations">
                            <div class="salutation-name">माननीय श्री  सी.पी. सूरत सिटी,</div>
                        </div>
                        <div class="letter-text mt4">
                            <div class="mb3">नमस्कार! जय हिंद! जय भारत!</div>
                            हमारे जैन <strong>${object.ms_name_hi}</strong> और अन्य <strong>9</strong> संतों के साथ सहायक <strong>${object.from_location_id.name}</strong> से <strong>${object.to_location_id.name}</strong> तक की विहार (पैदल) यात्रा है। इस विहार (पैदल) यात्रा के दौरान पुलिस सुरक्षा देने का कस्ट करें।
                        </div>
                        <div class="sevak-details mt4">
                            <div class="sevak-name">Sevak Name: <strong>${object.sevak_name}</strong></div>
                            <div class="contact-number">Sevak Contact: <div class="contact-list ml2">${object.contact_number}</div></div>
                        </div>
                        <div class="vihar-route mt4">
                            <div class="ant-table-wrapper shift-table leaderboard-table css-1ja3ll3">
                                <table style="table-layout: auto;">
                                    <thead>
                                        <tr>
                                            <th>तारीख</th>
                                            <th>कहाँ से</th>
                                            <th>कहाँ तक</th>
                                            <th>समय</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>${object.vihar_route_ids.vihar_date_time}</td>
                                            <td>${object.from_location_id.name}</td>
                                            <td>${object.to_location_id.name}</td>
                                            <td>${object.vihar_route_ids.time}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="end-note mt4">
                            <div class="head">Written By,</div>
                            <div class="name">Team VPS</div>
                        </div>
                    </div>
                </div>
            </div>
            ]]>
        </field>
    </record>
</odoo>
