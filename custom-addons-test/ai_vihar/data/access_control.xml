<odoo>
    <record id="group_user" model="res.groups">
        <field name="name">User</field>
    </record>

    <record id="group_admin" model="res.groups">
        <field name="name">Admin</field>
    </record>

    <record id="group_district_incharge" model="res.groups">
        <field name="name">District Incharge</field>
    </record>

    <record id="access_add_vihar_user" model="ir.model.access">
        <field name="name">add.vihar user access</field>
        <field name="model_id" ref="model_ai_add_vihar"/>
        <field name="group_id" ref="group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_add_vihar_admin" model="ir.model.access">
        <field name="name">add.vihar admin access</field>
        <field name="model_id" ref="model_ai_add_vihar"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_add_vihar_district_incharge" model="ir.model.access">
        <field name="name">add.vihar district incharge access</field>
        <field name="model_id" ref="model_ai_add_vihar"/>
        <field name="group_id" ref="group_district_incharge"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_mahatma_user" model="ir.model.access">
        <field name="name">mahatma user access</field>
        <field name="model_id" ref="model_ai_mahatma"/>
        <field name="group_id" ref="group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_mahatma_admin" model="ir.model.access">
        <field name="name">mahatma admin access</field>
        <field name="model_id" ref="model_ai_mahatma"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_state_user" model="ir.model.access">
        <field name="name">state user access</field>
        <field name="model_id" ref="model_ai_state"/>
        <field name="group_id" ref="group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_state_admin" model="ir.model.access">
        <field name="name">state admin access</field>
        <field name="model_id" ref="model_ai_state"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_district_user" model="ir.model.access">
        <field name="name">district user access</field>
        <field name="model_id" ref="model_ai_district"/>
        <field name="group_id" ref="group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_district_admin" model="ir.model.access">
        <field name="name">district admin access</field>
        <field name="model_id" ref="model_ai_district"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_village_user" model="ir.model.access">
        <field name="name">village user access</field>
        <field name="model_id" ref="model_ai_village"/>
        <field name="group_id" ref="group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_village_admin" model="ir.model.access">
        <field name="name">village admin access</field>
        <field name="model_id" ref="model_ai_village"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_salutation_user" model="ir.model.access">
        <field name="name">salutation user access</field>
        <field name="model_id" ref="model_ai_salutation"/>
        <field name="group_id" ref="group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_salutation_admin" model="ir.model.access">
        <field name="name">salutation admin access</field>
        <field name="model_id" ref="model_ai_salutation"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_samuday_admin" model="ir.model.access">
        <field name="name">samuday admin access</field>
        <field name="model_id" ref="model_ai_samuday"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <record id="access_vihar_route_admin" model="ir.model.access">
        <field name="name">vihar route admin access</field>
        <field name="model_id" ref="model_ai_vihar_route"/>
        <field name="group_id" ref="group_admin"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>
</odoo>
