<odoo>
    <record id="view_state_form" model="ir.ui.view">
        <field name="name">state.form</field>
        <field name="model">ai.state</field>
        <field name="arch" type="xml">
            <form string="State">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="name_en"/>
                        <field name="name_gu"/>
                        <field name="name_hi"/>
                        <field name="status"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_state_tree" model="ir.ui.view">
        <field name="name">state.tree</field>
        <field name="model">ai.state</field>
        <field name="arch" type="xml">
            <tree string="States">
                <field name="name"/>
                <field name="status"/>
            </tree>
        </field>
    </record>
</odoo>
