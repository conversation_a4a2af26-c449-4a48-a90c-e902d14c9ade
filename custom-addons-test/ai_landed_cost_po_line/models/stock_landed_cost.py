from odoo import api, fields, models, _
from odoo.exceptions import UserError
from odoo.tools.float_utils import float_is_zero


class StockLandedCost(models.Model):
    _inherit = 'stock.landed.cost'

    split_method = fields.Selection(
        selection=[
            ('equal', 'Equal'),
            ('by_quantity', 'By Quantity'),
            ('by_current_cost_price', 'By Current Cost'),
            ('by_weight', 'By Weight'),
            ('by_volume', 'By Volume'),
            ('by_po_line_cost', 'By Purchase Order Line Cost'),
        ],
    )

    def _get_valuation_lines(self):
        lines = super()._get_valuation_lines()
        
        if any(cost.split_method == 'by_po_line_cost' for cost in self.cost_lines):
            new_lines = []
            for line in lines:
                move = line.move_id
                if move.purchase_line_id:
                    po_line = move.purchase_line_id
                    # Calculate the ratio based on received quantity vs ordered quantity
                    ordered_qty = po_line.product_qty
                    received_qty = move.quantity_done
                    
                    if not float_is_zero(ordered_qty, precision_rounding=move.product_uom.rounding):
                        ratio = received_qty / ordered_qty
                        # Use the user-defined landed cost amount
                        line.weight = po_line.landed_cost_amount * ratio
                        line.volume = po_line.landed_cost_amount * ratio
                    else:
                        line.weight = 0.0
                        line.volume = 0.0
                new_lines.append(line)
            return new_lines
        return lines

    def compute_landed_cost(self):
        """Ensure proper cost distribution when using by_po_line_cost method"""
        for cost in self:
            if any(line.split_method == 'by_po_line_cost' for line in cost.cost_lines):
                for line in cost.valuation_adjustment_lines:
                    move = line.move_id
                    if move.purchase_line_id:
                        po_line = move.purchase_line_id
                        # Update the additional landed cost value in the stock move
                        move.write({
                            'price_unit': move.price_unit + (line.additional_landed_cost / line.quantity)
                        })
        return super().compute_landed_cost()

    @api.model
    def _get_cost_distribution_methods(self):
        methods = super()._get_cost_distribution_methods()
        methods.append({
            'id': 'by_po_line_cost',
            'name': _('By Purchase Order Line Cost'),
            'sequence': 6,
        })
        return methods


class StockLandedCostLine(models.Model):
    _inherit = 'stock.landed.cost.lines'

    split_method = fields.Selection(
        selection=[
            ('equal', 'Equal'),
            ('by_quantity', 'By Quantity'),
            ('by_current_cost_price', 'By Current Cost'),
            ('by_weight', 'By Weight'),
            ('by_volume', 'By Volume'),
            ('by_po_line_cost', 'By Purchase Order Line Cost'),
        ],
    )
