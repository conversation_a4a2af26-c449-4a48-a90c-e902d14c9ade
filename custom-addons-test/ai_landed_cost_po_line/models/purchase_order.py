from odoo import api, fields, models


class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    landed_cost_ids = fields.Many2many(
        'stock.landed.cost',
        string='Landed Costs',
        copy=False,
        help='Landed costs applied to this purchase order line'
    )
    landed_cost_amount = fields.Monetary(
        string='Landed Cost Amount',
        help='User defined landed cost amount for this line'
    )
    
    def _create_or_update_picking_landed_cost(self, picking):
        """Create or update landed cost for the given picking"""
        if not self.landed_cost_amount:
            return
            
        LandedCost = self.env['stock.landed.cost']
        cost_lines_vals = []
        
        # product = self.env.ref('ai_landed_cost_po_line.product_landed_cost')
        product = self.env['product.template'].sudo().search([('name', '=', 'Landed Costs')], limit=1)
            
        cost_lines_vals.append({
            'product_id': product.product_variant_id.id,
            'price_unit': self.landed_cost_amount,
            'split_method': 'by_po_line_cost',
            'name': f'Additional Cost for {self.product_id.name}'
        })
        
        if cost_lines_vals:
            landed_cost = LandedCost.create({
                'picking_ids': [(4, picking.id)],
                'cost_lines': [(0, 0, val) for val in cost_lines_vals],
                'account_journal_id': self.env['account.journal'].search([('type', '=', 'general')], limit=1).id,
            })
            landed_cost.compute_landed_cost()
            landed_cost.button_validate()
            self.write({'landed_cost_ids': [(4, landed_cost.id)]})


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    
    def _create_picking(self):
        pickings = super()._create_picking()
        if pickings and not isinstance(pickings, bool):
            for picking in pickings:
                for move in picking.move_ids:
                    if move.purchase_line_id:
                        move.purchase_line_id._create_or_update_picking_landed_cost(picking)
        return pickings
