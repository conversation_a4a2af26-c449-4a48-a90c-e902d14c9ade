{
    'name': 'Purchase Order Line Landed Costs',
    'version': '********.0',
    'category': 'Inventory/Landed Costs',
    'summary': 'Manage landed costs at purchase order line level',
    'description': """
        This module extends the standard Odoo landed costs functionality to:
        * Allow landed cost allocation at purchase order line level
        * Support split method 'BY Purchase Orderline Costs'
        * Handle multiple receipts from the same purchase order
        * Automatically create landed costs upon receipt confirmation
    """,
    'depends': ['purchase_stock', 'stock_landed_costs'],
    'data': [
        'security/ir.model.access.csv',
        # 'data/product_data.xml',
        # 'views/stock_landed_cost_views.xml',
        'views/purchase_order_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
