<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stock_landed_cost_form_inherit" model="ir.ui.view">
        <field name="name">stock.landed.cost.form.inherit</field>
        <field name="model">stock.landed.cost</field>
        <field name="inherit_id" ref="stock_landed_costs.view_stock_landed_cost_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='cost_lines']/list//field[@name='split_method']" position="attributes">
                <attribute name="domain">[('id', 'in', parent._get_cost_distribution_methods().ids)]</attribute>
            </xpath>
        </field>
    </record>
</odoo>
