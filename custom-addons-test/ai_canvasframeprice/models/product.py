from odoo import models, fields, api

class ProductTemplate(models.Model):  # ✅ Use product.template instead
    _inherit = 'product.template'

    _x_price_extra = fields.Float(
        string="Price Extra",
        compute="_compute_x_price_extra",
        store=True
    )

    _x_product_template_attribute_value_ids = fields.One2many(
        'product.template.attribute.value', 'product_tmpl_id',
        string="Product Template Attribute Values"
    )

    @api.depends('_x_product_template_attribute_value_ids')
    def _compute_x_price_extra(self):
        for product in self:
            _x_length = _x_width = 0
            for attr in product._x_product_template_attribute_value_ids:
                if attr._x_attribute_id.name == 'Length':
                    _x_length = float(attr._x_name) if attr._x_name.replace('.', '', 1).isdigit() else 0
                if attr._x_attribute_id.name == 'Width':
                    _x_width = float(attr._x_name) if attr._x_name.replace('.', '', 1).isdigit() else 0
            if _x_length and _x_width:
                product._x_price_extra = _x_length * _x_width * 10  # Custom logic
            else:
                super()._compute_x_price_extra()  # ✅ Corrected super() call
