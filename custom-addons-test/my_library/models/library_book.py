from odoo import models, fields, api
from odoo.exceptions import ValidationError

class LibraryBook(models.Model):
    _name = 'library.book'
    _description = 'Library Book'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # Basic Fields
    name = fields.Char(string="Title", required=True, tracking=True)
    author = fields.Char(string="Author", required=True, tracking=True)
    isbn = fields.Char(string="ISBN", size=13)
    publication_date = fields.Date(string="Publication Date")
    pages = fields.Integer(string="Number of Pages")
    available_copies = fields.Integer(string="Available Copies")

    # Selection Field
    genre = fields.Selection([
        ('fiction', 'Fiction'),
        ('nonfiction', 'Non-Fiction'),
        ('science', 'Science'),
        ('history', 'History'),
    ], string="Genre")


    # Relational Fields
    publisher_id = fields.Many2one('res.partner', string="Publisher", ondelete='set null')
    borrow_ids = fields.One2many('library.borrow', 'book_id', string="Borrow History")

    # Computed Fields
    total_copies = fields.Integer(string="Total Copies", compute="_compute_total_copies", store=True)

    # Constraints
    _sql_constraints = [
        ('unique_isbn', 'UNIQUE(isbn)', 'The ISBN must be unique!')
    ]

    # Computation Method
    @api.depends('available_copies')
    def _compute_total_copies(self):
        for record in self:
            record.total_copies = record.available_copies  # Remove dummy logic and ensure correct calculation

    # Overriding unlink
    def unlink(self):
        for record in self:
            if record.borrow_ids:
                raise ValidationError("You cannot delete a book that has been borrowed!")
        return super(LibraryBook, self).unlink()10


        from odoo import models, fields

class TestModel(models.Model):
    _name = 'x_test'
    _description = 'Test Model'

    name = fields.Char(string="Name", required=True)
    activity_type_icon = fields.Char(string="Activity Type Icon")
    has_message = fields.Boolean(string="Has Message", default=False)
    my_activity_deadline = fields.Date(string="My Activity Deadline")
