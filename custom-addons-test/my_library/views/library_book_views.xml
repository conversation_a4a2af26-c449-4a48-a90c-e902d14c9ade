<odoo>
    <!-- Tree View -->
    <record id="view_library_book_tree" model="ir.ui.view">
        <field name="name">library.book.tree</field>
        <field name="model">library.book</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="author"/>
                <field name="isbn"/>
                <field name="available_copies"/>
                <field name="genre"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_library_book_form" model="ir.ui.view">
        <field name="name">library.book.form</field>
        <field name="model">library.book</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="author"/>
                        <field name="isbn"/>
                        <field name="publication_date"/>
                        <field name="pages"/>
                        <field name="available_copies"/>
                        <field name="genre"/>
                        <field name="publisher_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_library_book" model="ir.actions.act_window">
        <field name="name">Books</field>
        <field name="res_model">library.book</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_library_root" name="Library Management"/>
    <menuitem id="menu_library_books" name="Books" parent="menu_library_root" action="action_library_book"/>
</odoo>