<PERSON><PERSON><PERSON> (Master)
Name
Status

Mahatmas (Master)
Code
name (All Language Names with ' | ' sepration) - Automatic
Name In English
Name In Gujarati
Name In Hindi
Samuday
Contact Person
Contact Number

States (Master)
name (All Language Names with ' | ' sepration) - Automatic
Name IN English
Name In Gujarati	
Name In Hindi
Status

Districts (Master)
name (All Language Names with ' | ' sepration) - Automatic
Name IN English
Name In Gujarati
Name In Hindi
State
Status

Village (Master)
name (All Language Names with ' | ' sepration) - Automatic
Name IN English
Name In Gujarati
Name In Hindi
District
Status

Salutations (Master)
name (All Language Names with ' | ' sepration) - Automatic
Name IN English
Name In Gujarati
Name In Hindi
District/State
Status	

Add Vihar (Master)
Code(Mahatma table many2one - Mahatma code which should get all the details from Mahatma) 
Letter No (char)
M. S. Name in English (Mahtama Name in English fetched from code)  - (char)
મ.સા. નામ ગુજરાતીમાં (Mahtama Name in Gujarati fetched from code) - (char)
म.सा. नाम हिंदीमें (Mahtama Name in Hindi fetched from code) - (char)
Samuday (many2one samuday table)
Sevak Name (Sevak is Contact Person in Mahatma table) (char) - editable but autofetched
Thana No.
Contact Number (Sevak Contact Number is Contact Person Number in Mahatma table) (char) - editable but autofetched
District Incharge (Users many2many, only those users having group district incharge should be shown in this dropdwon)
Vihar Routes (many2many with vihar route table) - Multiple Vihar route can be added
Salutations (many2many with salutation table) - Based on the Vihar Routes data, all the districts corresponding to that route's villages should be automatically fetched in this many2many and user can change.

Additional Features:
- District Incharge can assign Vihar Route or the Full Add Vihar Record to any other user who are User/Manager/District Incharge.

On this Screen, We should additionally provide Additional Buttons to 
- View and Print Route like below example:
Date: 1/29/2025
From: Katargam dairy faliyu | કતારગામ ડેરી ફળિયું | कतारगाम डेरी फलियु
To: Vesu | વેસુ | वेसु
Date: 1/30/2025
From: Vesu | વેસુ | वेसु
To: Bavan Jinalay Tighra - Ratn Chintamani Parshvnath | બાવન જિનાલય તિઘરા - રત્ન ચિંતામણી પાર્શ્વનાથ | बावन जिनालय तिघरा - रत्न चिंतामणि पार्श्वनाथ
- Download English Route PDF
- Download Gujarati Route PDF  
- Download Hindi Route PDF
- Send English Route PDF via Email
- Send Gujarati Route PDF via Email  
- Send Hindi Route PDF via Email


Viihar Route (Master)
Vihar Date with Time(DD/MM/YYYY)
From location (many2one with village table)
To location (many2one with village table)

For Sending Emails, We should also add feature to have Email Field in District Table. And In Vihar Table Email To Field should be auto populated based on Vihar Route Villages and their corresponding Districts.

We should have following User Roles/Groups.
User (Can view all the data except for Add Vihar. Can only create and read their own Add Vihar Records)
Admin (Can view, create, edit all the data except for Add Vihar. Can create, read, update their own Add Vihar Records.)
District Incharge (Can view, create, edit and delete all the data except for Add Vihar. Can create, read, update their own Add Vihar Records. Can also add see all Add Vihar Records)
