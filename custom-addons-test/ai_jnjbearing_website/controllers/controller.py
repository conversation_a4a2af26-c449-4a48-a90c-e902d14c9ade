from odoo import http
from odoo.http import request
from odoo.addons.website.controllers.main import Website
from odoo.addons.portal.controllers.portal import CustomerPortal
class WebsitePageController(http.Controller):

    @http.route(['/'], type='http', auth="public", website=True)
    def homepage(self, **kwargs):
        return request.render('ai_jnjbearing_website.homepage_template')

    @http.route(['/about-us'], type='http', auth="public", website=True)
    def about_us(self, **kwargs):
        return request.render('ai_jnjbearing_website.about_us_template')

    @http.route('/our-team', type='http', auth="public", website=True)
    def our_team(self, **kwargs):
        # You can fetch any data you need from the database here
        # For example, if you have a model named 'team.member', you can fetch all members:
        # team_members = request.env['team.member'].search([])

        # Render the template and pass any data you need to the template
        return request.render('ai_jnjbearing_website.our_team_template', {
            # 'team_members': team_members,  # Pass the data to the template if needed
        })

    @http.route('/jnj-products', type='http', auth="public", website=True)
    def jnj_products(self, **kwargs):
        # Fetch product categories with parent_id = 17
        categories = request.env['product.public.category'].sudo().search([('parent_id', '=', 'J&J')])

        # Render the template and pass the categories to it
        return request.render('ai_jnjbearing_website.jnj_products_template', {
            'categories': categories,
        })

    @http.route('/jnj-subproducts', type='http', auth="public", website=True)
    def jnj_subproducts(self, **kwargs):
        # Fetch the category ID from the query parameters
        category_id = int(kwargs.get('s', 0))

        # Fetch the subcategories of the selected category
        subcategories = request.env['product.public.category'].sudo().search([('parent_id', '=', category_id)])

        # Render the subproducts template and pass the subcategories to it
        return request.render('ai_jnjbearing_website.jnj_subproducts_template', {
            'categories': subcategories,
        })

    @http.route('/jnj-category/<int:category_id>', type='http', auth="public", website=True)
    def jnj_category(self, category_id, **kwargs):
        # Fetch the products in the selected category
        category = request.env['product.public.category'].sudo().browse(category_id)
        products = request.env['product.template'].sudo().search([('public_categ_ids', 'in', [category_id])])

        # Fetch category headers and searchable headers
        category_headers = request.env['x_category_headers'].sudo().search([
            ('x_category_id', '=', category_id),
            ('x_visible_in_table', '=', True)
        ], order='x_sequence ASC')

        category_searchable_headers = request.env['x_category_headers'].sudo().search([
            ('x_category_id', '=', category_id),
            ('x_visible_in_table', '=', True),
            ('x_is_searchable', '=', True)
        ], order='x_sequence ASC')

        # Render the category template and pass the category, products, and headers to it
        return request.render('ai_jnjbearing_website.jnj_category_template', {
            'category': category,
            'products': products,
            'category_headers': category_headers,
            'category_searchable_headers': category_searchable_headers,
        })

    @http.route('/j-j-sand-plant', type='http', auth="public", website=True)
    def jnj_sand_plant(self, **kwargs):
        # Render the template
        return request.render('ai_jnjbearing_website.jnj_sand_plant_template', {})

    @http.route('/our-services', type='http', auth="public", website=True)
    def our_services(self, **kwargs):
        # Render the template
        return request.render('ai_jnjbearing_website.services_template', {})

    @http.route('/privacy', type='http', auth="public", website=True)
    def privacy_policy(self, **kwargs):
        # Render the template
        return request.render('ai_jnjbearing_website.privacy_policy_template', {})

    @http.route('/products-15', type='http', auth="public", website=True)
    def products_15(self, **kwargs):
        # Fetch product categories with parent_id = 32
        categories = request.env['product.public.category'].sudo().search([('parent_id', '=', 32)])

        # Render the template and pass the categories to it
        return request.render('ai_jnjbearing_website.products_15_template', {
            'categories': categories,
        })

    @http.route('/jnj-infrastructure', type='http', auth="public", website=True)
    def privacy_policy(self, **kwargs):
        # Render the template
        return request.render('ai_jnjbearing_website.jnj_infrastructure_template', {})


class ContactController(CustomerPortal):
    @http.route(['/contactus'], type='http', auth='public', website=True, sitemap=True)
    def contact_page(self, **kwargs):
        values = self._prepare_portal_layout_values()

        # Get company information
        company = request.env.company
        values.update({
            'company': company,
            'res_company': company,
        })

        # If form is submitted
        if request.httprequest.method == 'POST':
            error = {}
            values = {
                'name': kwargs.get('name', ''),
                'phone': kwargs.get('phone', ''),
                'email_from': kwargs.get('email_from', ''),
                'company': kwargs.get('company', ''),
                'subject': kwargs.get('subject', ''),
                'description': kwargs.get('description', ''),
            }

            # Basic validation
            if not values.get('name'):
                error['name'] = 'Please enter your name'
            if not values.get('email_from'):
                error['email_from'] = 'Please enter your email'
            if not values.get('description'):
                error['description'] = 'Please enter your message'

            if not error:
                # Create contact message
                request.env['mail.mail'].sudo().create({
                    'subject': f"Website Contact Form: {values.get('subject', 'Contact Request')}",
                    'email_from': values.get('email_from'),
                    'email_to': company.email,
                    'body_html': f"""
                        <p>Contact Information:</p>
                        <ul>
                            <li>Name: {values.get('name')}</li>
                            <li>Phone: {values.get('phone')}</li>
                            <li>Email: {values.get('email_from')}</li>
                            <li>Company: {values.get('company')}</li>
                        </ul>
                        <p>Message:</p>
                        <p>{values.get('description')}</p>
                    """,
                })

                # Log the contact in CRM if installed
                if request.env['ir.module.module'].sudo().search([('name', '=', 'crm'), ('state', '=', 'installed')]):
                    request.env['crm.lead'].sudo().create({
                        'name': f"Website Contact: {values.get('subject', 'Contact Request')}",
                        'partner_name': values.get('name'),
                        'email_from': values.get('email_from'),
                        'phone': values.get('phone'),
                        'description': values.get('description'),
                        'type': 'opportunity',
                        'medium_id': request.env.ref('utm.utm_medium_website').id,
                    })

                # Track event in Plausible if enabled
                if request.website.plausible_shared_key:
                    request.env['website.track.event'].create({
                        'event_name': 'Lead Generation',
                        'event_params': {'CTA': 'Contact Us'}
                    })

                # Redirect to thank you page
                return request.redirect('/contactus-thank-you')

            values['error'] = error

        return request.render("ai_jnjbearing_website.contactus_template", values)

    @http.route(['/contactus-thank-you'], type='http', auth='public', website=True, sitemap=False)
    def contact_thank_you(self, **kwargs):
        """Handler for the thank you page"""
        values = {
            'company': request.env.company,
        }
        return request.render("ai_jnjbearing_website.contactus_thanks_template", values)

    @http.route(['/contactus/submit'], type='json', auth="public", website=True)
    def contact_form_submit(self, **kwargs):
        """AJAX submission endpoint for the contact form"""
        return self.contact_page(**kwargs)




