<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <!-- Home Menu -->
        <record id="website_menu_5" model="website.menu">
            <field name="name">Home</field>
            <field name="url">/</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="website.main_menu"/>
        </record>

        <!-- About Us Menu and Submenu -->
        <record id="website_menu_13" model="website.menu">
            <field name="name">About Us</field>
            <field name="url">#</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="website.main_menu"/>
        </record>

        <record id="website_menu_112" model="website.menu">
            <field name="name">Company Profile</field>
            <field name="url">/about-us</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="website_menu_13"/>
        </record>

        <record id="website_menu_15" model="website.menu">
            <field name="name">Our Team</field>
            <field name="url">/our-team</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="website_menu_13"/>
        </record>

        <!-- Products Menu and Submenu -->
        <record id="website_menu_16" model="website.menu">
            <field name="name">Products</field>
            <field name="url">#</field>
            <field name="sequence">30</field>
            <field name="parent_id" ref="website.main_menu"/>
        </record>

        <record id="website_menu_18" model="website.menu">
            <field name="name">J&amp;J</field>
            <field name="url">/jnj-products</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="website_menu_16"/>
        </record>

        <record id="website_menu_22" model="website.menu">
            <field name="name">jnj-subproducts</field>
            <field name="url">/jnj-subproducts</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="website_menu_16"/>
        </record>

        <!-- Infrastructure Menu -->
        <record id="website_menu_20" model="website.menu">
            <field name="name">Infrastructure</field>
            <field name="url">/jnj-infrastructure</field>
            <field name="sequence">40</field>
            <field name="parent_id" ref="website.main_menu"/>
        </record>

        <!-- Contact Us Menu -->
        <record id="website_menu_6" model="website.menu">
            <field name="name">Contact us</field>
            <field name="url">/contactus</field>
            <field name="sequence">50</field>
            <field name="parent_id" ref="website.main_menu"/>
        </record>

        <!-- Categories Menu -->
        <record id="website_menu_23" model="website.menu">
            <field name="name">jnj-categories</field>
            <field name="url">/jnj-categories</field>
            <field name="sequence">60</field>
            <field name="parent_id" ref="website.main_menu"/>
        </record>

        <!-- Products 15 Menu -->
        <record id="website_menu_24" model="website.menu">
            <field name="name">products_15</field>
            <field name="url">/products-15</field>
            <field name="sequence">70</field>
            <field name="parent_id" ref="website.main_menu"/>
        </record>
    </data>
</odoo>