<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_x_category_headers_form" model="ir.ui.view">
        <field name="name">x_category_headers.form</field>
        <field name="model">x_category_headers</field>
        <field name="arch" type="xml">
            <form string="Category Headers">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_sequence"/>
                            <field name="x_name"/>
                            <field name="x_label"/>
                            <field name="x_category_id"/>
                            <field name="x_is_searchable"/>
                            <field name="x_search_type"/>
                            <field name="x_visible_in_table"/>
                            <field name="x_category_parent_header_id"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_category_headers_tree" model="ir.ui.view">
        <field name="name">x_category_headers.tree</field>
        <field name="model">x_category_headers</field>
        <field name="arch" type="xml">
            <tree string="Category Headers">
                <!-- <field name="name"/> -->
                <field name="x_sequence"/>
                <field name="x_name"/>
                <field name="x_label"/>
                <field name="x_category_id"/>
                <field name="x_is_searchable"/>
                <field name="x_search_type"/>
                <field name="x_visible_in_table"/>
                <field name="x_category_parent_header_id"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_category_headers_search" model="ir.ui.view">
        <field name="name">x_category_headers.search</field>
        <field name="model">x_category_headers</field>
        <field name="arch" type="xml">
            <search string="Category Headers">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_label"/>
                <field name="x_category_id"/>
                <field name="x_category_parent_header_id"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_category_headers" model="ir.actions.act_window">
        <field name="name">Category Headers</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_category_headers</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_category_headers_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Category Headers
            </p>
        </field>
    </record>
    
    <record id="view_x_category_parent_headers_form" model="ir.ui.view">
        <field name="name">x_category_parent_headers.form</field>
        <field name="model">x_category_parent_headers</field>
        <field name="arch" type="xml">
            <form string="Category Parent Headers">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_name"/>
                            <field name="x_sequence"/>
                            <field name="x_category_id"/>
                            <field name="x_category_headers"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_category_parent_headers_tree" model="ir.ui.view">
        <field name="name">x_category_parent_headers.tree</field>
        <field name="model">x_category_parent_headers</field>
        <field name="arch" type="xml">
            <tree string="Category Parent Headers">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_sequence"/>
                <field name="x_category_id"/>
                <field name="x_category_headers"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_category_parent_headers_search" model="ir.ui.view">
        <field name="name">x_category_parent_headers.search</field>
        <field name="model">x_category_parent_headers</field>
        <field name="arch" type="xml">
            <search string="Category Parent Headers">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_category_id"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_category_parent_headers" model="ir.actions.act_window">
        <field name="name">Category Parent Headers</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_category_parent_headers</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_category_parent_headers_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Category Parent Headers
            </p>
        </field>
    </record>
    
    <record id="view_x_data_form" model="ir.ui.view">
        <field name="name">x_data.form</field>
        <field name="model">x_data</field>
        <field name="arch" type="xml">
            <form string="Data Modelling">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_model_id"/>
                            <field name="x_name"/>
                            <field name="x_data_file"/>
                            <field name="x_resolve_relations"/>
                            <field name="x_data"/>
                            <field name="x_json"/>
                            <field name="x_data_mapping"/>
                            <field name="x_skip_keys"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_data_tree" model="ir.ui.view">
        <field name="name">x_data.tree</field>
        <field name="model">x_data</field>
        <field name="arch" type="xml">
            <tree string="Data Modelling">
                <!-- <field name="name"/> -->
                <field name="x_model_id"/>
                <field name="x_name"/>
                <field name="x_data_file"/>
                <field name="x_resolve_relations"/>
                <field name="x_data"/>
                <field name="x_json"/>
                <field name="x_data_mapping"/>
                <field name="x_skip_keys"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_data_search" model="ir.ui.view">
        <field name="name">x_data.search</field>
        <field name="model">x_data</field>
        <field name="arch" type="xml">
            <search string="Data Modelling">
                <!-- <field name="name"/> -->
                <field name="x_model_id"/>
                <field name="x_name"/>
                <field name="x_data"/>
                <field name="x_json"/>
                <field name="x_data_mapping"/>
                <field name="x_skip_keys"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_data" model="ir.actions.act_window">
        <field name="name">Data Modelling</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_data</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_data_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Data Modelling
            </p>
        </field>
    </record>
    
    <record id="view_x_documents_form" model="ir.ui.view">
        <field name="name">x_documents.form</field>
        <field name="model">x_documents</field>
        <field name="arch" type="xml">
            <form string="Documents">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_code"/>
                            <field name="x_model"/>
                            <field name="x_name"/>
                            <field name="x_print_name"/>
                            <field name="x_report"/>
                            <field name="x_template_name"/>
                            <field name="x_view"/>
                            <field name="x_view_name"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_documents_tree" model="ir.ui.view">
        <field name="name">x_documents.tree</field>
        <field name="model">x_documents</field>
        <field name="arch" type="xml">
            <tree string="Documents">
                <!-- <field name="name"/> -->
                <field name="x_code"/>
                <field name="x_model"/>
                <field name="x_name"/>
                <field name="x_print_name"/>
                <field name="x_report"/>
                <field name="x_template_name"/>
                <field name="x_view"/>
                <field name="x_view_name"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_documents_search" model="ir.ui.view">
        <field name="name">x_documents.search</field>
        <field name="model">x_documents</field>
        <field name="arch" type="xml">
            <search string="Documents">
                <!-- <field name="name"/> -->
                <field name="x_code"/>
                <field name="x_model"/>
                <field name="x_name"/>
                <field name="x_print_name"/>
                <field name="x_report"/>
                <field name="x_template_name"/>
                <field name="x_view"/>
                <field name="x_view_name"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_documents" model="ir.actions.act_window">
        <field name="name">Documents</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_documents</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_documents_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Documents
            </p>
        </field>
    </record>
    
    <record id="view_x_enquiry_form" model="ir.ui.view">
        <field name="name">x_enquiry.form</field>
        <field name="model">x_enquiry</field>
        <field name="arch" type="xml">
            <form string="Enquiry">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_name"/>
                            <field name="x_address"/>
                            <field name="x_city"/>
                            <field name="x_company_name"/>
                            <field name="x_country"/>
                            <field name="x_email"/>
                            <field name="x_last_name"/>
                            <field name="x_message"/>
                            <field name="x_phone"/>
                            <field name="x_state"/>
                            <field name="x_subject"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_enquiry_tree" model="ir.ui.view">
        <field name="name">x_enquiry.tree</field>
        <field name="model">x_enquiry</field>
        <field name="arch" type="xml">
            <tree string="Enquiry">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_address"/>
                <field name="x_city"/>
                <field name="x_company_name"/>
                <field name="x_country"/>
                <field name="x_email"/>
                <field name="x_last_name"/>
                <field name="x_message"/>
                <field name="x_phone"/>
                <field name="x_state"/>
                <field name="x_subject"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_enquiry_search" model="ir.ui.view">
        <field name="name">x_enquiry.search</field>
        <field name="model">x_enquiry</field>
        <field name="arch" type="xml">
            <search string="Enquiry">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_address"/>
                <field name="x_city"/>
                <field name="x_company_name"/>
                <field name="x_country"/>
                <field name="x_email"/>
                <field name="x_last_name"/>
                <field name="x_message"/>
                <field name="x_state"/>
                <field name="x_subject"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_enquiry" model="ir.actions.act_window">
        <field name="name">Enquiry</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_enquiry</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_enquiry_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Enquiry
            </p>
        </field>
    </record>
    
    <record id="view_x_generate_barcode_batch_form" model="ir.ui.view">
        <field name="name">x_generate_barcode_batch.form</field>
        <field name="model">x_generate_barcode_batch</field>
        <field name="arch" type="xml">
            <form string="Generate Barcode Batch">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_generated_barcodes"/>
                            <field name="x_name"/>
                            <field name="x_product_id"/>
                            <field name="x_number"/>
                            <field name="x_inspec_by"/>
                            <field name="x_csv_file"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_generate_barcode_batch_tree" model="ir.ui.view">
        <field name="name">x_generate_barcode_batch.tree</field>
        <field name="model">x_generate_barcode_batch</field>
        <field name="arch" type="xml">
            <tree string="Generate Barcode Batch">
                <!-- <field name="name"/> -->
                <field name="x_generated_barcodes"/>
                <field name="x_name"/>
                <field name="x_product_id"/>
                <field name="x_number"/>
                <field name="x_inspec_by"/>
                <field name="x_csv_file"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_generate_barcode_batch_search" model="ir.ui.view">
        <field name="name">x_generate_barcode_batch.search</field>
        <field name="model">x_generate_barcode_batch</field>
        <field name="arch" type="xml">
            <search string="Generate Barcode Batch">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_product_id"/>
                <field name="x_inspec_by"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_generate_barcode_batch" model="ir.actions.act_window">
        <field name="name">Generate Barcode Batch</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_generate_barcode_batch</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_generate_barcode_batch_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Generate Barcode Batch
            </p>
        </field>
    </record>
    
    <record id="view_x_generated_barcodes_form" model="ir.ui.view">
        <field name="name">x_generated_barcodes.form</field>
        <field name="model">x_generated_barcodes</field>
        <field name="arch" type="xml">
            <form string="Generated Barcodes">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_name"/>
                            <field name="x_product_id"/>
                            <field name="x_token"/>
                            <field name="x_no_of_visits"/>
                            <field name="x_generate_barcode_batch_id"/>
                            <field name="x_msg"/>
                            <field name="x_product_code"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_generated_barcodes_tree" model="ir.ui.view">
        <field name="name">x_generated_barcodes.tree</field>
        <field name="model">x_generated_barcodes</field>
        <field name="arch" type="xml">
            <tree string="Generated Barcodes">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_product_id"/>
                <field name="x_token"/>
                <field name="x_no_of_visits"/>
                <field name="x_generate_barcode_batch_id"/>
                <field name="x_msg"/>
                <field name="x_product_code"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_generated_barcodes_search" model="ir.ui.view">
        <field name="name">x_generated_barcodes.search</field>
        <field name="model">x_generated_barcodes</field>
        <field name="arch" type="xml">
            <search string="Generated Barcodes">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_product_id"/>
                <field name="x_token"/>
                <field name="x_generate_barcode_batch_id"/>
                <field name="x_product_code"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_generated_barcodes" model="ir.actions.act_window">
        <field name="name">Generated Barcodes</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_generated_barcodes</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_generated_barcodes_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Generated Barcodes
            </p>
        </field>
    </record>
    
    <record id="view_x_product_enquiry_form" model="ir.ui.view">
        <field name="name">x_product_enquiry.form</field>
        <field name="model">x_product_enquiry</field>
        <field name="arch" type="xml">
            <form string="Product Enquiry">
                <sheet>
                    <!-- <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div> -->
                    <group>
                        <group>
                            <field name="x_name"/>
                            <field name="x_phone"/>
                            <field name="x_email"/>
                            <field name="x_company_name"/>
                            <field name="x_qty"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_x_product_enquiry_tree" model="ir.ui.view">
        <field name="name">x_product_enquiry.tree</field>
        <field name="model">x_product_enquiry</field>
        <field name="arch" type="xml">
            <tree string="Product Enquiry">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_phone"/>
                <field name="x_email"/>
                <field name="x_company_name"/>
                <field name="x_qty"/>
            </tree>
        </field>
    </record>
    
    <record id="view_x_product_enquiry_search" model="ir.ui.view">
        <field name="name">x_product_enquiry.search</field>
        <field name="model">x_product_enquiry</field>
        <field name="arch" type="xml">
            <search string="Product Enquiry">
                <!-- <field name="name"/> -->
                <field name="x_name"/>
                <field name="x_phone"/>
                <field name="x_email"/>
                <field name="x_company_name"/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="groupby_name" domain="[]" context="{'group_by': 'x_name'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <record id="action_x_product_enquiry" model="ir.actions.act_window">
        <field name="name">Product Enquiry</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">x_product_enquiry</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_x_product_enquiry_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Product Enquiry
            </p>
        </field>
    </record>
    
    <record id="view_product_template_form_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
    
            <xpath expr="//form/sheet/notebook/page[1]" position="after">
                <page string="J&amp;J Technical Details">
                    <group>
                        <group string="Specifications">
                            <field name="x_website_url"/>
                            <field name="x_description"/>
                            <field name="x_adapter_sleeve"/>
                            <field name="x_bearing_adapter_sleeve_designation"/>
                            <field name="x_bearing_seating_Ca"/>
                            <field name="x_bearing_seating_Da"/>
                            <field name="x_bearing_seating_db"/>
                            <field name="x_felt_strip"/>
                            <field name="x_housing_desi"/>
                            <field name="x_housing_dimensions_A"/>
                            <field name="x_housing_dimensions_A1"/>
                            <field name="x_housing_dimensions_A2"/>
                            <field name="x_housing_dimensions_A3"/>
                            <field name="x_housing_dimensions_B"/>
                            <field name="x_housing_dimensions_Ca"/>
                            <field name="x_housing_dimensions_D"/>
                            <field name="x_housing_dimensions_Da"/>
                            <field name="x_housing_dimensions_E"/>
                            <field name="x_housing_dimensions_G"/>
                            <field name="x_housing_dimensions_H"/>
                            <field name="x_housing_dimensions_H1"/>
                            <field name="x_housing_dimensions_H2"/>
                            <field name="x_housing_dimensions_I"/>
                            <field name="x_housing_dimensions_J"/>
                            <field name="x_housing_dimensions_J1"/>
                            <field name="x_housing_dimensions_L"/>
                            <field name="x_housing_dimensions_N"/>
                            <field name="x_housing_dimensions_N1"/>
                            <field name="x_housing_dimensions_S"/>
                            <field name="x_housing_dimensions_V"/>
                            <field name="x_housing_dimensions_a"/>
                            <field name="x_housing_dimensions_b"/>
                            <field name="x_housing_dimensions_c"/>
                            <field name="x_housing_dimensions_h"/>
                            <field name="x_housing_dimensions_m"/>
                            <field name="x_housing_dimensions_n"/>
                            <field name="x_housing_dimensions_s"/>
                            <field name="x_housing_dimensions_s_sd3300"/>
                            <field name="x_housing_dimensions_u"/>
                            <field name="x_housing_dimensions_v"/>
                            <field name="x_housing_without_seals"/>
                            <field name="x_qty"/>
                            <field name="x_rings_no_desg"/>
                            <field name="x_rubber_orings"/>
                            <field name="x_shaft_dia_in"/>
                            <field name="x_shift_dia_D"/>
                            <field name="x_shift_dia_d1"/>
                            <field name="x_shift_dia_da"/>
                            <field name="x_shift_dia_db"/>
                            <field name="x_shift_dia_mm"/>
                            <field name="x_width_across_seal_A2"/>
                            <field name="x_width_across_seal_A3"/>
                            <field name="x_width_across_seal_Da"/>
                            <field name="x_without_seal_housing"/>
                            <field name="x_without_seal_housing_des"/>                       
                            <field name="x_appro_bearing"/>
                        </group>
                    </group>
                </page>
            </xpath>
        
            <!-- <xpath expr="//xpath[4]" position="inside">
        
                <field name="x_appro_bearing"/>
            </xpath> -->
        
        </field>
    </record>
    
   
    <record id="view_product_product_form_inherit" model="ir.ui.view">
        <field name="name">product.product.form.inherit</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
    
            <xpath expr="//form/sheet/notebook/page[1]" position="after">                 
                <page string="J&amp;J Technical Details">
                    <group>
                        <group string="Specifications">
                            <field name="x_adapter_sleeve"/>
                            <field name="x_appro_bearing"/>
                            <field name="x_bearing_adapter_sleeve_designation"/>
                            <field name="x_bearing_seating_Ca"/>
                            <field name="x_bearing_seating_Da"/>
                            <field name="x_bearing_seating_db"/>
                            <field name="x_felt_strip"/>
                            <field name="x_housing_desi"/>
                            <field name="x_housing_dimensions_A"/>
                            <field name="x_housing_dimensions_A1"/>
                            <field name="x_housing_dimensions_A2"/>
                            <field name="x_housing_dimensions_A3"/>
                            <field name="x_housing_dimensions_B"/>
                            <field name="x_housing_dimensions_Ca"/>
                            <field name="x_housing_dimensions_D"/>
                            <field name="x_housing_dimensions_Da"/>
                            <field name="x_housing_dimensions_E"/>
                            <field name="x_housing_dimensions_G"/>
                            <field name="x_housing_dimensions_H"/>
                            <field name="x_housing_dimensions_H1"/>
                            <field name="x_housing_dimensions_H2"/>
                            <field name="x_housing_dimensions_I"/>
                            <field name="x_housing_dimensions_J"/>
                            <field name="x_housing_dimensions_J1"/>
                            <field name="x_housing_dimensions_L"/>
                            <field name="x_housing_dimensions_N"/>
                            <field name="x_housing_dimensions_N1"/>
                            <field name="x_housing_dimensions_S"/>
                            <field name="x_housing_dimensions_V"/>
                            <field name="x_housing_dimensions_a"/>
                            <field name="x_housing_dimensions_b"/>
                            <field name="x_housing_dimensions_c"/>
                            <field name="x_housing_dimensions_h"/>
                            <field name="x_housing_dimensions_m"/>
                            <field name="x_housing_dimensions_n"/>
                            <field name="x_housing_dimensions_s"/>
                            <field name="x_housing_dimensions_s_sd3300"/>
                            <field name="x_housing_dimensions_u"/>
                            <field name="x_housing_dimensions_v"/>
                            <field name="x_housing_without_seals"/>
                            <field name="x_qty"/>
                            <field name="x_rings_no_desg"/>
                            <field name="x_rubber_orings"/>
                            <field name="x_shaft_dia_in"/>
                            <field name="x_shift_dia_D"/>
                            <field name="x_shift_dia_d1"/>
                            <field name="x_shift_dia_da"/>
                            <field name="x_shift_dia_db"/>
                            <field name="x_shift_dia_mm"/>
                            <field name="x_width_across_seal_A2"/>
                            <field name="x_width_across_seal_A3"/>
                            <field name="x_width_across_seal_Da"/>
                            <field name="x_without_seal_housing"/>
                            <field name="x_without_seal_housing_des"/>
                        </group>
                    </group>
                </page>
            </xpath>
        
        </field>
    </record>
    
     
<record id="view_product_public_category_form_inherit" model="ir.ui.view">
    <field name="name">product.public.category.form.inherit</field>
    <field name="model">product.public.category</field>
    <field name="inherit_id" ref="website_sale.product_public_category_form_view"/>
    <field name="arch" type="xml">
        <xpath expr="/form/sheet/div/group//field[@name='name']" position="after">
        
            <!-- <group string="Custom Fields"> -->
             <field name="x_category_description"/>
                <field name="x_diagrams"/>
                <!-- <field name="x_product_image"/> -->
            <!-- </group> -->
        </xpath>
    </field>
</record>
    
    <record id="view_product_public_category_tree_inherit" model="ir.ui.view">
        <field name="name">product.public.category.tree.inherit</field>
        <field name="model">product.public.category</field>
        <field name="inherit_id" ref="website_sale.product_public_category_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="inside">
                <field name="x_category_description"/>
                <!-- <field name="x_diagrams"/> -->
                <field name="x_product_image"/>
            </xpath>
            <xpath expr="//tree" position="attributes">
                <attribute name="editable"></attribute>
            </xpath>
        </field>
    </record>
    
    <!-- <record id="view_account_analytic_line_form_inherit" model="ir.ui.view">
        <field name="name">account.analytic.line.form.inherit</field>
        <field name="model">account.analytic.line</field>
        <field name="inherit_id" ref="account_analytic_line.view_account_analytic_line_form"/>
        <field name="arch" type="xml">
    
            <xpath expr="//form/sheet/group[1]" position="inside">
        
                <field name="x_plan2_id"/>
                <field name="x_plan3_id"/>
            </xpath>
        
        </field>
    </record>
    
    <record id="view_account_analytic_line_tree_inherit" model="ir.ui.view">
        <field name="name">account.analytic.line.tree.inherit</field>
        <field name="model">account.analytic.line</field>
        <field name="inherit_id" ref="account_analytic_line.view_account_analytic_line_tree"/>
        <field name="arch" type="xml">
    
            <xpath expr="//tree" position="inside">
                <field name="x_plan2_id"/>
                <field name="x_plan3_id"/>
            </xpath>
        
        </field>
    </record> -->
</odoo>