// Variables
$primary-color: #2C3E50;
$secondary-color: #E74C3C;
$accent-color: #3498DB;
$light-gray: #f8f9fa;

// Animations
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes rotateIn {
    from {
        transform: rotate(-5deg) scale(0.95);
        opacity: 0;
    }
    to {
        transform: rotate(0) scale(1);
        opacity: 1;
    }
}

// Global Animations
.animate-fade-in {
    animation: fadeIn 0.8s ease-in-out;
}

.animate-slide-up {
    animation: slideInUp 0.8s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out;
}

.animate-rotate-in {
    animation: rotateIn 0.6s ease-out;
}

// Scroll Animations
.reveal-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
    
    &.revealed {
        opacity: 1;
        transform: translateY(0);
    }
}

// Header Styles
.o_header_photobook {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    
    .navbar {
        padding: 1rem 0;
    }

    .brand-name {
        font-size: 1.5rem;
        font-weight: bold;
        color: $primary-color;
    }

    .nav-link {
        font-weight: 500;
        color: $primary-color;
        
        &:hover {
            color: $secondary-color;
        }
    }
}

// Hero Section
.hero-section {
    padding: 4rem 0;
    background-color: $light-gray;

    h1 {
        font-size: 3rem;
        font-weight: bold;
        color: $primary-color;
        margin-bottom: 1.5rem;
    }

    .lead {
        font-size: 1.25rem;
        color: lighten($primary-color, 20%);
        margin-bottom: 2rem;
    }

    .btn-primary {
        background-color: $secondary-color;
        border-color: $secondary-color;
        padding: 0.75rem 2rem;
        font-weight: 600;
        
        &:hover {
            background-color: darken($secondary-color, 10%);
            border-color: darken($secondary-color, 10%);
        }
    }
}

// Featured Categories
.featured-categories {
    padding: 4rem 0;

    .category-card {
        text-decoration: none;
        color: inherit;
        transition: transform 0.3s ease;

        &:hover {
            transform: translateY(-5px);
        }

        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            
            img {
                height: 200px;
                object-fit: cover;
            }

            .card-body {
                text-align: center;
            }
        }
    }
}

// Features Section
.features-section {
    padding: 4rem 0;
    background-color: $light-gray;

    .feature-item {
        padding: 2rem;
        
        i {
            color: $accent-color;
        }

        h4 {
            margin: 1rem 0;
            color: $primary-color;
        }

        p {
            color: lighten($primary-color, 20%);
        }
    }
}

// Testimonials Section
.testimonials-section {
    padding: 4rem 0;

    .testimonial-card {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 2rem;

        .rating {
            color: #f1c40f;
            margin-bottom: 1rem;
        }

        p {
            font-style: italic;
            color: $primary-color;
            margin-bottom: 1rem;
        }

        .author {
            font-weight: 600;
            color: $accent-color;
        }
    }
}

// Product Cards
.product-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        
        .product-overlay {
            opacity: 1;
        }
        
        .quick-view-btn {
            transform: translateY(0);
        }
    }
    
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.3);
        opacity: 0;
        transition: all 0.3s ease;
    }
    
    .quick-view-btn {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%) translateY(20px);
        transition: all 0.3s ease;
        background: white;
        color: $primary-color;
        padding: 10px 20px;
        border-radius: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
}

// Shopping Cart
.js_cart_lines {
    .td-product_name {
        font-weight: 600;
    }
}

// Photobook Preview
.photobook-preview {
    perspective: 1000px;
    
    .book-container {
        transform-style: preserve-3d;
        transition: transform 0.8s ease;
        
        &:hover {
            transform: rotateY(10deg);
        }
    }
    
    .page-turn {
        animation: pageTurn 1.2s ease-in-out;
    }
}

@keyframes pageTurn {
    0% { transform: rotateY(0); }
    50% { transform: rotateY(-15deg); }
    100% { transform: rotateY(0); }
}

// Custom Cursor
.custom-cursor {
    width: 20px;
    height: 20px;
    border: 2px solid $accent-color;
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    transition: all 0.1s ease;
    z-index: 9999;
    
    &.hover {
        transform: scale(1.5);
        background: rgba($accent-color, 0.1);
    }
}

// Loading Animations
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba($primary-color, 0.1);
    border-left-color: $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

// Image Gallery
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
    
    .gallery-item {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        cursor: pointer;
        
        img {
            transition: transform 0.5s ease;
        }
        
        &:hover {
            img {
                transform: scale(1.1);
            }
            
            .gallery-overlay {
                opacity: 1;
            }
        }
        
        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba($primary-color, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            
            .overlay-content {
                color: white;
                text-align: center;
                transform: translateY(20px);
                transition: transform 0.3s ease;
            }
        }
    }
}

// Responsive Design
@media (max-width: 768px) {
    .hero-section {
        h1 {
            font-size: 2rem;
        }
    }
    
    .featured-categories {
        .category-card {
            margin-bottom: 2rem;
        }
    }
}
