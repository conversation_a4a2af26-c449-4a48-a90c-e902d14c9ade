odoo.define('ai_photobook.gallery_viewer', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');
    var _t = core._t;

    var GalleryViewer = publicWidget.Widget.extend({
        selector: '.gallery-grid',
        events: {
            'click .gallery-item': '_onGalleryItemClick',
            'click .filter-btn': '_onFilterClick'
        },

        init: function () {
            this._super.apply(this, arguments);
            this.currentFilter = 'all';
            this.items = [];
            this.isAnimating = false;
        },

        start: function () {
            this._super.apply(this, arguments);
            this._initMasonry();
            this._initLightbox();
            this._initFilters();
            this._initHoverEffects();
            return this._loadGalleryItems();
        },

        //---------- Gallery Initialization ----------
        _initMasonry: function () {
            this.$grid = this.$('.gallery-grid').masonry({
                itemSelector: '.gallery-item',
                columnWidth: '.gallery-item',
                percentPosition: true,
                transitionDuration: '0.3s',
                stagger: 30,
                // Add animation
                visibleStyle: { transform: 'translateY(0)', opacity: 1 },
                hiddenStyle: { transform: 'translateY(100px)', opacity: 0 }
            });
        },

        _initLightbox: function () {
            this.$el.find('.gallery-item').magnificPopup({
                type: 'image',
                gallery: {
                    enabled: true,
                    navigateByImgClick: true,
                    preload: [0, 1],
                    tPrev: _t('Previous'),
                    tNext: _t('Next')
                },
                zoom: {
                    enabled: true,
                    duration: 300,
                    easing: 'ease-in-out'
                },
                callbacks: {
                    beforeOpen: function() {
                        // Add animation to popup
                        this.st.mainClass = 'mfp-zoom-in';
                    }
                }
            });
        },

        _initFilters: function () {
            this.$filterButtons = this.$('.filter-btn');
            this.$filterButtons.on('click', this._onFilterClick.bind(this));
        },

        _initHoverEffects: function () {
            this.$el.find('.gallery-item').each(function() {
                $(this).hoverIntent({
                    over: function() {
                        $(this).find('.gallery-overlay').addClass('show');
                        $(this).find('img').addClass('scale');
                    },
                    out: function() {
                        $(this).find('.gallery-overlay').removeClass('show');
                        $(this).find('img').removeClass('scale');
                    },
                    timeout: 200
                });
            });
        },

        //---------- Event Handlers ----------
        _onGalleryItemClick: function (ev) {
            ev.preventDefault();
            var $item = $(ev.currentTarget);
            
            // Show full image details
            this._showImageDetails($item);
        },

        _onFilterClick: function (ev) {
            ev.preventDefault();
            if (this.isAnimating) return;

            var $button = $(ev.currentTarget);
            var filter = $button.data('filter');

            // Update active state
            this.$filterButtons.removeClass('active');
            $button.addClass('active');

            // Filter items
            this._filterItems(filter);
        },

        //---------- Gallery Functions ----------
        _loadGalleryItems: function () {
            return this._rpc({
                route: '/photobook/gallery/items',
                params: {}
            }).then(function (result) {
                this.items = result.items;
                this._renderItems();
            }.bind(this));
        },

        _renderItems: function () {
            var self = this;
            var $items = $(qweb.render('ai_photobook.GalleryItems', {
                items: this.items
            }));

            // Add items to masonry grid
            this.$grid.append($items).masonry('appended', $items);
            
            // Trigger layout after images are loaded
            this.$grid.imagesLoaded().progress(function() {
                self.$grid.masonry('layout');
            });
        },

        _filterItems: function (filter) {
            this.isAnimating = true;
            this.currentFilter = filter;

            var self = this;
            var $items = this.$('.gallery-item');
            var $filtered = filter === 'all' ? 
                $items : 
                $items.filter('[data-category="' + filter + '"]');

            // Hide items that don't match filter
            $items.not($filtered).each(function() {
                $(this).addClass('hide');
                self.$grid.masonry('hide', this);
            });

            // Show filtered items
            $filtered.each(function() {
                $(this).removeClass('hide');
                self.$grid.masonry('reveal', this);
            });

            // Update layout
            this.$grid.one('layoutComplete', function() {
                self.isAnimating = false;
            });
            this.$grid.masonry();
        },

        _showImageDetails: function ($item) {
            var imageId = $item.data('id');
            var item = this.items.find(function(i) { return i.id === imageId; });

            if (!item) return;

            // Create and show modal with image details
            var $modal = $(qweb.render('ai_photobook.GalleryItemModal', {
                item: item
            }));

            // Add modal to body
            $modal.appendTo('body').modal({
                backdrop: 'static',
                keyboard: false
            });

            // Handle modal events
            $modal.on('hidden.bs.modal', function() {
                $modal.remove();
            });

            // Initialize image zoom
            $modal.find('.gallery-detail-image').zoom({
                magnify: 1.5
            });
        },

        //---------- Social Sharing ----------
        _initSocialSharing: function () {
            this.$el.find('.share-btn').on('click', function(e) {
                e.preventDefault();
                var type = $(this).data('type');
                var url = window.location.href;
                var title = document.title;
                var shareUrl;

                switch(type) {
                    case 'facebook':
                        shareUrl = 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(url);
                        break;
                    case 'twitter':
                        shareUrl = 'https://twitter.com/intent/tweet?url=' + encodeURIComponent(url) + '&text=' + encodeURIComponent(title);
                        break;
                    case 'pinterest':
                        var img = $(this).closest('.gallery-item').find('img').attr('src');
                        shareUrl = 'https://pinterest.com/pin/create/button/?url=' + encodeURIComponent(url) + '&media=' + encodeURIComponent(img) + '&description=' + encodeURIComponent(title);
                        break;
                }

                if (shareUrl) {
                    window.open(shareUrl, '_blank', 'width=600,height=400');
                }
            });
        },

        //---------- Lazy Loading ----------
        _initLazyLoading: function () {
            var self = this;
            var page = 1;
            var loading = false;

            $(window).on('scroll', _.throttle(function() {
                if (loading) return;

                var scrollHeight = $(document).height();
                var scrollPosition = $(window).height() + $(window).scrollTop();

                if ((scrollHeight - scrollPosition) / scrollHeight < 0.2) {
                    loading = true;
                    page++;

                    self._rpc({
                        route: '/photobook/gallery/items',
                        params: { page: page }
                    }).then(function(result) {
                        if (result.items.length) {
                            self.items = self.items.concat(result.items);
                            self._renderItems();
                        }
                        loading = false;
                    });
                }
            }, 500));
        }
    });

    publicWidget.registry.GalleryViewer = GalleryViewer;
    return GalleryViewer;
});
