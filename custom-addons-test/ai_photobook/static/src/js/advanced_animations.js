odoo.define('ai_photobook.advanced_animations', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');
    var _t = core._t;

    var AdvancedAnimations = publicWidget.Widget.extend({
        selector: 'body',

        init: function () {
            this._super.apply(this, arguments);
            this.isInitialized = false;
            this.animationElements = [];
        },

        start: function () {
            this._super.apply(this, arguments);
            this._initScrollAnimations();
            this._initParallax();
            this._initCursorEffects();
            this._initPageTransitions();
            this._initTextAnimations();
            return this._loadAnimationAssets();
        },

        //---------- Scroll Animations ----------
        _initScrollAnimations: function () {
            // Initialize ScrollMagic
            this.controller = new ScrollMagic.Controller();

            // Fade In animations
            $('.fade-in-element').each((index, element) => {
                new ScrollMagic.Scene({
                    triggerElement: element,
                    triggerHook: 0.85,
                    reverse: false
                })
                .setClassToggle(element, 'visible')
                .addTo(this.controller);
            });

            // Slide Up animations
            $('.slide-up-element').each((index, element) => {
                new ScrollMagic.Scene({
                    triggerElement: element,
                    triggerHook: 0.8,
                    reverse: false
                })
                .setTween(TweenMax.from(element, 1, {
                    y: 50,
                    opacity: 0,
                    ease: Power3.easeOut
                }))
                .addTo(this.controller);
            });

            // Stagger animations for lists
            $('.stagger-list').each((index, element) => {
                const items = $(element).find('.stagger-item').toArray();
                new ScrollMagic.Scene({
                    triggerElement: element,
                    triggerHook: 0.8,
                    reverse: false
                })
                .setTween(TweenMax.staggerFrom(items, 0.8, {
                    y: 30,
                    opacity: 0,
                    ease: Power3.easeOut
                }, 0.2))
                .addTo(this.controller);
            });
        },

        //---------- Parallax Effects ----------
        _initParallax: function () {
            $('.parallax-section').each((index, element) => {
                new ScrollMagic.Scene({
                    triggerElement: element,
                    triggerHook: 1,
                    duration: '200%'
                })
                .setTween(TweenMax.from(element, 1, {
                    y: '-30%',
                    ease: Linear.easeNone
                }))
                .addTo(this.controller);
            });

            // Parallax elements
            $('.parallax-element').each((index, element) => {
                const speed = $(element).data('parallax-speed') || 0.5;
                new ScrollMagic.Scene({
                    triggerElement: element,
                    triggerHook: 1,
                    duration: '200%'
                })
                .setTween(TweenMax.to(element, 1, {
                    y: `${speed * 100}%`,
                    ease: Linear.easeNone
                }))
                .addTo(this.controller);
            });
        },

        //---------- Cursor Effects ----------
        _initCursorEffects: function () {
            // Custom cursor
            const cursor = $('<div class="custom-cursor"></div>');
            const cursorFollower = $('<div class="cursor-follower"></div>');
            $('body').append(cursor).append(cursorFollower);

            $(document).on('mousemove', (e) => {
                TweenMax.to(cursor, 0, {
                    x: e.clientX,
                    y: e.clientY
                });
                TweenMax.to(cursorFollower, 0.3, {
                    x: e.clientX,
                    y: e.clientY
                });
            });

            // Hover effects
            $('a, button, .clickable').hover(
                function() {
                    cursor.addClass('active');
                    cursorFollower.addClass('active');
                },
                function() {
                    cursor.removeClass('active');
                    cursorFollower.removeClass('active');
                }
            );
        },

        //---------- Page Transitions ----------
        _initPageTransitions: function () {
            // Page transition overlay
            const overlay = $('<div class="page-transition-overlay"></div>');
            $('body').append(overlay);

            // Handle internal links
            $('a[href^="/"]:not([target="_blank"])').click(function(e) {
                e.preventDefault();
                const href = $(this).attr('href');

                // Animate overlay
                TweenMax.to(overlay, 0.5, {
                    opacity: 1,
                    onComplete: () => {
                        window.location = href;
                    }
                });
            });

            // Hide overlay on page load
            $(window).on('load', () => {
                TweenMax.to(overlay, 0.5, {
                    opacity: 0
                });
            });
        },

        //---------- Text Animations ----------
        _initTextAnimations: function () {
            // Split text into characters
            $('.animate-text').each((index, element) => {
                const text = $(element).text();
                const chars = text.split('');
                
                $(element).empty();
                chars.forEach(char => {
                    $(element).append(`<span class="char">${char}</span>`);
                });

                // Animate characters
                new ScrollMagic.Scene({
                    triggerElement: element,
                    triggerHook: 0.8,
                    reverse: false
                })
                .setTween(TweenMax.staggerFrom($(element).find('.char'), 0.8, {
                    y: 20,
                    opacity: 0,
                    ease: Back.easeOut
                }, 0.03))
                .addTo(this.controller);
            });
        },

        //---------- Loading Animations ----------
        _loadAnimationAssets: function () {
            return new Promise((resolve) => {
                // Preload images
                const images = $('img').toArray();
                let loadedImages = 0;

                images.forEach(img => {
                    if (img.complete) {
                        loadedImages++;
                    } else {
                        img.addEventListener('load', () => {
                            loadedImages++;
                            if (loadedImages === images.length) {
                                this._showPage();
                                resolve();
                            }
                        });
                    }
                });

                // Show page if all images are already loaded
                if (loadedImages === images.length) {
                    this._showPage();
                    resolve();
                }
            });
        },

        _showPage: function () {
            const loader = $('.page-loader');
            
            TweenMax.to(loader, 0.5, {
                opacity: 0,
                onComplete: () => {
                    loader.remove();
                    this._animatePageElements();
                }
            });
        },

        _animatePageElements: function () {
            // Animate hero section
            TweenMax.from('.hero-content', 1, {
                y: 50,
                opacity: 0,
                ease: Power3.easeOut
            });

            // Animate navigation
            TweenMax.staggerFrom('.nav-item', 0.6, {
                y: -20,
                opacity: 0,
                ease: Power3.easeOut
            }, 0.1);

            // Initialize scroll animations
            this.isInitialized = true;
        }
    });

    publicWidget.registry.AdvancedAnimations = AdvancedAnimations;
    return AdvancedAnimations;
});
