odoo.define('ai_photobook.editor', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');
    var _t = core._t;

    var PhotobookEditor = publicWidget.Widget.extend({
        selector: '.editor-content',
        events: {
            'click .layout-option': '_onLayoutClick',
            'click .effect-btn': '_onEffectClick',
            'click .add-text': '_onAddTextClick',
            'click .upload-photos': '_onUploadClick',
            'dragover .photo-upload-area': '_onDragOver',
            'drop .photo-upload-area': '_onDrop',
            'click .preview-btn': '_onPreviewClick',
            'click .save-btn': '_onSaveClick',
            'click .undo-btn': '_onUndoClick',
            'click .redo-btn': '_onRedoClick',
            'click .order-btn': '_onOrderClick'
        },

        init: function () {
            this._super.apply(this, arguments);
            this.canvas = null;
            this.currentPage = 1;
            this.totalPages = 20;
            this.undoStack = [];
            this.redoStack = [];
            this.zoomLevel = 100;
        },

        start: function () {
            this._super.apply(this, arguments);
            this._initCanvas();
            this._initDragAndDrop();
            this._loadTemplates();
            this._bindKeyboardShortcuts();
            return this._loadSavedState();
        },

        //---------- Canvas Initialization ----------
        _initCanvas: function () {
            this.canvas = new fabric.Canvas('photobook-canvas', {
                width: 800,
                height: 600,
                backgroundColor: '#ffffff'
            });

            // Enable object selection and manipulation
            this.canvas.on('object:modified', this._onCanvasModified.bind(this));
            this.canvas.on('selection:created', this._onSelectionCreated.bind(this));
            this.canvas.on('selection:cleared', this._onSelectionCleared.bind(this));

            // Initialize with empty page
            this._createNewPage();
        },

        _createNewPage: function () {
            this.canvas.clear();
            this.canvas.backgroundColor = '#ffffff';
            this._saveState();
        },

        //---------- Layout Management ----------
        _onLayoutClick: function (ev) {
            var layout = $(ev.currentTarget).data('layout');
            this._applyLayout(layout);
        },

        _applyLayout: function (layout) {
            // Clear current layout
            this.canvas.clear();

            switch (layout) {
                case '1':
                    this._createSinglePhotoLayout();
                    break;
                case '2':
                    this._createTwoPhotoLayout();
                    break;
                case '3':
                    this._createThreePhotoLayout();
                    break;
            }

            this._saveState();
        },

        _createSinglePhotoLayout: function () {
            var rect = new fabric.Rect({
                left: 100,
                top: 100,
                width: 600,
                height: 400,
                fill: '#f0f0f0',
                stroke: '#cccccc',
                strokeWidth: 1
            });
            this.canvas.add(rect);
            this.canvas.renderAll();
        },

        //---------- Photo Management ----------
        _onUploadClick: function () {
            $('<input type="file" multiple accept="image/*">')
                .on('change', this._handleFileSelect.bind(this))
                .click();
        },

        _handleFileSelect: function (ev) {
            var files = ev.target.files;
            this._processFiles(files);
        },

        _processFiles: function (files) {
            Array.from(files).forEach(file => {
                if (!file.type.match('image.*')) {
                    return;
                }

                var reader = new FileReader();
                reader.onload = (e) => {
                    fabric.Image.fromURL(e.target.result, (img) => {
                        // Scale image to fit canvas
                        var scale = Math.min(
                            200 / img.width,
                            200 / img.height
                        );
                        img.scale(scale);
                        
                        // Add to canvas and thumbnail panel
                        this._addPhotoToCanvas(img);
                        this._addPhotoToPanel(e.target.result);
                    });
                };
                reader.readAsDataURL(file);
            });
        },

        _addPhotoToCanvas: function (img) {
            this.canvas.add(img);
            this.canvas.renderAll();
            this._saveState();
        },

        _addPhotoToPanel: function (dataUrl) {
            var $thumbnail = $('<div class="photo-thumbnail">')
                .append($('<img>').attr('src', dataUrl))
                .draggable({
                    helper: 'clone',
                    revert: 'invalid'
                });
            
            $('.uploaded-photos').append($thumbnail);
        },

        //---------- Effects Management ----------
        _onEffectClick: function (ev) {
            var effect = $(ev.currentTarget).data('effect');
            var activeObject = this.canvas.getActiveObject();
            
            if (!activeObject) return;

            switch (effect) {
                case 'grayscale':
                    this._applyGrayscale(activeObject);
                    break;
                case 'sepia':
                    this._applySepia(activeObject);
                    break;
                case 'vintage':
                    this._applyVintage(activeObject);
                    break;
                default:
                    this._removeFilters(activeObject);
            }

            this.canvas.renderAll();
            this._saveState();
        },

        _applyGrayscale: function (obj) {
            obj.filters = [new fabric.Image.filters.Grayscale()];
            obj.applyFilters();
        },

        //---------- Text Management ----------
        _onAddTextClick: function () {
            var text = new fabric.IText('Double click to edit', {
                left: 100,
                top: 100,
                fontFamily: 'Arial',
                fontSize: 20,
                fill: '#000000'
            });
            
            this.canvas.add(text);
            this.canvas.setActiveObject(text);
            this.canvas.renderAll();
            this._saveState();
        },

        //---------- State Management ----------
        _saveState: function () {
            var json = JSON.stringify(this.canvas.toJSON());
            this.undoStack.push(json);
            this.redoStack = [];
            this._updateUndoRedoButtons();
        },

        _onUndoClick: function () {
            if (this.undoStack.length > 1) {
                var currentState = this.undoStack.pop();
                this.redoStack.push(currentState);
                var previousState = this.undoStack[this.undoStack.length - 1];
                this._loadState(previousState);
            }
        },

        _onRedoClick: function () {
            if (this.redoStack.length > 0) {
                var nextState = this.redoStack.pop();
                this.undoStack.push(nextState);
                this._loadState(nextState);
            }
        },

        _loadState: function (state) {
            this.canvas.loadFromJSON(state, () => {
                this.canvas.renderAll();
                this._updateUndoRedoButtons();
            });
        },

        _updateUndoRedoButtons: function () {
            $('.undo-btn').toggleClass('disabled', this.undoStack.length <= 1);
            $('.redo-btn').toggleClass('disabled', this.redoStack.length === 0);
        },

        //---------- Preview & Save ----------
        _onPreviewClick: function () {
            var preview = this._generatePreview();
            $('.preview-container').html(preview);
            $('#previewModal').modal('show');
        },

        _generatePreview: function () {
            return $('<img>')
                .attr('src', this.canvas.toDataURL())
                .addClass('img-fluid');
        },

        _onSaveClick: function () {
            var data = {
                canvas_data: this.canvas.toJSON(),
                page: this.currentPage
            };

            this._rpc({
                route: '/photobook/save',
                params: data
            }).then(function (result) {
                if (result.success) {
                    this.displayNotification({
                        type: 'success',
                        title: _t('Saved'),
                        message: _t('Your changes have been saved successfully.')
                    });
                }
            });
        },

        //---------- Order Management ----------
        _onOrderClick: function () {
            // Generate preview images for all pages
            var previews = this._generatePagePreviews();
            
            // Save to order data
            this._rpc({
                route: '/photobook/prepare_order',
                params: {
                    previews: previews,
                    canvas_data: this._getAllPagesData()
                }
            }).then(function (result) {
                if (result.success) {
                    window.location = '/shop/cart';
                }
            });
        },

        //---------- Utility Functions ----------
        _bindKeyboardShortcuts: function () {
            $(document).on('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'z':
                            e.preventDefault();
                            if (e.shiftKey) {
                                this._onRedoClick();
                            } else {
                                this._onUndoClick();
                            }
                            break;
                        case 's':
                            e.preventDefault();
                            this._onSaveClick();
                            break;
                    }
                }
            });
        },

        destroy: function () {
            this.canvas.dispose();
            $(document).off('keydown');
            this._super.apply(this, arguments);
        }
    });

    publicWidget.registry.PhotobookEditor = PhotobookEditor;
    return PhotobookEditor;
});
