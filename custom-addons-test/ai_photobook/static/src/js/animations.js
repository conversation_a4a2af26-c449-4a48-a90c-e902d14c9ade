odoo.define('ai_photobook.animations', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');
    var _t = core._t;

    // Scroll Reveal Animation
    publicWidget.registry.ScrollReveal = publicWidget.Widget.extend({
        selector: '.reveal-on-scroll',
        
        start: function () {
            this._super.apply(this, arguments);
            this._bindScrollEvent();
            this._checkVisibility();
        },
        
        _bindScrollEvent: function () {
            var self = this;
            $(window).on('scroll.reveal', _.throttle(function () {
                self._checkVisibility();
            }, 100));
        },
        
        _checkVisibility: function () {
            var self = this;
            $('.reveal-on-scroll').each(function () {
                var $element = $(this);
                if (self._isElementInViewport($element) && !$element.hasClass('revealed')) {
                    $element.addClass('revealed');
                }
            });
        },
        
        _isElementInViewport: function ($element) {
            var rect = $element[0].getBoundingClientRect();
            return (
                rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
                rect.bottom >= 0
            );
        },
        
        destroy: function () {
            $(window).off('scroll.reveal');
            this._super.apply(this, arguments);
        }
    });

    // Custom Cursor
    publicWidget.registry.CustomCursor = publicWidget.Widget.extend({
        selector: 'body',
        
        start: function () {
            this._super.apply(this, arguments);
            this._createCursor();
            this._bindCursorEvents();
        },
        
        _createCursor: function () {
            this.$cursor = $('<div class="custom-cursor"></div>');
            $('body').append(this.$cursor);
        },
        
        _bindCursorEvents: function () {
            var self = this;
            $(document).on('mousemove.cursor', function (e) {
                self.$cursor.css({
                    left: e.clientX - 10,
                    top: e.clientY - 10
                });
            });

            $('a, button, .clickable').on('mouseenter.cursor', function () {
                self.$cursor.addClass('hover');
            }).on('mouseleave.cursor', function () {
                self.$cursor.removeClass('hover');
            });
        },
        
        destroy: function () {
            $(document).off('mousemove.cursor');
            $('a, button, .clickable').off('mouseenter.cursor mouseleave.cursor');
            this.$cursor.remove();
            this._super.apply(this, arguments);
        }
    });

    // Smooth Scroll
    publicWidget.registry.SmoothScroll = publicWidget.Widget.extend({
        selector: 'a[href*="#"]:not([href="#"])',
        
        events: {
            'click': '_onClickScroll'
        },
        
        _onClickScroll: function (ev) {
            var $target = $(this.$el.attr('href'));
            if ($target.length) {
                ev.preventDefault();
                $('html, body').animate({
                    scrollTop: $target.offset().top - 100
                }, 1000, 'easeInOutQuart');
            }
        }
    });

    // Parallax Effect
    publicWidget.registry.ParallaxBackground = publicWidget.Widget.extend({
        selector: '.parallax-section',
        
        start: function () {
            this._super.apply(this, arguments);
            this._bindParallaxScroll();
        },
        
        _bindParallaxScroll: function () {
            var self = this;
            $(window).on('scroll.parallax', _.throttle(function () {
                self._updateParallax();
            }, 20));
        },
        
        _updateParallax: function () {
            var scrolled = $(window).scrollTop();
            this.$el.css('background-position-y', -(scrolled * 0.5) + 'px');
        },
        
        destroy: function () {
            $(window).off('scroll.parallax');
            this._super.apply(this, arguments);
        }
    });

    // Image Gallery
    publicWidget.registry.ImageGallery = publicWidget.Widget.extend({
        selector: '.gallery-grid',
        
        events: {
            'click .gallery-item': '_onClickGalleryItem'
        },
        
        start: function () {
            this._super.apply(this, arguments);
            this._initializeMasonry();
        },
        
        _initializeMasonry: function () {
            this.$el.masonry({
                itemSelector: '.gallery-item',
                columnWidth: '.gallery-item',
                percentPosition: true
            });
        },
        
        _onClickGalleryItem: function (ev) {
            var $item = $(ev.currentTarget);
            var $image = $item.find('img');
            
            // Create lightbox
            var $lightbox = $('<div class="gallery-lightbox">').append(
                $('<img>', {
                    src: $image.attr('src'),
                    alt: $image.attr('alt')
                })
            ).appendTo('body');
            
            // Add close functionality
            $lightbox.on('click', function () {
                $lightbox.fadeOut(function () {
                    $lightbox.remove();
                });
            });
            
            // Show lightbox
            $lightbox.fadeIn();
        }
    });

    // Product Quick View
    publicWidget.registry.ProductQuickView = publicWidget.Widget.extend({
        selector: '.product-card',
        
        events: {
            'click .quick-view-btn': '_onClickQuickView'
        },
        
        _onClickQuickView: function (ev) {
            ev.preventDefault();
            var self = this;
            var productId = this.$el.data('product-id');
            
            this._rpc({
                route: '/shop/product/quick-view',
                params: {
                    product_id: productId
                },
            }).then(function (modal) {
                var $modal = $(modal);
                $modal.modal({
                    backdrop: 'static',
                    keyboard: false
                });
                
                // Initialize product gallery in modal
                self._initProductGallery($modal);
            });
        },
        
        _initProductGallery: function ($modal) {
            $modal.find('.product-image-gallery').owlCarousel({
                items: 1,
                loop: true,
                nav: true,
                dots: true,
                autoplay: true,
                autoplayTimeout: 5000
            });
        }
    });
});
