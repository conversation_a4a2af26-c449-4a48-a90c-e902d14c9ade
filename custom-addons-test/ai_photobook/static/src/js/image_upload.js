odoo.define('ai_photobook.image_upload', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');
    var _t = core._t;

    publicWidget.registry.PhotobookImageUpload = publicWidget.Widget.extend({
        selector: '.photobook-image-upload',
        events: {
            'change .image-upload-input': '_onFileSelect',
            'dragover .upload-zone': '_onDragOver',
            'dragleave .upload-zone': '_onDragLeave',
            'drop .upload-zone': '_onDrop',
            'click .upload-zone': '_onClickUpload'
        },

        start: function () {
            this.uploadedImages = [];
            this.maxImages = parseInt(this.$el.data('max-images')) || 50;
            return this._super.apply(this, arguments);
        },

        _onFileSelect: function (ev) {
            this._processFiles(ev.target.files);
        },

        _onDragOver: function (ev) {
            ev.preventDefault();
            ev.stopPropagation();
            this.$('.upload-zone').addClass('dragover');
        },

        _onDragLeave: function (ev) {
            ev.preventDefault();
            ev.stopPropagation();
            this.$('.upload-zone').removeClass('dragover');
        },

        _onDrop: function (ev) {
            ev.preventDefault();
            ev.stopPropagation();
            this.$('.upload-zone').removeClass('dragover');
            this._processFiles(ev.originalEvent.dataTransfer.files);
        },

        _onClickUpload: function () {
            this.$('.image-upload-input').click();
        },

        _processFiles: function (files) {
            var self = this;
            var remainingSlots = this.maxImages - this.uploadedImages.length;
            
            if (remainingSlots <= 0) {
                this._showError(_t('Maximum number of images reached'));
                return;
            }

            Array.from(files).slice(0, remainingSlots).forEach(function (file) {
                if (!file.type.match('image.*')) {
                    self._showError(_t('Please upload only image files'));
                    return;
                }

                if (file.size > 10 * 1024 * 1024) { // 10MB limit
                    self._showError(_t('Image size should not exceed 10MB'));
                    return;
                }

                var reader = new FileReader();
                reader.onload = function (e) {
                    self._addImagePreview(e.target.result, file.name);
                };
                reader.readAsDataURL(file);
            });
        },

        _addImagePreview: function (dataUrl, filename) {
            var $preview = $(core.qweb.render('ai_photobook.image_preview', {
                src: dataUrl,
                filename: filename
            }));
            
            this.$('.image-previews').append($preview);
            this.uploadedImages.push({
                dataUrl: dataUrl,
                filename: filename
            });

            this._updateCounter();
        },

        _updateCounter: function () {
            this.$('.upload-counter').text(
                _t('%s of %s images uploaded',
                    this.uploadedImages.length,
                    this.maxImages
                )
            );
        },

        _showError: function (message) {
            this.$('.upload-error')
                .text(message)
                .removeClass('d-none')
                .delay(3000)
                .fadeOut();
        }
    });

    return publicWidget.registry.PhotobookImageUpload;
});
