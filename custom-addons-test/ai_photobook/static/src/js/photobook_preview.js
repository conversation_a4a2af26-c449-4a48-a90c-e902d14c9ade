odoo.define('ai_photobook.preview', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');
    var _t = core._t;

    publicWidget.registry.PhotobookPreview = publicWidget.Widget.extend({
        selector: '.photobook-preview',
        events: {
            'click .preview-next': '_onNextPage',
            'click .preview-prev': '_onPrevPage',
            'click .preview-fullscreen': '_onFullscreen'
        },

        start: function () {
            this.currentPage = 0;
            this.totalPages = this.$('.preview-page').length;
            this._updateNavigation();
            return this._super.apply(this, arguments);
        },

        _onNextPage: function (ev) {
            ev.preventDefault();
            if (this.currentPage < this.totalPages - 1) {
                this.currentPage++;
                this._updateNavigation();
            }
        },

        _onPrevPage: function (ev) {
            ev.preventDefault();
            if (this.currentPage > 0) {
                this.currentPage--;
                this._updateNavigation();
            }
        },

        _onFullscreen: function (ev) {
            ev.preventDefault();
            this.$el.toggleClass('fullscreen');
            if (this.$el.hasClass('fullscreen')) {
                $(document.body).css('overflow', 'hidden');
            } else {
                $(document.body).css('overflow', '');
            }
        },

        _updateNavigation: function () {
            this.$('.preview-page').removeClass('active');
            this.$(`.preview-page:eq(${this.currentPage})`).addClass('active');
            
            this.$('.preview-prev').toggleClass('disabled', this.currentPage === 0);
            this.$('.preview-next').toggleClass('disabled', this.currentPage === this.totalPages - 1);
            
            this.$('.preview-counter').text(
                _t('Page %s of %s', this.currentPage + 1, this.totalPages)
            );
        },
    });

    return publicWidget.registry.PhotobookPreview;
});
