<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Blog List Template -->
    <template id="blog_list" name="Blog List">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure">
                <!-- Hero Section -->
                <section class="blog-hero parallax-section" style="background-image: url('/ai_photobook/static/src/img/blog-hero-bg.jpg')">
                    <div class="container py-5">
                        <div class="row justify-content-center">
                            <div class="col-lg-8 text-center">
                                <h1 class="display-4 text-white mb-4 animate-fade-in">Photobook Stories</h1>
                                <p class="lead text-white mb-4 animate-slide-up">Tips, inspiration, and stories about preserving memories</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Blog Content -->
                <section class="blog-content py-5">
                    <div class="container">
                        <div class="row">
                            <!-- Main Content -->
                            <div class="col-lg-8">
                                <!-- Featured Post -->
                                <t t-if="featured_post">
                                    <article class="featured-post mb-5 reveal-on-scroll">
                                        <div class="card">
                                            <img t-att-src="featured_post.cover_properties" class="card-img-top" t-att-alt="featured_post.name"/>
                                            <div class="card-body">
                                                <div class="post-meta text-muted mb-2">
                                                    <span class="post-date">
                                                        <i class="fa fa-calendar-alt"></i>
                                                        <t t-esc="featured_post.post_date"/>
                                                    </span>
                                                    <span class="post-author ml-3">
                                                        <i class="fa fa-user"></i>
                                                        <t t-esc="featured_post.author_id.name"/>
                                                    </span>
                                                </div>
                                                <h2 class="card-title">
                                                    <a t-att-href="'/blog/%s/%s' % (featured_post.blog_id.id, featured_post.id)">
                                                        <t t-esc="featured_post.name"/>
                                                    </a>
                                                </h2>
                                                <p class="card-text">
                                                    <t t-esc="featured_post.subtitle"/>
                                                </p>
                                                <a t-att-href="'/blog/%s/%s' % (featured_post.blog_id.id, featured_post.id)" class="btn btn-primary">
                                                    Read More
                                                </a>
                                            </div>
                                        </div>
                                    </article>
                                </t>

                                <!-- Blog Posts Grid -->
                                <div class="row">
                                    <t t-foreach="blog_posts" t-as="post">
                                        <div class="col-md-6 mb-4">
                                            <article class="post-card reveal-on-scroll">
                                                <div class="card h-100">
                                                    <img t-att-src="post.cover_properties" class="card-img-top" t-att-alt="post.name"/>
                                                    <div class="card-body">
                                                        <div class="post-meta text-muted mb-2">
                                                            <span class="post-date">
                                                                <i class="fa fa-calendar-alt"></i>
                                                                <t t-esc="post.post_date"/>
                                                            </span>
                                                        </div>
                                                        <h3 class="card-title h5">
                                                            <a t-att-href="'/blog/%s/%s' % (post.blog_id.id, post.id)">
                                                                <t t-esc="post.name"/>
                                                            </a>
                                                        </h3>
                                                        <p class="card-text">
                                                            <t t-esc="post.subtitle"/>
                                                        </p>
                                                    </div>
                                                    <div class="card-footer bg-white border-0">
                                                        <a t-att-href="'/blog/%s/%s' % (post.blog_id.id, post.id)" class="btn btn-link px-0">
                                                            Read More <i class="fa fa-arrow-right ml-2"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </article>
                                        </div>
                                    </t>
                                </div>

                                <!-- Pagination -->
                                <nav class="mt-4" t-if="pager">
                                    <t t-call="website.pager"/>
                                </nav>
                            </div>

                            <!-- Sidebar -->
                            <div class="col-lg-4">
                                <!-- Search -->
                                <div class="sidebar-widget mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <form t-att-action="'/blog/search'" method="get">
                                                <div class="input-group">
                                                    <input type="text" name="search" class="form-control" placeholder="Search posts..."/>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-primary" type="submit">
                                                            <i class="fa fa-search"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Categories -->
                                <div class="sidebar-widget mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="widget-title">Categories</h4>
                                            <ul class="list-unstyled">
                                                <t t-foreach="categories" t-as="category">
                                                    <li class="mb-2">
                                                        <a t-att-href="'/blog/category/%s' % category.id">
                                                            <t t-esc="category.name"/>
                                                            <span class="badge badge-primary float-right">
                                                                <t t-esc="category.post_count"/>
                                                            </span>
                                                        </a>
                                                    </li>
                                                </t>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- Popular Posts -->
                                <div class="sidebar-widget mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="widget-title">Popular Posts</h4>
                                            <t t-foreach="popular_posts" t-as="post">
                                                <div class="media mb-3">
                                                    <img t-att-src="post.cover_properties" class="mr-3" style="width: 64px; height: 64px; object-fit: cover;" t-att-alt="post.name"/>
                                                    <div class="media-body">
                                                        <h6 class="mt-0">
                                                            <a t-att-href="'/blog/%s/%s' % (post.blog_id.id, post.id)">
                                                                <t t-esc="post.name"/>
                                                            </a>
                                                        </h6>
                                                        <small class="text-muted">
                                                            <t t-esc="post.post_date"/>
                                                        </small>
                                                    </div>
                                                </div>
                                            </t>
                                        </div>
                                    </div>
                                </div>

                                <!-- Newsletter -->
                                <div class="sidebar-widget mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="widget-title">Newsletter</h4>
                                            <p>Subscribe to get the latest updates</p>
                                            <form t-att-action="'/blog/subscribe'" method="post">
                                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                                <div class="form-group">
                                                    <input type="email" name="email" class="form-control" placeholder="Your email address" required="required"/>
                                                </div>
                                                <button type="submit" class="btn btn-primary btn-block">Subscribe</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>

    <!-- Blog Post Detail Template -->
    <template id="blog_post_detail" name="Blog Post Detail">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure">
                <!-- Post Header -->
                <header class="post-header py-5" t-attf-style="background-image: url(#{post.cover_properties})">
                    <div class="container">
                        <div class="row justify-content-center">
                            <div class="col-lg-8 text-center">
                                <div class="post-meta text-white mb-2">
                                    <span class="post-date">
                                        <i class="fa fa-calendar-alt"></i>
                                        <t t-esc="post.post_date"/>
                                    </span>
                                    <span class="post-author ml-3">
                                        <i class="fa fa-user"></i>
                                        <t t-esc="post.author_id.name"/>
                                    </span>
                                </div>
                                <h1 class="text-white mb-4">
                                    <t t-esc="post.name"/>
                                </h1>
                                <p class="lead text-white">
                                    <t t-esc="post.subtitle"/>
                                </p>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Post Content -->
                <section class="post-content py-5">
                    <div class="container">
                        <div class="row justify-content-center">
                            <div class="col-lg-8">
                                <article class="blog-post">
                                    <t t-raw="post.content"/>
                                    
                                    <!-- Tags -->
                                    <div class="post-tags mt-4">
                                        <t t-foreach="post.tag_ids" t-as="tag">
                                            <a t-att-href="'/blog/tag/%s' % tag.id" class="badge badge-light mr-2">
                                                #<t t-esc="tag.name"/>
                                            </a>
                                        </t>
                                    </div>

                                    <!-- Share Buttons -->
                                    <div class="post-share mt-4">
                                        <h5>Share this post</h5>
                                        <div class="social-share">
                                            <a href="#" class="btn btn-outline-primary mr-2">
                                                <i class="fab fa-facebook-f"></i>
                                            </a>
                                            <a href="#" class="btn btn-outline-primary mr-2">
                                                <i class="fab fa-twitter"></i>
                                            </a>
                                            <a href="#" class="btn btn-outline-primary mr-2">
                                                <i class="fab fa-linkedin-in"></i>
                                            </a>
                                            <a href="#" class="btn btn-outline-primary">
                                                <i class="fab fa-pinterest-p"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Author Bio -->
                                    <div class="author-bio mt-5">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="media">
                                                    <img t-att-src="post.author_id.image_1920" class="rounded-circle mr-3" style="width: 80px; height: 80px;" t-att-alt="post.author_id.name"/>
                                                    <div class="media-body">
                                                        <h5 class="mt-0">About <t t-esc="post.author_id.name"/></h5>
                                                        <p t-field="post.author_id.description"/>
                                                        <div class="author-social">
                                                            <a href="#" class="text-primary mr-3">
                                                                <i class="fab fa-twitter"></i>
                                                            </a>
                                                            <a href="#" class="text-primary mr-3">
                                                                <i class="fab fa-linkedin-in"></i>
                                                            </a>
                                                            <a href="#" class="text-primary">
                                                                <i class="fab fa-instagram"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Related Posts -->
                                    <div class="related-posts mt-5">
                                        <h3 class="mb-4">Related Posts</h3>
                                        <div class="row">
                                            <t t-foreach="related_posts" t-as="related">
                                                <div class="col-md-6 mb-4">
                                                    <div class="card h-100">
                                                        <img t-att-src="related.cover_properties" class="card-img-top" t-att-alt="related.name"/>
                                                        <div class="card-body">
                                                            <h5 class="card-title">
                                                                <a t-att-href="'/blog/%s/%s' % (related.blog_id.id, related.id)">
                                                                    <t t-esc="related.name"/>
                                                                </a>
                                                            </h5>
                                                            <p class="card-text">
                                                                <t t-esc="related.subtitle"/>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Comments -->
                                    <div class="post-comments mt-5">
                                        <h3 class="mb-4">Comments</h3>
                                        <div class="comments-list">
                                            <t t-foreach="post.message_ids" t-as="comment">
                                                <div class="media mb-4">
                                                    <img t-att-src="comment.author_id.image_1920" class="rounded-circle mr-3" style="width: 50px; height: 50px;" t-att-alt="comment.author_id.name"/>
                                                    <div class="media-body">
                                                        <h5 class="mt-0">
                                                            <t t-esc="comment.author_id.name"/>
                                                            <small class="text-muted ml-2">
                                                                <t t-esc="comment.date"/>
                                                            </small>
                                                        </h5>
                                                        <p t-field="comment.body"/>
                                                    </div>
                                                </div>
                                            </t>
                                        </div>

                                        <!-- Comment Form -->
                                        <div class="comment-form mt-4">
                                            <h4>Leave a Comment</h4>
                                            <form t-att-action="'/blog/%s/%s/comment' % (post.blog_id.id, post.id)" method="post">
                                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                                <div class="form-group">
                                                    <label for="comment">Your Comment</label>
                                                    <textarea name="comment" id="comment" class="form-control" rows="4" required="required"></textarea>
                                                </div>
                                                <button type="submit" class="btn btn-primary">Post Comment</button>
                                            </form>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>
</odoo>
