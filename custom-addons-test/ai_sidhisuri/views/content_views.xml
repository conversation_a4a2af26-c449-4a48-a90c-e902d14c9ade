<odoo>
    <!-- Form View for Content -->
    <record id="view_sidhisuri_content_form" model="ir.ui.view">
        <field name="name">sidhisuri.content.form</field>
        <field name="model">sidhisuri.content</field>
        <field name="arch" type="xml">
            <form string="Content">
                <sheet>
                    <group>
                        <field name="x_name"/>
                        <field name="x_photo"/>
                        <field name="x_video"/>
                        <field name="x_audio"/>
                        <field name="x_message"/>
                        <field name="x_min_age"/>
                        <field name="x_max_age"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree View for Content -->
    <record id="view_sidhisuri_content_tree" model="ir.ui.view">
        <field name="name">sidhisuri.content.tree</field>
        <field name="model">sidhisuri.content</field>
        <field name="arch" type="xml">
            <list string="Content">
                <field name="x_name"/>
                <field name="x_photo"/>
                <field name="x_video"/>
                <field name="x_audio"/>
                <field name="x_min_age"/>
                <field name="x_max_age"/>
            </list>
        </field>
    </record>

    <!-- Action to Open Content -->
    <record id="action_sidhisuri_content" model="ir.actions.act_window">
        <field name="name">Content</field>
        <field name="res_model">sidhisuri.content</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- Menu for Content -->
    <menuitem id="menu_sidhisuri_content" name="Content"
              parent="base.menu_custom" action="action_sidhisuri_content"/>
</odoo>
