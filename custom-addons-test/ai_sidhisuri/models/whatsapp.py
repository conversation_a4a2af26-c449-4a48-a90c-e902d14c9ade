# whatsapp.py
from odoo import models, fields

class WhatsAppScheduler(models.Model):
    _name = 'sidhisuri.whatsapp'
     _description = 'WhatsApp Scheduler'  # Indentation is incorrect here

    x_id = fields.Char(string='id',required=True)
    x_name = fields.Char(string='Name', required=True)
    x_session_key = fields.Char(string='Session Key')
    x_from_no = fields.Char(string='From No')
    x_to_number = fields.Char(string='To Number')
    x_message = fields.Text(string='Message')
    x_status = fields.Selection([
        ('Planned', 'Planned'), 
        ('In Progress', 'In Progress'),
        ('Successful','Successful'),
        ('Fail','Fail')
    ], default='draft', string='Status')
    x_data_dictionary = fields.Text(string='Data Dictionary')
    x_priority = fields.Integer(string='Priority', default=0)
    x_content_range = fields.Selection([
    ('0-12', '0-12 years'),
    ('13-90', '13-90 years')
], string='Age Range', required=True, default='13-90')

    # File fields
    x_photo = fields.Binary(string='Photo', attachment=True)
    x_audio = fields.Binary(string='Audio', attachment=True)
    x_video = fields.Binary(string='Video', attachment=True)
    
    # Additional fields
    x_photo_url = fields.Char(string='Photo URL')
    x_file_name = fields.Char(string='File Name')
    x_file_extension = fields.Selection([
        ('jpg', 'JPG'),
        ('png', 'PNG'),
        ('mp4', 'MP4'),
        ('mp3', 'MP3'),
        ('pdf', 'PDF')
    ], string='File Extension')
    x_post_time = fields.Datetime(string='Post Time')
    x_sender_client_ref = fields.Selection([
        ('Alpa shah', 'Alpa shah'),
        ('Amar shah', 'Amar shah')
    ], string='Sender Client Ref')
    x_send_contact = fields.Char(string='Send Contact (String/Array)')
    x_partner_vcf = fields.Char(string='Partner To (VCF Generation)')
   
