from odoo import models, fields

class ParnaRegistration(models.Model):
    _name = 'sidhisuri.parna'
    _description = 'Parna Registration'

    x_tapasvi_family_name = fields.Char(string="Tapasvi Family Name", required=True)
    x_name_of_sangh = fields.Char(string="Name of Sangh")
    x_name_of_firm = fields.Char(string="Name of Firm")
    x_email = fields.Char(string="Email")
    x_mobile_number = fields.Char(string="Mobile Number")
    x_good_day_of_parna = fields.Date(string="Good Day of Parna")
    x_good_time_of_parna = fields.Char(string="Good Time of Parna")
    x_good_place_of_parna = fields.Char(string="Good Place of Parna")

    x_good_time_of_meal = fields.Selection([
        ('morning', 'Morning'),
        ('afternoon', 'Afternoon'),
        ('evening', 'Evening'),
    ], string="Good Time of Meal", default='morning')
