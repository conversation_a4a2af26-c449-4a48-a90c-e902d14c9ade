from odoo import models, fields

class <PERSON><PERSON><PERSON><PERSON>hatsapp(models.Model):
    _name = "sidhisuri.whatsapp"  # Must match external ID in ir.model.access.csv
    _description = "Sidhisuri Whatsapp"

    name = fields.Char(string="Name", required=True)
    phone_number = fields.Char(string="Phone Number")
    message = fields.Text(string="Message")
    sent = fields.Boolean(string="Sent", default=False)
