document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let searchTimeout = null;
    let originalContent = null;
    let categorySection = null;
    let searchResultsContainer = null;
    let currentPage = 1;
    let itemsPerPage = 20;
    let products = [];

    // Initialize the search functionality
    function initSearch() {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'bearing-search-container';
        searchContainer.innerHTML = `
            <div class="search-box">
                <i class="fa fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search by Housing Designation or Compatible Bearing...">
                <i class="fa fa-times search-reset"></i>
            </div>
            <div class="search-results" style="display: none;">
                <div class="results-grid"></div>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .bearing-search-container {
                max-width: 800px;
                margin: 2rem auto;
                padding: 0 1rem;
            }
            .search-box {
                display: flex;
                align-items: center;
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 0.5rem 1rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .search-input {
                flex: 1;
                border: none;
                padding: 0.5rem;
                font-size: 1rem;
                outline: none;
            }
            .search-icon, .search-reset {
                color: #666;
                cursor: pointer;
                padding: 0.5rem;
            }
            .search-icon:hover, .search-reset:hover {
                color: #000;
            }
            .results-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }
            .product-card {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                overflow: hidden;
                transition: transform 0.3s;
            }
            .product-card:hover {
                transform: translateY(-4px);
            }
            .product-card img {
                width: 100%;
                height: 200px;
                object-fit: cover;
            }
            .product-card-content {
                padding: 1.5rem;
            }
            .product-card h3 {
                margin: 0 0 1rem;
                font-size: 1.25rem;
            }
            .product-card p {
                color: #666;
                margin-bottom: 1rem;
            }
            .highlight {
                background: #ffd700;
                padding: 0.1em 0.2em;
                border-radius: 2px;
            }
            .no-results {
                text-align: center;
                padding: 2rem;
                color: #666;
            }
            .pagination-controls {
                margin-top: 2rem;
                text-align: center;
            }
            .pagination-controls button {
                margin: 0 0.5rem;
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 4px;
                background: #f0f0f0;
                cursor: pointer;
            }
            .pagination-controls button:hover {
                background: #e0e0e0;
            }
            .pagination-controls button.active {
                background: #007bff;
                color: white;
            }
        `;
        document.head.appendChild(style);

        // Insert search container
        const targetSection = document.querySelector('.s_three_columns, .category-section');
        if (targetSection) {
            targetSection.parentNode.insertBefore(searchContainer, targetSection);
            categorySection = targetSection;
            searchResultsContainer = searchContainer.querySelector('.search-results');

            // Add event listeners
            const searchInput = searchContainer.querySelector('.search-input');
            const resetButton = searchContainer.querySelector('.search-reset');

            searchInput.addEventListener('input', handleSearch);
            resetButton.addEventListener('click', resetSearch);
        }
    }

    // Handle search input
    function handleSearch(event) {
        const searchTerm = event.target.value.trim().toLowerCase();
        
        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set new timeout
        searchTimeout = setTimeout(() => {
            if (searchTerm) {
                performSearch(searchTerm);
            } else {
                resetSearch();
            }
        }, 300);
    }

    // Perform search
    function performSearch(searchTerm) {
        // Save original content if not saved
        if (!originalContent && categorySection) {
            originalContent = categorySection.style.display;
        }

        // Get all products
        products = [];
        document.querySelectorAll('.product-card, .category-card').forEach(card => {
            const title = card.querySelector('h3, .card-title')?.textContent || '';
            const desc = card.querySelector('p, .card-text')?.textContent || '';
            const housing = card.getAttribute('data-housing') || '';
            const compatible = card.getAttribute('data-compatible') || '';
            
            const searchContent = `${title} ${desc} ${housing} ${compatible}`.toLowerCase();
            const terms = searchTerm.split(/\s+/);
            
            // Calculate relevance score
            let score = 0;
            terms.forEach(term => {
                if (searchContent.includes(term)) {
                    score += 1;
                    if (housing.toLowerCase().includes(term) || compatible.toLowerCase().includes(term)) {
                        score += 2;
                    }
                    if (title.toLowerCase().includes(term)) {
                        score += 1;
                    }
                }
            });

            if (score > 0) {
                products.push({
                    element: card.cloneNode(true),
                    score: score
                });
            }
        });

        // Sort by relevance
        products.sort((a, b) => b.score - a.score);

        // Show results
        if (products.length > 0) {
            displayResults(products, searchTerm);
        } else {
            showNoResults();
        }
    }

    // Display search results
    function displayResults(products, searchTerm) {
        const resultsGrid = searchResultsContainer.querySelector('.results-grid');
        resultsGrid.innerHTML = '';

        // Calculate total pages
        const totalPages = Math.ceil(products.length / itemsPerPage);

        // Get the products for the current page
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedProducts = products.slice(startIndex, endIndex);

        paginatedProducts.forEach(({element}) => {
            // Highlight matching text
            const terms = searchTerm.split(/\s+/);
            terms.forEach(term => {
                element.innerHTML = element.innerHTML.replace(
                    new RegExp(term, 'gi'),
                    match => `<span class="highlight">${match}</span>`
                );
            });

            resultsGrid.appendChild(element);
        });

        // Create pagination controls
        createPaginationControls(totalPages);

        // Hide categories, show results
        categorySection.style.display = 'none';
        searchResultsContainer.style.display = 'block';
    }

    // Create pagination controls
    function createPaginationControls(totalPages) {
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'pagination-controls';

        // Previous button
        const prevButton = document.createElement('button');
        prevButton.innerText = 'Previous';
        prevButton.disabled = currentPage === 1;
        prevButton.onclick = () => {
            if (currentPage > 1) {
                currentPage--;
                displayResults(products, '');
            }
        };
        paginationContainer.appendChild(prevButton);

        // Page indicators
        for (let i = 1; i <= totalPages; i++) {
            const pageButton = document.createElement('button');
            pageButton.innerText = i;
            pageButton.className = i === currentPage ? 'active' : '';
            pageButton.onclick = () => {
                currentPage = i;
                displayResults(products, '');
            };
            paginationContainer.appendChild(pageButton);
        }

        // Next button
        const nextButton = document.createElement('button');
        nextButton.innerText = 'Next';
        nextButton.disabled = currentPage === totalPages;
        nextButton.onclick = () => {
            if (currentPage < totalPages) {
                currentPage++;
                displayResults(products, '');
            }
        };
        paginationContainer.appendChild(nextButton);

        // Clear existing pagination and append new
        const existingPagination = searchResultsContainer.querySelector('.pagination-controls');
        if (existingPagination) {
            existingPagination.remove();
        }
        searchResultsContainer.appendChild(paginationContainer);
    }

    // Show no results message
    function showNoResults() {
        const resultsGrid = searchResultsContainer.querySelector('.results-grid');
        resultsGrid.innerHTML = '<div class="no-results">No matching products found</div>';
        
        // Keep categories visible
        categorySection.style.display = originalContent;
        searchResultsContainer.style.display = 'block';
    }

    // Reset search
    function resetSearch() {
        const searchInput = document.querySelector('.search-input');
        searchInput.value = '';
        
        // Restore original view
        if (categorySection) {
            categorySection.style.display = originalContent;
        }
        if (searchResultsContainer) {
            searchResultsContainer.style.display = 'none';
        }
    }

    // Initialize search when DOM is loaded
    initSearch();
});
