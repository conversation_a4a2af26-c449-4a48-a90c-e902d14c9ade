# -*- coding: utf-8 -*-
{
    'name': 'Bearing Search Extension',
    'version': '17.1.1',
    'author': 'Arihant AI',
    'summary': 'Extends bearing websites with advanced search functionality',
    'description': """
        This module extends both ai_bearing_website and ai_jnjbearing_website modules
        to add advanced search functionality including:
        - Intelligent search for bearings and housing designations
        - Relevance-based sorting
        - Word highlighting
        - Smooth transitions
    """,
    'depends': [
        'base',
        'website',
        'ai_bearing_website',
        'ai_jnjbearing_website'
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            '/ai_bearing_search/static/js/bearing_search.js',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
