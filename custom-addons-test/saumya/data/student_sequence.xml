<odoo>
    <!-- Student Menu -->
    <menuitem id="menu_school_root" name="School Management"/>
    <menuitem id="menu_school_students" name="Students" parent="menu_school_root"/>
    
    <!-- Student Form View -->
    <record id="view_student_form" model="ir.ui.view">
        <field name="name">school.student.form</field>
        <field name="model">school.student</field>
        <field name="arch" type="xml">
            <form string="Student Form">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="age"/>
                        <field name="gender"/>
                        <field name="subject_ids" widget="many2many_tags"/>
                        <field name="club_ids" widget="many2many_tags"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Student Tree View -->
    <record id="view_student_tree" model="ir.ui.view">
        <field name="name">school.student.tree</field>
        <field name="model">school.student</field>
        <field name="arch" type="xml">
            <tree string="Students">
                <field name="name"/>
                <field name="age"/>
                <field name="gender"/>
            </tree>
        </field>
    </record>

    <!-- Student Action -->
    <record id="action_student" model="ir.actions.act_window">
        <field name="name">Students</field>
        <field name="res_model">school.student</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_student" name="Manage Students" parent="menu_school_students" action="action_student"/>
</odoo>
