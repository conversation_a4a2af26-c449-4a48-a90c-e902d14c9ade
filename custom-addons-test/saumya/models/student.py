from odoo import models, fields

class SchoolStudent(models.Model):
    _name = 'school.student'
    _description = 'School Student'

    name = fields.Char(string="Student Name", required=True)
    age = fields.Integer(string="Age")
    gender = fields.Selection([('male', 'Male'), ('female', 'Female')], string="Gender")
    
    # Many2many Relations
    subject_ids = fields.Many2many('school.subject', string="Subjects")
    club_ids = fields.Many2many('school.club', string="Clubs")