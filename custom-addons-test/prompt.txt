You are in UBuntu Server and Odoo 18 is installed on localhost:6070. 

All the folder inside @arihanterp/custom-addons-test will be available in Odoo to be installed.

We can create new Database in this Odoo.
admin_passwd = bethebest@1

Now, Below is the task we need to do and test:

We need to develop, install and test new Odoo Module for Online Photobook Selling businness. Our website plan and strategy is mentioned in @E-com Website plan.pdf, it's text is mentioned in @Ecom-Website Plan.md and @Website Development.md 

We should make this website nearly perfect for users to make live in 2025 for gifting. All the process from Developing our module to Database creation, Installation of modules to making it live on 6070 port should be taken care by you.

FOr website UI/UX and colour schema we can take reference of https://mixbook.com/ which is our competitor. We need to develop better website then this one. We must use our content SEO Organized. Blogs content should also be written in our website for our usecases 

Make Sure all new module name must start with 'ai_' and AUthor should be Arihant AI. Use Odoo's Builtin Modules only and do not develop any new Odoo Models in the backend. Focus on Great Website and Web Portal.