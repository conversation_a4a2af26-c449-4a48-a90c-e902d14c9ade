[databases]
* = host=db port=5432 user=odoo password=odoo

[pgbouncer]
listen_addr = *
listen_port = 6432
auth_type = plain
auth_file = /etc/pgbouncer/userlist.txt
pool_mode = session
max_client_conn = 1000
default_pool_size = 50
reserve_pool_size = 20
server_reset_query = DISCARD ALL
server_check_query = select 1
server_check_delay = 30
max_db_connections = 512
max_user_connections = 50
admin_users = odoo
stats_users = odoo
ignore_startup_parameters = extra_float_digits
