<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mobile API Token Form View -->
    <record id="view_mobile_api_token_form" model="ir.ui.view">
        <field name="name">mobile.api.token.form</field>
        <field name="model">mobile.api.token</field>
        <field name="arch" type="xml">
            <form string="Mobile API Token">
                <header>
                    <button name="extend_expiry" string="Extend Expiry" type="object" class="oe_highlight" attrs="{'invisible': [('is_active', '=', False)]}"/>
                    <button name="revoke" string="Revoke Token" type="object" class="btn-danger" attrs="{'invisible': [('is_active', '=', False)]}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Token Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="token" readonly="1"/>
                            <field name="user_id"/>
                        </group>
                        <group>
                            <field name="expiry_date"/>
                            <field name="is_active"/>
                            <field name="last_used" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Mobile API Token Tree View -->
    <record id="view_mobile_api_token_tree" model="ir.ui.view">
        <field name="name">mobile.api.token.tree</field>
        <field name="model">mobile.api.token</field>
        <field name="arch" type="xml">
            <tree string="Mobile API Tokens" decoration-muted="not is_active" decoration-danger="expiry_date &lt; current_date">
                <field name="name"/>
                <field name="user_id"/>
                <field name="expiry_date"/>
                <field name="is_active"/>
                <field name="last_used"/>
            </tree>
        </field>
    </record>

    <!-- Mobile API Token Search View -->
    <record id="view_mobile_api_token_search" model="ir.ui.view">
        <field name="name">mobile.api.token.search</field>
        <field name="model">mobile.api.token</field>
        <field name="arch" type="xml">
            <search string="Search Mobile API Tokens">
                <field name="name"/>
                <field name="user_id"/>
                <field name="token"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Expired" name="expired" domain="[('expiry_date', '&lt;', context_today().strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="group_by_user" context="{'group_by': 'user_id'}"/>
                    <filter string="Status" name="group_by_status" context="{'group_by': 'is_active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Mobile API Token Action -->
    <record id="action_mobile_api_token" model="ir.actions.act_window">
        <field name="name">Mobile API Tokens</field>
        <field name="res_model">mobile.api.token</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Mobile API Token
            </p>
            <p>
                Mobile API Tokens are used to authenticate mobile app users.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_mobile_api_root" name="Mobile API" parent="website.menu_website_configuration" sequence="100"/>
    <menuitem id="menu_mobile_api_token" name="API Tokens" parent="menu_mobile_api_root" action="action_mobile_api_token" sequence="10"/>
</odoo>
