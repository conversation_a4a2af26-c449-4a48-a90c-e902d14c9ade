# -*- coding: utf-8 -*-

import json
import logging
import werkzeug
import requests
from datetime import datetime

from odoo import http, _
from odoo.http import request
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)

class MobilePaymentController(http.Controller):
    
    @http.route('/api/mobile/payment/methods', type='json', auth='public', methods=['GET'], csrf=False)
    def get_payment_methods(self, **kwargs):
        """
        Get available payment methods for mobile app
        """
        try:
            # Get enabled payment acquirers
            acquirers = request.env['payment.acquirer'].sudo().search([
                ('state', 'in', ['enabled', 'test']),
                ('provider', 'in', ['razorpay', 'transfer']),  # Only show supported providers
            ])
            
            result = []
            for acquirer in acquirers:
                result.append({
                    'id': acquirer.id,
                    'name': acquirer.name,
                    'provider': acquirer.provider,
                    'description': acquirer.description,
                    'is_test_mode': acquirer.state == 'test',
                    'supported_currencies': acquirer.journal_id.currency_id.name,
                    'supported_payment_methods': [pm.name for pm in acquirer.payment_method_ids],
                })
            
            return {
                'success': True,
                'payment_methods': result,
            }
        except Exception as e:
            _logger.exception("Mobile API payment methods error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/payment/razorpay/create', type='json', auth='public', methods=['POST'], csrf=False)
    def create_razorpay_order(self, **kwargs):
        """
        Create a Razorpay order for mobile payment
        """
        try:
            sale_order = request.website.sale_get_order()
            if not sale_order:
                return {'success': False, 'error': _('No order found')}
            
            # Get Razorpay acquirer
            acquirer = request.env['payment.acquirer'].sudo().search([
                ('provider', '=', 'razorpay'),
                ('state', 'in', ['enabled', 'test']),
            ], limit=1)
            
            if not acquirer:
                return {'success': False, 'error': _('Razorpay payment method not available')}
            
            # Create payment transaction
            transaction = request.env['payment.transaction'].sudo().create({
                'acquirer_id': acquirer.id,
                'amount': sale_order.amount_total,
                'currency_id': sale_order.currency_id.id,
                'partner_id': sale_order.partner_id.id,
                'sale_order_ids': [(6, 0, [sale_order.id])],
                'reference': sale_order.name,
            })
            
            # Get Razorpay credentials
            key_id = acquirer.sudo().razorpay_key_id
            key_secret = acquirer.sudo().razorpay_key_secret
            
            # Create Razorpay order
            url = 'https://api.razorpay.com/v1/orders'
            headers = {
                'Content-Type': 'application/json',
            }
            auth = (key_id, key_secret)
            data = {
                'amount': int(sale_order.amount_total * 100),  # Amount in paise
                'currency': sale_order.currency_id.name,
                'receipt': sale_order.name,
                'payment_capture': 1,  # Auto capture
                'notes': {
                    'order_id': sale_order.id,
                    'transaction_id': transaction.id,
                },
            }
            
            response = requests.post(url, auth=auth, headers=headers, data=json.dumps(data))
            
            if response.status_code == 200:
                order_data = response.json()
                
                # Update transaction with Razorpay order ID
                transaction.write({
                    'acquirer_reference': order_data['id'],
                })
                
                return {
                    'success': True,
                    'order_id': order_data['id'],
                    'amount': order_data['amount'] / 100,  # Convert back to currency units
                    'currency': order_data['currency'],
                    'key_id': key_id,
                    'transaction_id': transaction.id,
                    'customer_info': {
                        'name': sale_order.partner_id.name,
                        'email': sale_order.partner_id.email,
                        'phone': sale_order.partner_id.phone,
                    },
                }
            else:
                return {'success': False, 'error': response.json().get('error', {}).get('description', 'Razorpay API error')}
            
        except Exception as e:
            _logger.exception("Mobile API Razorpay order creation error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/payment/razorpay/verify', type='json', auth='public', methods=['POST'], csrf=False)
    def verify_razorpay_payment(self, **kwargs):
        """
        Verify Razorpay payment and confirm order
        """
        try:
            payment_id = kwargs.get('payment_id')
            order_id = kwargs.get('order_id')
            signature = kwargs.get('signature')
            transaction_id = kwargs.get('transaction_id')
            
            if not payment_id or not order_id or not signature:
                return {'success': False, 'error': _('Payment verification data incomplete')}
            
            # Get transaction
            transaction = request.env['payment.transaction'].sudo().browse(int(transaction_id))
            if not transaction:
                return {'success': False, 'error': _('Transaction not found')}
            
            # Get Razorpay acquirer
            acquirer = transaction.acquirer_id
            
            # Verify signature
            key_secret = acquirer.sudo().razorpay_key_secret
            
            # Verify payment with Razorpay
            url = f'https://api.razorpay.com/v1/payments/{payment_id}'
            auth = (acquirer.sudo().razorpay_key_id, key_secret)
            
            response = requests.get(url, auth=auth)
            
            if response.status_code == 200:
                payment_data = response.json()
                
                # Check if payment is authorized or captured
                if payment_data['status'] in ['authorized', 'captured']:
                    # Update transaction with payment ID
                    transaction.write({
                        'acquirer_reference': f"{order_id}:{payment_id}",
                        'state': 'done',
                        'date': datetime.now(),
                    })
                    
                    # Confirm the order
                    sale_order = transaction.sale_order_ids[0]
                    sale_order.with_context(send_email=True).action_confirm()
                    
                    return {
                        'success': True,
                        'order_id': sale_order.id,
                        'order_name': sale_order.name,
                        'payment_status': payment_data['status'],
                    }
                else:
                    return {'success': False, 'error': _('Payment not authorized or captured')}
            else:
                return {'success': False, 'error': response.json().get('error', {}).get('description', 'Razorpay API error')}
            
        except Exception as e:
            _logger.exception("Mobile API Razorpay payment verification error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/payment/upi/create', type='json', auth='public', methods=['POST'], csrf=False)
    def create_upi_payment(self, **kwargs):
        """
        Create a UPI payment for mobile app
        """
        try:
            vpa = kwargs.get('vpa')  # Virtual Payment Address (UPI ID)
            
            if not vpa:
                return {'success': False, 'error': _('UPI ID is required')}
            
            sale_order = request.website.sale_get_order()
            if not sale_order:
                return {'success': False, 'error': _('No order found')}
            
            # Get transfer acquirer (for UPI)
            acquirer = request.env['payment.acquirer'].sudo().search([
                ('provider', '=', 'transfer'),
                ('state', 'in', ['enabled', 'test']),
            ], limit=1)
            
            if not acquirer:
                return {'success': False, 'error': _('UPI payment method not available')}
            
            # Create payment transaction
            transaction = request.env['payment.transaction'].sudo().create({
                'acquirer_id': acquirer.id,
                'amount': sale_order.amount_total,
                'currency_id': sale_order.currency_id.id,
                'partner_id': sale_order.partner_id.id,
                'sale_order_ids': [(6, 0, [sale_order.id])],
                'reference': sale_order.name,
            })
            
            # Generate UPI payment details
            upi_payment_data = {
                'vpa': vpa,
                'name': acquirer.company_id.name,
                'amount': sale_order.amount_total,
                'transaction_ref': f"TXN{transaction.id}",
                'transaction_note': f"Payment for order {sale_order.name}",
                'currency': sale_order.currency_id.name,
            }
            
            return {
                'success': True,
                'upi_payment_data': upi_payment_data,
                'transaction_id': transaction.id,
                'order_id': sale_order.id,
                'order_name': sale_order.name,
            }
            
        except Exception as e:
            _logger.exception("Mobile API UPI payment creation error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/payment/upi/verify', type='json', auth='public', methods=['POST'], csrf=False)
    def verify_upi_payment(self, **kwargs):
        """
        Verify UPI payment and confirm order
        """
        try:
            transaction_id = kwargs.get('transaction_id')
            upi_transaction_id = kwargs.get('upi_transaction_id')
            status = kwargs.get('status')
            
            if not transaction_id or not upi_transaction_id:
                return {'success': False, 'error': _('Payment verification data incomplete')}
            
            # Get transaction
            transaction = request.env['payment.transaction'].sudo().browse(int(transaction_id))
            if not transaction:
                return {'success': False, 'error': _('Transaction not found')}
            
            # Check status
            if status == 'SUCCESS':
                # Update transaction with UPI transaction ID
                transaction.write({
                    'acquirer_reference': upi_transaction_id,
                    'state': 'done',
                    'date': datetime.now(),
                })
                
                # Confirm the order
                sale_order = transaction.sale_order_ids[0]
                sale_order.with_context(send_email=True).action_confirm()
                
                return {
                    'success': True,
                    'order_id': sale_order.id,
                    'order_name': sale_order.name,
                    'payment_status': 'paid',
                }
            else:
                return {'success': False, 'error': _('UPI payment failed or cancelled')}
            
        except Exception as e:
            _logger.exception("Mobile API UPI payment verification error")
            return {'success': False, 'error': str(e)}
