# -*- coding: utf-8 -*-

import json
import logging
from werkzeug.exceptions import BadRequest

from odoo import http, _
from odoo.http import request
from odoo.addons.website_sale.controllers.main import WebsiteSale

_logger = logging.getLogger(__name__)

class MobileApiController(http.Controller):
    
    @http.route('/api/mobile/auth', type='json', auth='none', methods=['POST'], csrf=False)
    def authenticate(self, **kwargs):
        """
        Mobile-specific authentication endpoint
        Returns user data and session information
        """
        try:
            db = kwargs.get('db') or http.request.env.cr.dbname
            login = kwargs.get('login')
            password = kwargs.get('password')
            
            if not login or not password:
                return {'success': False, 'error': _('Login and password are required')}
            
            uid = request.session.authenticate(db, login, password)
            
            if not uid:
                return {'success': False, 'error': _('Authentication failed')}
            
            user = request.env['res.users'].sudo().browse(uid)
            partner = user.partner_id
            
            # Generate API token for mobile app
            api_token = request.env['mobile.api.token'].sudo().create({
                'user_id': uid,
                'name': 'Mobile App Token',
            })
            
            return {
                'success': True,
                'uid': uid,
                'sessionId': request.session.sid,
                'apiToken': api_token.token,
                'userData': {
                    'id': user.id,
                    'name': user.name,
                    'email': user.email or partner.email,
                    'phone': partner.phone,
                    'image': partner.image_1920 and partner.image_1920.decode('utf-8') or False,
                }
            }
        except Exception as e:
            _logger.exception("Mobile API authentication error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/products', type='json', auth='public', methods=['GET'], csrf=False)
    def get_products(self, **kwargs):
        """
        Get optimized product list for mobile app
        Supports pagination, filtering, and search
        """
        try:
            limit = int(kwargs.get('limit', 20))
            offset = int(kwargs.get('offset', 0))
            category_id = kwargs.get('category_id')
            search = kwargs.get('search')
            
            domain = [('website_published', '=', True)]
            
            if category_id:
                domain.append(('public_categ_ids', 'child_of', int(category_id)))
            
            if search:
                domain.append(('name', 'ilike', search))
            
            products = request.env['product.template'].sudo().search(domain, limit=limit, offset=offset)
            total_count = request.env['product.template'].sudo().search_count(domain)
            
            result = []
            for product in products:
                result.append({
                    'id': product.id,
                    'name': product.name,
                    'list_price': product.list_price,
                    'description_sale': product.description_sale,
                    'image_1920': product.image_1920 and product.image_1920.decode('utf-8') or False,
                    'public_categ_ids': product.public_categ_ids.ids,
                    'website_url': product.website_url,
                    'product_variant_ids': product.product_variant_ids.ids,
                })
            
            return {
                'success': True,
                'products': result,
                'total_count': total_count,
                'has_more': offset + limit < total_count,
            }
        except Exception as e:
            _logger.exception("Mobile API products error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/cart/add', type='json', auth='public', methods=['POST'], csrf=False)
    def add_to_cart(self, **kwargs):
        """
        Add product to cart with mobile-specific handling
        """
        try:
            product_id = int(kwargs.get('product_id'))
            quantity = float(kwargs.get('quantity', 1.0))
            
            if not product_id:
                return {'success': False, 'error': _('Product ID is required')}
            
            sale_order = request.website.sale_get_order(force_create=True)
            if not sale_order:
                return {'success': False, 'error': _('Could not create order')}
            
            sale_order._cart_update(product_id=product_id, add_qty=quantity)
            
            return {
                'success': True,
                'cart': self._get_cart_data(sale_order),
            }
        except Exception as e:
            _logger.exception("Mobile API add to cart error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/cart', type='json', auth='public', methods=['GET'], csrf=False)
    def get_cart(self, **kwargs):
        """
        Get cart data optimized for mobile
        """
        try:
            sale_order = request.website.sale_get_order()
            if not sale_order:
                return {
                    'success': True,
                    'cart': {
                        'items': [],
                        'total': 0,
                        'subtotal': 0,
                        'tax': 0,
                    }
                }
            
            return {
                'success': True,
                'cart': self._get_cart_data(sale_order),
            }
        except Exception as e:
            _logger.exception("Mobile API get cart error")
            return {'success': False, 'error': str(e)}
    
    @http.route('/api/mobile/payment/process', type='json', auth='public', methods=['POST'], csrf=False)
    def process_payment(self, **kwargs):
        """
        Process payment from mobile app
        """
        try:
            payment_method = kwargs.get('payment_method')
            payment_details = kwargs.get('payment_details', {})
            
            if not payment_method:
                return {'success': False, 'error': _('Payment method is required')}
            
            sale_order = request.website.sale_get_order()
            if not sale_order:
                return {'success': False, 'error': _('No order found')}
            
            # Create payment transaction
            acquirer = request.env['payment.acquirer'].sudo().search(
                [('provider', '=', payment_method)], limit=1)
            
            if not acquirer:
                return {'success': False, 'error': _('Payment method not available')}
            
            transaction = request.env['payment.transaction'].sudo().create({
                'acquirer_id': acquirer.id,
                'amount': sale_order.amount_total,
                'currency_id': sale_order.currency_id.id,
                'partner_id': sale_order.partner_id.id,
                'sale_order_ids': [(6, 0, [sale_order.id])],
                'reference': sale_order.name,
            })
            
            # Process payment based on method
            if payment_method == 'razorpay':
                # Handle Razorpay payment
                payment_id = payment_details.get('payment_id')
                if not payment_id:
                    return {'success': False, 'error': _('Payment ID is required')}
                
                # Update transaction with payment ID
                transaction.write({
                    'acquirer_reference': payment_id,
                })
                
                # Set transaction as done
                transaction._set_done()
                
                # Confirm the order
                sale_order.with_context(send_email=True).action_confirm()
                
                return {
                    'success': True,
                    'order_id': sale_order.id,
                    'order_name': sale_order.name,
                }
            
            elif payment_method == 'upi':
                # Handle UPI payment
                upi_id = payment_details.get('upi_id')
                transaction_id = payment_details.get('transaction_id')
                
                if not transaction_id:
                    return {'success': False, 'error': _('Transaction ID is required')}
                
                # Update transaction with UPI details
                transaction.write({
                    'acquirer_reference': transaction_id,
                })
                
                # Set transaction as done
                transaction._set_done()
                
                # Confirm the order
                sale_order.with_context(send_email=True).action_confirm()
                
                return {
                    'success': True,
                    'order_id': sale_order.id,
                    'order_name': sale_order.name,
                }
            
            else:
                return {'success': False, 'error': _('Unsupported payment method')}
            
        except Exception as e:
            _logger.exception("Mobile API payment error")
            return {'success': False, 'error': str(e)}
    
    def _get_cart_data(self, sale_order):
        """
        Helper method to format cart data for mobile
        """
        items = []
        for line in sale_order.order_line:
            product = line.product_id
            items.append({
                'id': line.id,
                'product_id': product.id,
                'product_name': product.name,
                'product_image': product.image_1920 and product.image_1920.decode('utf-8') or False,
                'price_unit': line.price_unit,
                'product_uom_qty': line.product_uom_qty,
                'price_subtotal': line.price_subtotal,
                'price_total': line.price_total,
            })
        
        return {
            'items': items,
            'total': sale_order.amount_total,
            'subtotal': sale_order.amount_untaxed,
            'tax': sale_order.amount_tax,
            'discount': sum(line.price_unit * line.product_uom_qty - line.price_subtotal 
                           for line in sale_order.order_line),
        }
