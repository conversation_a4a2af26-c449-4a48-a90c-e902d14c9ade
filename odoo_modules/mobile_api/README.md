# Mobile API Module for Odoo 18

This module provides custom API endpoints for the Siddhi eCommerce mobile application.

## Features

- Mobile-specific authentication with token management
- Optimized product listing endpoints
- Cart management APIs
- Order processing
- Payment integration with Razorpay and UPI

## Installation

1. Copy the `mobile_api` folder to your Odoo addons directory.
2. Restart the Odoo server.
3. Go to Apps menu and click on "Update Apps List".
4. Search for "Mobile API" and install the module.

## Configuration

### API Authentication

The module uses token-based authentication for mobile clients. To configure:

1. Go to Website > Configuration > Mobile API > API Tokens
2. Create a new token for each mobile client
3. Use the token in the mobile app's API requests

### Payment Integration

To configure payment gateways:

1. Go to Invoicing > Configuration > Payment Acquirers
2. Configure Razorpay and other payment methods
3. Make sure the payment methods are enabled and properly configured

## API Endpoints

### Authentication

- **POST** `/api/mobile/auth`
  - Authenticate user and get session information
  - Parameters: `login`, `password`
  - Returns: User data, session ID, and API token

### Products

- **GET** `/api/mobile/products`
  - Get optimized product list for mobile app
  - Parameters: `limit`, `offset`, `category_id`, `search`
  - Returns: List of products with essential data

### Cart

- **GET** `/api/mobile/cart`
  - Get cart data optimized for mobile
  - Returns: Cart items, totals, and other cart information

- **POST** `/api/mobile/cart/add`
  - Add product to cart
  - Parameters: `product_id`, `quantity`
  - Returns: Updated cart data

### Payment

- **POST** `/api/mobile/payment/process`
  - Process payment from mobile app
  - Parameters: `payment_method`, `payment_details`
  - Returns: Order confirmation data

## Mobile App Integration

To integrate with the mobile app:

1. Configure the base URL in the mobile app's constants:
   ```javascript
   export const API = {
     BASE_URL: 'http://your-odoo-server:8069',
     TIMEOUT: 10000,
     DB_NAME: 'your_database_name',
   };
   ```

2. Use the authentication endpoint to get a session token:
   ```javascript
   const response = await axios.post(`${API.BASE_URL}/api/mobile/auth`, {
     login: username,
     password: password,
     db: API.DB_NAME,
   });
   
   if (response.data.success) {
     // Store the session ID and API token
     const { sessionId, apiToken, userData } = response.data;
     // Use these for subsequent requests
   }
   ```

3. Include the session ID in subsequent requests:
   ```javascript
   const headers = {
     'Cookie': sessionId,
     'X-API-Token': apiToken,
   };
   
   const response = await axios.get(`${API.BASE_URL}/api/mobile/products`, {
     headers,
     params: {
       limit: 20,
       offset: 0,
     },
   });
   ```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check that the user credentials are correct
   - Verify that the database name is correct
   - Ensure the user has the necessary permissions

2. **Payment Processing Errors**
   - Verify that the payment acquirers are properly configured
   - Check the payment gateway credentials
   - Look for error logs in the Odoo server logs

3. **API Access Issues**
   - Ensure the module is properly installed
   - Check that the user has access to the API endpoints
   - Verify that the API token is valid and not expired

## Development

To extend this module:

1. Add new controllers in the `controllers` directory
2. Create new models in the `models` directory
3. Update the security settings in `security/ir.model.access.csv`
4. Add new views in the `views` directory

## License

This module is licensed under LGPL-3.
