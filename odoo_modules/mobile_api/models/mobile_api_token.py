# -*- coding: utf-8 -*-

import uuid
import logging
from datetime import datetime, timedelta

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)

class MobileApiToken(models.Model):
    _name = 'mobile.api.token'
    _description = 'Mobile API Token'
    
    name = fields.Char('Name', required=True)
    token = fields.Char('Token', readonly=True, required=True, copy=False, default=lambda self: str(uuid.uuid4()))
    user_id = fields.Many2one('res.users', string='User', required=True, ondelete='cascade')
    expiry_date = fields.Datetime('Expiry Date', required=True, 
                                 default=lambda self: fields.Datetime.now() + timedelta(days=30))
    is_active = fields.Bo<PERSON>an('Active', default=True)
    last_used = fields.Datetime('Last Used')
    
    _sql_constraints = [
        ('token_uniq', 'unique (token)', 'Token must be unique!')
    ]
    
    @api.model
    def create(self, vals):
        # Ensure token is unique
        vals['token'] = str(uuid.uuid4())
        return super(MobileApiToken, self).create(vals)
    
    def is_valid(self):
        """Check if token is valid"""
        self.ensure_one()
        if not self.is_active:
            return False
        if self.expiry_date and self.expiry_date < fields.Datetime.now():
            return False
        return True
    
    def validate_token(self, token):
        """Validate token and return user"""
        token_rec = self.search([('token', '=', token)], limit=1)
        if not token_rec or not token_rec.is_valid():
            return False
        
        # Update last used
        token_rec.write({'last_used': fields.Datetime.now()})
        return token_rec.user_id
    
    def extend_expiry(self, days=30):
        """Extend token expiry"""
        self.ensure_one()
        self.expiry_date = fields.Datetime.now() + timedelta(days=days)
        return True
    
    def revoke(self):
        """Revoke token"""
        self.ensure_one()
        self.is_active = False
        return True
