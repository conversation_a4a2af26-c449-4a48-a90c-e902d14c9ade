# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* muk_web_theme
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 13:53+0000\n"
"PO-Revision-Date: 2024-11-06 13:53+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Apps Active"
msgstr "Aktive Apps"

#. module: muk_web_theme
#: model:ir.model.fields,field_description:muk_web_theme.field_res_company__background_image
#: model:ir.model.fields,field_description:muk_web_theme.field_res_config_settings__theme_background_image
msgid "Apps Menu Background Image"
msgstr "Hintergrundfarbe Appsmenü"

#. module: muk_web_theme
#: model:ir.model.fields,field_description:muk_web_theme.field_res_config_settings__theme_color_appsmenu_text
msgid "Apps Menu Text Color"
msgstr "Textfarbe Appsmenü"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Apps Text"
msgstr "Appstext"

#. module: muk_web_theme
#: model:ir.model.fields,field_description:muk_web_theme.field_res_config_settings__theme_color_appbar_active
msgid "AppsBar Active Color"
msgstr "Aktive Farbe Appsleiste"

#. module: muk_web_theme
#: model:ir.model.fields,field_description:muk_web_theme.field_res_config_settings__theme_color_appbar_background
msgid "AppsBar Background Color"
msgstr "Hintergrundfarbe Appsleiste"

#. module: muk_web_theme
#: model:ir.model.fields,field_description:muk_web_theme.field_res_config_settings__theme_color_appbar_text
msgid "AppsBar Text Color"
msgstr "Textfarbe Appsleiste"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Backend Theme"
msgstr "Backend-Theme"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Background"
msgstr "Hintergrund"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Background Image"
msgstr "Hintergrundbild"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Brand"
msgstr "Marke"

#. module: muk_web_theme
#: model:ir.model,name:muk_web_theme.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: muk_web_theme
#: model:ir.model.fields,field_description:muk_web_theme.field_res_company__favicon
#: model:ir.model.fields,field_description:muk_web_theme.field_res_config_settings__theme_favicon
msgid "Company Favicon"
msgstr "Favicon Unternehmen"

#. module: muk_web_theme
#: model:ir.model,name:muk_web_theme.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Context Colors"
msgstr "Kontext Farben"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Customize context colors of the system"
msgstr "Passen Sie die Kontextfarben des Systems an"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Customize the look and feel of the theme"
msgstr "Passen Sie das Erscheinungsbild des Themas an"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Danger"
msgstr "Gefahr"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Favicon"
msgstr "Favicon"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Favicon & Logo"
msgstr "Favicon & Logo"

#. module: muk_web_theme
#: model:ir.model,name:muk_web_theme.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: muk_web_theme
#. odoo-javascript
#: code:addons/muk_web_theme/static/src/webclient/navbar/navbar.xml:0
msgid "Home Menu"
msgstr "Home-Menü"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Info"
msgstr "Info"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Logo"
msgstr "Logo"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Menu Text"
msgstr "Menü Text"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Primary"
msgstr "Primär"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Reset Theme Colors"
msgstr "Farben des Themas zurücksetzen"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Set the background image for the apps menu"
msgstr "Legen Sie das Hintergrundbild für das Apps-Menü fest"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Set your own favicon and logo for the appsbar"
msgstr "Legen Sie ihr eigenes Favicon und Logo für die Appsleiste fest"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Success"
msgstr "Erfolg"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Theme Colors"
msgstr "Thema Farben"

#. module: muk_web_theme
#: model_terms:ir.ui.view,arch_db:muk_web_theme.view_res_config_settings_colors_form
msgid "Warning"
msgstr "Warnung"