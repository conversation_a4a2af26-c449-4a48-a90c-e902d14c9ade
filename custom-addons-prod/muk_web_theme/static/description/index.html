<section class="oe_container">
	<div class="oe_row oe_spaced">
		<h2 class="oe_slogan">MuK Backend Theme</h2>
		<h3 class="oe_slogan mb-0">Odoo Community Backend Theme</h3>
		<img src="logo.png" style="width: 150px;" class="mx-auto center-block">
		<h4 class="oe_slogan mt-0" style="font-size: 23px">MuK IT GmbH - www.mukit.at</h4>
		<div class="mb-4" style="text-align: center;">
			<span 
				class="btn btn-sm mb-2" 
				style="font-size:14px; font-weight:500; background-color:#243742; color:#fff; cursor:default;"
			>
				<i class="fa fa-check"></i> Community
			</span>
			<span 
				class="btn btn-sm mb-2" 
				style="font-size:14px; font-weight:500; background-color:#5D8DA8; color:#fff; cursor:default;"
			>
				<i class="fa fa-times me-1"></i> Enterprise
			</span>
		</div>
		<div class="oe_demo oe_screenshot"
			style="max-width: 84%; margin: 16px 8%">
			<img src="screenshot.png">
		</div>
	</div>
</section>

<section class="oe_container">
	<div class="oe_row oe_spaced">
		<div class="text-justify" style="max-width: 84%; margin: 16px 8%;">
			<h3 class="oe_slogan">Overview</h3>
			<p class="oe_mt32">
				This module offers a mobile compatible design for Odoo Community. Furthermore it 
				allows the user to define some design preferences. Each user can choose the size 
				of the sidebar. In addition, the background image of the app menu can be set 
				for each company.
			</p>
		</div>
	</div>
</section>

<section class="oe_container">
	<div class="oe_row oe_spaced">
		<div class="text-justify" style="max-width: 84%; margin: 16px 8%;">
			<h3 class="oe_slogan">Desktop Interface</h3>
			<p>
				The theme adds a new apps menu. This can also be opened via the menu icon. Instead 
				of a list, the apps are now displayed in a fullscreen popover. If you start tapping 
				while you are on the menu, the menu search opens automatically.
            </p>
			<div class="row">
				<div class="mb16 col-lg-6">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_apps.png">
					</div>
				</div>
				<div class="mb16 col-lg-6">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_chatter.png">
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<section class="oe_container">
	<div class="oe_row oe_spaced">
		<div class="text-justify" style="max-width: 84%; margin: 16px 8%;">
			<h3 class="oe_slogan">Mobile Interface</h3>
			<p>
				The mobile view has also been improved. Here too, the menu view is now a list with 
				corresponding icons and the chat buttons are smaller in the mobile view to optimise 
				the use of space.
            </p>
			<div class="row">
				<div class="mb16 col-md-4 col-lg-3">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_mobile_apps.png">
					</div>
				</div>
				<div class="mb16 col-md-4 col-lg-3">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_mobile_form.png">
					</div>
				</div>
				<div class="mb16 col-md-4 col-lg-3">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_mobile_menu.png">
					</div>
				</div>
				<div class="mb16 col-md-4 col-lg-3">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_mobile_kanban.png">
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<section class="oe_container">
	<div class="oe_row oe_spaced">
		<div class="text-justify" style="max-width: 84%; margin: 16px 8%;">
			<h3 class="oe_slogan">Fully Customizable</h3>
			<p>
				In addition to the colours, the favicon and the appsbar logo can also be set in 
				the general settings. Each user also has the option of adjusting the relevant 
				settings in their user profile.
            </p>
			<div class="row">
				<div class="mb16 col-lg-6">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_customize.png">
					</div>
				</div>
				<div class="mb16 col-lg-6">
					<div class="oe_demo oe_screenshot">
						<img src="screenshot_settings.png">
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<section
	class="oe_container oe_dark d-flex justify-content-around align-items-lg-center flex-column flex-lg-row">
	<div class="d-flex flex-column m-3 ms-lg-4">
		<h3 class="oe_slogan mb-3 mt-3 text-left">
			<i class="fa fa-comments mr8"></i>Want more?
		</h3>
		<p>
			Are you having troubles with your Odoo integration? Or do you feel
			your system lacks of essential features? <br>If your answer is <b>YES</b>
			to one of the above questions, feel free to contact us at anytime
			with your inquiry. <br>We are looking forward to discuss your
			needs and plan the next steps with you. <br>
		</p>
	</div>
	<div
		class="oe_slogan d-flex flex-column ms-1 me-1 ms-lg-4 me-lg-4 flex-grow-1">
		<a class="btn btn-primary btn-lg m-3"
			href="mailto:<EMAIL>?subject=Request%20Quote%3A%20MuK%20Theme"
			style="font-size: 1.2rem; position: relative; overflow: hidden;">
			<i class="fa fa-envelope me-1"></i>REQUEST QUOTE
		</a>

	</div>
</section>

<section class="oe_container">
	<div class="oe_row oe_spaced">
		<h3 class="oe_slogan">Our Services</h3>
		<div class="d-flex justify-content-between">
			<div>
				<div>
					<div style="width: 75px; height: 75px; border-radius: 100%; margin: auto">
						<img src="/service_development.png" style="width: 100%; border-radius: 100%">
					</div>
					<h3 
						class="oe_slogan" 
						style="text-align: center; font-size: 14px; width: 100%; margin: 0; margin-top: 5px; color: #000 !important; opacity: 1 !important; line-height: 17px"
					>
						Odoo <br>Development
					</h3>
				</div>
			</div>
			<div>
				<div>
					<div style="width: 75px; height: 75px; border-radius: 100%; margin: auto">
						<img src="/service_integration.png" style="width: 100%; border-radius: 100%">
					</div>
					<h3 
						class="oe_slogan" 
						style="text-align: center; font-size: 14px; width: 100%; margin: 0; margin-top: 5px; color: #000 !important; opacity: 1 !important; line-height: 17px"
					>
						Odoo <br>Integration
					</h3>
				</div>
			</div>
			<div>
				<div>
					<div style="width: 75px; height: 75px; border-radius: 100%; margin: auto">
						<img src="/service_infrastructure.png" style="width: 100%; border-radius: 100%">
					</div>
					<h3 
						class="oe_slogan" 
						style="text-align: center; font-size: 14px; width: 100%; margin: 0; margin-top: 5px; color: #000 !important; opacity: 1 !important; line-height: 17px"
					>
						Odoo <br>Infrastructure
					</h3>
				</div>
			</div>
			<div>
				<div>
					<div style="width: 75px; height: 75px; border-radius: 100%; margin: auto">
						<img src="/service_training.png" style="width: 100%; border-radius: 100%">
					</div>
					<h3 
						class="oe_slogan" 
						style="text-align: center; font-size: 14px; width: 100%; margin: 0; margin-top: 5px; color: #000 !important; opacity: 1 !important; line-height: 17px"
					>
						Odoo <br>Training
					</h3>
				</div>
			</div>
			<div>
				<div>
					<div style="width: 75px; height: 75px; border-radius: 100%; margin: auto">
						<img src="/service_support.png" style="width: 100%; border-radius: 100%">
					</div>
					<h3 
						class="oe_slogan" 
						style="text-align: center; font-size: 14px; width: 100%; margin: 0; margin-top: 5px; color: #000 !important; opacity: 1 !important; line-height: 17px"
					>
						Odoo <br>Support
					</h3>
				</div>
			</div>
		</div>
	</div>
</section>
