<?xml version="1.0" encoding="UTF-8" ?>

<templates xml:space="preserve">

	<t 
		t-name="muk_web_theme.NavBar.AppsMenu"
		t-inherit="web.NavBar.AppsMenu" 
		t-inherit-mode="extension" 
	>
		<xpath expr="//Dropdown" position="replace">
			<AppsMenu menuClass="'mk_app_menu'">
				<button data-hotkey="h" title="Home Menu">
					<i class="oi oi-apps" />
				</button>
          		<t t-set-slot="content">
					<DropdownItem
						t-foreach="this.appMenuService.getAppsMenuItems()"
						t-as="app"
						t-key="app.id"
						class="'o_app'"
						attrs="{ href: app.href, 'data-menu-xmlid': app.xmlid, 'data-section': app.id }"
						onSelected="() => this.onNavBarDropdownItemSelection(app)"
						closingMode="'none'"
					>
						<a 
							t-att-href="app.href" 
							t-on-click.prevent=""
						>
							<img 
								t-if="app.webIconData"          		
								class="mk_app_icon" 
								t-att-src="app.webIconData"
							/>
							<img  
								t-else="" 
								class="mk_app_icon" 
								src="/base/static/description/icon.png"
							/>
							<span class="mk_app_name">
								<t t-out="app.name"/>
							</span>
						</a>
					</DropdownItem>
         		</t>
			</AppsMenu>
		</xpath>
	</t>
	
</templates>
