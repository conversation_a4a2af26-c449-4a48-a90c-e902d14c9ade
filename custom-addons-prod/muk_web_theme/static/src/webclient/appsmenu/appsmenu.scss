.o_navbar_apps_menu .dropdown-toggle {
	padding: 0px 14px !important;
}
	
.mk_app_menu.dropdown-menu {
	display: flex !important;
	flex-direction: row !important;
	flex-wrap: wrap !important;
	align-content: flex-start;
    right: 0 !important;
    left: 0 !important;
    bottom: 0 !important;
    max-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    border: none;
    border-radius: 0;
	user-select: none;
    margin-top: 0 !important;
	margin-bottom: 0 !important;
	background: {
        size: cover;
        repeat: no-repeat;
        position: center;
    }
	@include media-breakpoint-up(lg) {
	    padding: {
	        left: 20vw;
	        right: 20vw;
	    }
	}
	.o_app {
		margin-top: 20px;
	    width: percentage(1/3);
		background: none !important;
	    @include media-breakpoint-up(sm) {
	        width: percentage(1/4);
	    }
	    @include media-breakpoint-up(md) {
	        width: percentage(1/6);
	    }
	   	> a {
		    display: flex;
		    align-items: center;
		    flex-direction: column;
	   		.mk_app_icon {
			    width: 100%;
			    padding: 10px;
			    max-width: 70px;
			    border-radius: 0.375rem;
			    background-color: $white;
			    transform-origin: center bottom;
			    transition: box-shadow ease-in 0.1s, transform ease-in 0.1s;
			    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.2), 0 4px 4px rgba(0, 0, 0, 0.02);
			}
			.mk_app_name {
				color: $mk-appsmenu-color; 
			}
	   	}
	    &:hover {
			.mk_app_icon {
			    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.2), 0 8px 8px rgba(0, 0, 0, 0.03);
    			transform: translateY(-2px);
			}
	    }
	}
}
