# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* muk_web_colors
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 14:36+0000\n"
"PO-Revision-Date: 2024-11-06 14:36+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: muk_web_colors
#: model:ir.model,name:muk_web_colors.model_web_editor_assets
msgid "Assets Utils"
msgstr ""

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Brand"
msgstr "Marke"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_brand_dark
msgid "Brand Dark Color"
msgstr "Marke dunkle Farbe"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_brand_light
msgid "Brand Light Color"
msgstr "Marke helle Farbe"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Branding"
msgstr "Branding"

#. module: muk_web_colors
#: model:ir.model,name:muk_web_colors.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Customize the look and feel of the dark mode"
msgstr "Passen Sie Aussehen und Handhabung des dunklen Modus an"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Customize the look and feel of the light mode"
msgstr "Passen Sie Aussehen und Handhabung des hellen Modus an"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Danger"
msgstr "Gefahr"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_danger_dark
msgid "Danger Dark Color"
msgstr "Gefahr dunkle Farbe"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_danger_light
msgid "Danger Light Color"
msgstr "Gefahr helle Farbe"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Dark Mode Colors"
msgstr "Farben dunkler Modus"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Info"
msgstr "Information"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_info_dark
msgid "Info Dark Color"
msgstr "Information dunkle Farbe"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_info_light
msgid "Info Light Color"
msgstr "Information helle Farbe"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Light Mode Colors"
msgstr "Farben heller Modus"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Primary"
msgstr "Primär"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_primary_dark
msgid "Primary Dark Color"
msgstr "Primäre dunkle Farbe"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_primary_light
msgid "Primary Light Color"
msgstr "Primäre helle Farbe"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Reset Dark Colors"
msgstr "dunkle Farbe zurücksetzen"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Reset Light Colors"
msgstr "helle Farbe zurücksetzen"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Success"
msgstr "Erfolg"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_success_dark
msgid "Success Dark Color"
msgstr "Erfolg dunkle Farbe"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_success_light
msgid "Success Light Color"
msgstr "Erfolg helle Farbe"

#. module: muk_web_colors
#: model_terms:ir.ui.view,arch_db:muk_web_colors.view_res_config_settings_form
msgid "Warning"
msgstr "Warnung"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_warning_dark
msgid "Warning Dark Color"
msgstr "Warnung dunkle Farbe"

#. module: muk_web_colors
#: model:ir.model.fields,field_description:muk_web_colors.field_res_config_settings__color_warning_light
msgid "Warning Light Color"
msgstr "Warnung helle Farbe"