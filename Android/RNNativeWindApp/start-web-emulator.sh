#!/bin/bash

# Script to start an Android emulator accessible via web browser

echo "Starting web-accessible Android emulator..."

# Set up environment variables
export ANDROID_HOME=/opt/android-sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator

# Create a virtual display with Xvfb
Xvfb :1 -screen 0 1280x800x24 &
export DISPLAY=:1

# Wait for Xvfb to start
sleep 2

# Start VNC server
x11vnc -display :1 -nopw -forever -shared -quiet &

# Start noVNC (HTML5 VNC client)
/opt/novnc/utils/novnc_proxy --vnc localhost:5900 --listen 6080 &

# Wait for display to be ready
sleep 3

# Check if the AVD exists
if ! $ANDROID_HOME/emulator/emulator -list-avds | grep -q "web_emulator"; then
  echo "Creating AVD..."
  echo "no" | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
    --name web_emulator \
    --package "system-images;android-33;google_apis;x86_64" \
    --device "pixel_5"
fi

# Start the emulator
$ANDROID_HOME/emulator/emulator -avd web_emulator -no-audio -gpu swiftshader_indirect -no-boot-anim -no-snapshot -verbose &
EMULATOR_PID=$!

echo "Emulator starting..."
echo "Web access available at: http://SERVER_IP:6080"
echo "Waiting for emulator to boot..."

# Wait for emulator to boot
$ANDROID_HOME/platform-tools/adb wait-for-device
$ANDROID_HOME/platform-tools/adb shell 'while [[ "$(getprop sys.boot_completed)" != "1" ]]; do sleep 1; done'

echo "Emulator is ready!"
echo "To install the APK, run:"
echo "adb install -r android/app/build/outputs/apk/debug/app-debug.apk"

# Keep script running
wait $EMULATOR_PID
