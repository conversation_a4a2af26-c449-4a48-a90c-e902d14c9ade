FROM node:18-bullseye

# Install system dependencies
RUN apt-get update && apt-get install -y \
    openjdk-11-jdk \
    android-tools-adb \
    libc6-dev-i386 \
    lib32z1 \
    lib32stdc++6 \
    build-essential \
    file \
    curl \
    wget \
    unzip \
    git \
    openssh-server \
    openssh-client \
    x11-apps \
    qemu-kvm \
    libvirt-daemon-system \
    libvirt-clients \
    bridge-utils \
    && rm -rf /var/lib/apt/lists/*

# Set up environment variables
ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=${PATH}:${ANDROID_HOME}/cmdline-tools/latest/bin:${ANDROID_HOME}/platform-tools:${ANDROID_HOME}/emulator

# Create Android SDK directory
RUN mkdir -p ${ANDROID_HOME}

# Download and install Android SDK Command-line tools
RUN cd ${ANDROID_HOME} && \
    wget https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O cmdline-tools.zip && \
    unzip cmdline-tools.zip && \
    mkdir -p cmdline-tools/latest && \
    mv cmdline-tools/bin cmdline-tools/latest/ && \
    mv cmdline-tools/lib cmdline-tools/latest/ && \
    mv cmdline-tools/NOTICE.txt cmdline-tools/latest/ && \
    mv cmdline-tools/source.properties cmdline-tools/latest/ && \
    rm cmdline-tools.zip

# Accept licenses and install Android SDK components
RUN yes | sdkmanager --licenses && \
    sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.0" "emulator" "system-images;android-33;google_apis;x86_64"

# Create an Android Virtual Device (AVD)
RUN echo "no" | avdmanager create avd \
    --name test_device \
    --package "system-images;android-33;google_apis;x86_64" \
    --tag "google_apis" \
    --abi "x86_64"

# Configure SSH for remote access
RUN mkdir -p /var/run/sshd
RUN echo 'root:password' | chpasswd
RUN sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config
RUN sed -i 's/#X11UseLocalhost yes/X11UseLocalhost no/' /etc/ssh/sshd_config

# Install Appium and WebdriverIO for automated testing
RUN npm install -g appium
RUN appium driver install uiautomator2
RUN npm install -g webdriverio

# Set up working directory
WORKDIR /app

# Copy package.json and install dependencies
COPY package*.json ./
RUN npm install

# Copy the rest of the application
COPY . .

# Create a startup script
RUN echo '#!/bin/bash\n\
# Start SSH server\n\
/usr/sbin/sshd\n\
# Start Metro bundler in the background\n\
npm start &\n\
# Keep container running\n\
tail -f /dev/null' > /start.sh
RUN chmod +x /start.sh

# Expose ports for Metro bundler, ADB, SSH, and Appium
EXPOSE 8081 5554 5555 5556 5557 22 4723

# Start services
CMD ["/start.sh"]
