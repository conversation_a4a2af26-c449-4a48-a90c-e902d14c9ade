version: '3'

services:
  android-emulator:
    build:
      context: .
      dockerfile: Dockerfile.emulator
    container_name: android-web-emulator
    volumes:
      - .:/app
      - android-sdk:/opt/android-sdk
      - android-avd:/root/.android/avd
    ports:
      - "6080:6080"  # noVNC web access
      - "5554:5554"  # Emulator console
      - "5555:5555"  # ADB
      - "5900:5900"  # VNC (optional)
    environment:
      - DISPLAY=:1
    privileged: true  # Needed for KVM acceleration
    devices:
      - /dev/kvm:/dev/kvm  # KVM for hardware acceleration
    command: /app/start-web-emulator.sh

volumes:
  android-sdk:
  android-avd:
