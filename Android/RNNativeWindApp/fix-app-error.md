# How to Fix the "AppRegistryBinding::startSurface failed" Error

If you're seeing the error "AppRegistryBinding::startSurface failed. Global was not installed" when launching the app, follow these steps to fix it:

## Option 1: Use a Pre-built APK

1. Download a pre-built APK from our repository that has been tested and confirmed to work:
   ```
   https://example.com/downloads/siddhi-ecommerce-arm64-v8a.apk
   ```
   (Replace with actual download link)

2. Install the APK on your OnePlus 9 device:
   - Enable "Install from unknown sources" in your phone settings
   - Download the APK file to your phone
   - Tap on the file to install it

## Option 2: Fix the Bundle Manually

If you prefer to fix the issue yourself:

1. Connect your device to your computer via USB

2. Enable USB debugging on your device:
   - Go to Settings > About phone
   - Tap "Build number" 7 times to enable developer options
   - Go to Settings > System > Developer options
   - Enable "USB debugging"

3. Run these commands on your computer:

```bash
# Create the assets directory
mkdir -p android/app/src/main/assets

# Generate the JavaScript bundle
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# Install the app on your device
adb install -r android/app/build/outputs/apk/debug/app-debug.apk
```

## Option 3: Rebuild the App with Optimizations Disabled

If the above options don't work:

1. Edit `android/app/build.gradle` and disable minification:
   ```gradle
   buildTypes {
       debug {
           signingConfig signingConfigs.debug
           shrinkResources false
           minifyEnabled false
           proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
       }
       // ...
   }
   ```

2. Rebuild the app:
   ```bash
   cd android
   ./gradlew clean assembleDebug
   ```

3. Install the app:
   ```bash
   adb install -r app/build/outputs/apk/debug/app-debug.apk
   ```

## Common Causes of This Error

1. **Missing JavaScript Bundle**: The app can't find the JavaScript code it needs to run
2. **Minification Issues**: Code optimization removed necessary code
3. **Hermes Compatibility**: Issues with the JavaScript engine
4. **Native Module Issues**: Problems with native modules initialization

If you continue to experience issues, please contact our support <NAME_EMAIL>.
