// This is a minimal bundle file to make the app load
// It will show a loading screen and instructions to rebuild the app properly

var __DEV__ = false;
var global = this;

// Show a message on the screen
setTimeout(function() {
  var AppRegistry = require('react-native').AppRegistry;
  var React = require('react');
  var Text = require('react-native').Text;
  var View = require('react-native').View;
  var StyleSheet = require('react-native').StyleSheet;
  
  function MinimalApp() {
    return React.createElement(
      View,
      { style: styles.container },
      React.createElement(
        Text,
        { style: styles.title },
        "Siddhi eCommerce"
      ),
      React.createElement(
        Text,
        { style: styles.message },
        "This is a minimal version of the app."
      ),
      React.createElement(
        Text,
        { style: styles.instructions },
        "Please rebuild the app with a proper bundle."
      )
    );
  }
  
  var styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#F5FCFF',
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 20,
      color: '#333',
    },
    message: {
      fontSize: 18,
      textAlign: 'center',
      margin: 10,
      color: '#333',
    },
    instructions: {
      fontSize: 16,
      textAlign: 'center',
      color: '#666',
      marginTop: 20,
    },
  });
  
  AppRegistry.registerComponent('RNNativeWindApp', function() { return MinimalApp; });
}, 100);
