#!/bin/bash

# A simplified script to fix the "AppRegistryBinding::startSurface failed" error
# by properly generating the JavaScript bundle without rebuilding the entire app

echo "=================================================================="
echo "🚀 Generating JavaScript bundle for app"
echo "=================================================================="
echo "Time: $(date)"
echo ""

# Create assets directory if it doesn't exist
echo "📁 Setting up asset directories..."
mkdir -p android/app/src/main/assets

# Generate the JavaScript bundle with proper settings
echo "📦 Generating JavaScript bundle..."
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

echo "=================================================================="
echo "✅ Bundle generation completed!"
echo "=================================================================="
echo ""
echo "To install the app on your device:"
echo "1. Connect your device via USB"
echo "2. Run: adb install -r android/app/build/outputs/apk/debug/app-debug.apk"
echo ""
echo "If you still see the error, try rebuilding the app with:"
echo "cd android && ./gradlew clean assembleDebug"
echo "=================================================================="
