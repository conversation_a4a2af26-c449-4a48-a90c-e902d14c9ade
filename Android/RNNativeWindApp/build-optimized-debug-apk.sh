#!/bin/bash

# Script to build an optimized debug APK with smaller size
# Based on techniques from: https://chatgpt.com/share/6809a3d0-146c-8007-bdda-eb473dcf1ed6

echo "=================================================================="
echo "🚀 Starting optimized debug APK build process"
echo "=================================================================="
echo "Time: $(date)"
echo ""

# Step 1: Optimize images to reduce APK size
echo "📷 Optimizing image assets..."
./optimize-images.sh

# Step 2: Clean the project
echo "🧹 Cleaning project..."
cd android
./gradlew clean
cd ..

# Step 3: Create assets directory if it doesn't exist
echo "📁 Setting up asset directories..."
mkdir -p android/app/src/main/assets

# Step 4: Generate an optimized JavaScript bundle
echo "📦 Generating optimized JavaScript bundle..."
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res --config metro.config.js

# Step 5: Remove unused resources
echo "🗑️ Removing unused resources..."
find android/app/src/main/res -path "*/drawable*/*" -name "*.png" -size +10k | xargs ls -lh
find android/app/src/main/res -path "*/drawable*/*" -name "*.png" -size +100k -delete

# Step 6: Build the optimized debug APK
echo "🔨 Building optimized debug APK..."
cd android
./gradlew assembleDebug --no-daemon

# Check if debug build was successful
if [ ! -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
  echo "❌ Debug build failed! Debug APK not found."
  exit 1
fi

# Check the debug APK size
DEBUG_APK_SIZE=$(du -h app/build/outputs/apk/debug/app-debug.apk | cut -f1)
echo "✅ Debug APK built successfully!"
echo "📱 Debug APK location: app/build/outputs/apk/debug/app-debug.apk"
echo "📏 Debug APK size: $DEBUG_APK_SIZE"

echo "=================================================================="
echo "✅ Optimized debug APK build process completed!"
echo "=================================================================="

# Return to the project root
cd ..
