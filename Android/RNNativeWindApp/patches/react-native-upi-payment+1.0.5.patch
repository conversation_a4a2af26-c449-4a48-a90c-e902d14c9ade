diff --git a/node_modules/react-native-upi-payment/android/build.gradle b/node_modules/react-native-upi-payment/android/build.gradle
index 5c0e7a0..a9c3e9c 100644
--- a/node_modules/react-native-upi-payment/android/build.gradle
+++ b/node_modules/react-native-upi-payment/android/build.gradle
@@ -1,15 +1,16 @@
 buildscript {
     repositories {
+        google()
+        mavenCentral()
         jcenter()
     }
     dependencies {
-        classpath 'com.android.tools.build:gradle:2.2.3'
+        classpath 'com.android.tools.build:gradle:7.3.1'
 
         // NOTE: Do not place your application dependencies here; they belong
         // in the individual module build.gradle files
     }
 }
-
 apply plugin: 'com.android.library'
 
 android {
@@ -17,11 +18,11 @@ android {
 buildToolsVersion "31.0.0"
 
     defaultConfig {
-        minSdkVersion 16
+        minSdkVersion 24
         targetSdkVersion 31
         versionCode 1
         versionName "1.0"
         ndk {
-            abiFilters "armeabi-v7a", "x86"
+            abiFilters "armeabi-v7a", "x86", "x86_64", "arm64-v8a"
         }
     }
 }
@@ -29,7 +30,7 @@ buildToolsVersion "31.0.0"
 repositories {
     mavenCentral()
     jcenter()
+    google()
 }
 
 dependencies { 
     implementation ("com.facebook.react:react-native:+")  // From node_modules
-    implementation ('com.google.code.gson:gson:2.8.0')
+    implementation ('com.google.code.gson:gson:2.10.1')
 }
