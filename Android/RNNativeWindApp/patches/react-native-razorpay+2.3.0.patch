diff --git a/node_modules/react-native-razorpay/android/build.gradle b/node_modules/react-native-razorpay/android/build.gradle
index 5c0e7a0..a9c3e9c 100644
--- a/node_modules/react-native-razorpay/android/build.gradle
+++ b/node_modules/react-native-razorpay/android/build.gradle
@@ -7,15 +7,15 @@ def safeExtGet(prop, fallback) {
 }
 
 android {
-    compileSdkVersion safeExtGet('compileSdkVersion', 28)
+    compileSdkVersion safeExtGet('compileSdkVersion', 33)
 
 
     defaultConfig {
-        minSdkVersion safeExtGet('minSdkVersion', 16)
-        targetSdkVersion safeExtGet('targetSdkVersion', 28)
+        minSdkVersion safeExtGet('minSdkVersion', 24)
+        targetSdkVersion safeExtGet('targetSdkVersion', 33)
         versionCode 1
         versionName "1.0"
     }
 }
 
 dependencies {
     implementation 'com.facebook.react:react-native:+'
-    implementation 'com.razorpay:checkout:1.6.+'
+    implementation 'com.razorpay:checkout:1.6.33'
 }
