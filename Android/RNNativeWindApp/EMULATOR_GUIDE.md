# Android Emulator Web Access Guide

This guide explains how to use the web-accessible Android emulator to test your React Native app without a physical device.

## Starting the Emulator

To start the web-accessible emulator, run:

```bash
sudo ./run-emulator-simple.sh
```

This script:
1. Installs necessary packages (x11vnc, xvfb, novnc, websockify)
2. Creates a virtual display
3. Starts a VNC server
4. Configures noVNC for web access
5. Starts the Android emulator
6. Waits for the emulator to fully boot

## Accessing the Emulator

Once the emulator is running, you can access it through a web browser:

```
http://YOUR_SERVER_IP:6080/
```

Replace `YOUR_SERVER_IP` with the IP address of your Ubuntu server.

## Installing the App

To install the app on the emulator:

```bash
./install-apk-on-emulator.sh
```

This script:
1. Checks if the emulator is running
2. Generates the JavaScript bundle
3. Installs the APK on the emulator
4. Launches the app

## Troubleshooting

### Emulator Won't Start

If the emulator fails to start:

```bash
# Check if the emulator is already running
ps aux | grep emulator

# Kill any existing emulator processes
sudo pkill -f emulator

# Try starting with more verbose output
emulator -avd web_emulator -verbose
```

### VNC Connection Issues

If you can't connect to the VNC server:

```bash
# Check if the VNC server is running
ps aux | grep x11vnc

# Restart the VNC server
sudo pkill x11vnc
x11vnc -display :1 -nopw -forever -shared &
```

### App Installation Fails

If app installation fails:

```bash
# Check the ADB connection
adb devices

# Restart ADB server
adb kill-server
adb start-server

# Check for installation errors
adb logcat | grep "Package"
```

### "AppRegistryBinding::startSurface failed" Error

If you see this error when launching the app:

```bash
# Generate the JavaScript bundle manually
mkdir -p android/app/src/main/assets
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# Reinstall the app
adb install -r android/app/build/outputs/apk/debug/app-debug.apk
```

## Taking Screenshots and Recording Video

To capture screenshots from the emulator:

```bash
adb shell screencap -p /sdcard/screenshot.png
adb pull /sdcard/screenshot.png
```

To record video of the emulator:

```bash
adb shell screenrecord /sdcard/recording.mp4
# Press Ctrl+C to stop recording
adb pull /sdcard/recording.mp4
```
