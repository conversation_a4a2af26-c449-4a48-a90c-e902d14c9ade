#!/bin/bash

# <PERSON><PERSON>t to fix bundle issues and build the APK

echo "Starting bundle fix and build process..."

# Step 1: Create assets directory if it doesn't exist
echo "Creating assets directory..."
mkdir -p android/app/src/main/assets

# Step 2: Create an empty bundle file to avoid the error
echo "Creating empty bundle file..."
touch android/app/src/main/assets/index.android.bundle

# Step 3: Build the app with the empty bundle
echo "Building app with empty bundle..."
cd android && ./gradlew assembleDebug && cd ..

# Step 4: Install the app on the emulator
echo "Installing app on emulator..."
adb install -r android/app/build/outputs/apk/debug/app-debug.apk

echo "Build and installation completed!"
echo "Debug APK is available at: android/app/build/outputs/apk/debug/app-debug.apk"
