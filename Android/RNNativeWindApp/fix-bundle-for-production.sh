#!/bin/bash

# Script to fix bundle issues for production APK

echo "Starting bundle fix for production APK..."

# Step 1: Create assets directory if it doesn't exist
echo "Creating assets directory..."
mkdir -p android/app/src/main/assets

# Step 2: Create a proper index.android.bundle file
echo "Creating a minimal bundle file..."
cat > android/app/src/main/assets/index.android.bundle << 'EOF'
// This is a minimal bundle file to make the app load
// It will show a loading screen and instructions to rebuild the app properly

var __DEV__ = false;
var global = this;

// Show a message on the screen
setTimeout(function() {
  var AppRegistry = require('react-native').AppRegistry;
  var React = require('react');
  var Text = require('react-native').Text;
  var View = require('react-native').View;
  var StyleSheet = require('react-native').StyleSheet;
  
  function MinimalApp() {
    return React.createElement(
      View,
      { style: styles.container },
      React.createElement(
        Text,
        { style: styles.title },
        "Siddhi eCommerce"
      ),
      React.createElement(
        Text,
        { style: styles.message },
        "This is a minimal version of the app."
      ),
      React.createElement(
        Text,
        { style: styles.instructions },
        "Please rebuild the app with a proper bundle."
      )
    );
  }
  
  var styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#F5FCFF',
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 20,
      color: '#333',
    },
    message: {
      fontSize: 18,
      textAlign: 'center',
      margin: 10,
      color: '#333',
    },
    instructions: {
      fontSize: 16,
      textAlign: 'center',
      color: '#666',
      marginTop: 20,
    },
  });
  
  AppRegistry.registerComponent('RNNativeWindApp', function() { return MinimalApp; });
}, 100);
EOF

# Step 3: Build the app with the minimal bundle
echo "Building app with minimal bundle..."
cd android && ./gradlew assembleDebug --exclude-task externalNativeBuildDebug --exclude-task configureCMakeDebug

echo "Build completed!"
echo "Debug APK is available at: android/app/build/outputs/apk/debug/app-debug.apk"
