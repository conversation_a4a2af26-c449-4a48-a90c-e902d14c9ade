const { remote } = require('webdriverio');

// Appium capabilities
const capabilities = {
  platformName: 'Android',
  'appium:automationName': 'UiAutomator2',
  'appium:deviceName': 'Android Emulator',
  'appium:app': '/app/android/app/build/outputs/apk/debug/app-debug.apk',
  'appium:noReset': false,
  'appium:newCommandTimeout': 240,
  'appium:autoGrantPermissions': true
};

// Appium server options
const wdOpts = {
  hostname: process.env.APPIUM_HOST || 'localhost',
  port: parseInt(process.env.APPIUM_PORT, 10) || 4723,
  logLevel: 'info',
  capabilities
};

// Test results
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  errors: []
};

// Helper function to log test results
function logTestResult(name, passed, error = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ PASSED: ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ FAILED: ${name}`);
    if (error) {
      testResults.errors.push({ name, error: error.toString() });
      console.error(`   Error: ${error}`);
    }
  }
}

async function runTest() {
  console.log('\n🚀 Starting automated tests for Siddhi eCommerce app\n');
  console.log(`Test time: ${new Date().toLocaleString()}\n`);

  let driver;

  try {
    // Connect to Appium server
    console.log('Connecting to Appium server...');
    driver = await remote(wdOpts);
    console.log('Connected to Appium server');

    // Wait for app to load
    console.log('App launched, waiting for it to load...');
    await driver.pause(10000);

    // Check if the error message is present
    try {
      const errorElements = await driver.$('android=new UiSelector().textContains("Unable to load script")');
      const hasError = await errorElements.isExisting();

      logTestResult('App loads without bundle error', !hasError);

      if (hasError) {
        const errorText = await errorElements.getText();
        console.log(`Error message: ${errorText}`);
        // If we have a bundle error, we can't continue testing
        return;
      }
    } catch (e) {
      // If we can't find the error element, that's actually good
      logTestResult('App loads without bundle error', true);
    }

    // Test 1: Check if products screen loads
    try {
      console.log('\nTesting products screen...');

      // Wait for products to load
      await driver.pause(5000);

      // Check for product elements
      const productElements = await driver.$$('android=new UiSelector().resourceId("product-card")');
      logTestResult('Products screen shows products', productElements.length > 0);

      if (productElements.length > 0) {
        console.log(`   Found ${productElements.length} products`);
      }
    } catch (e) {
      logTestResult('Products screen shows products', false, e);
    }

    // Test 2: Test product filtering
    try {
      console.log('\nTesting product filtering...');

      // Click on filter button
      const filterButton = await driver.$('android=new UiSelector().textContains("filter")');
      await filterButton.click();
      await driver.pause(2000);

      // Check if filter modal appears
      const filterModal = await driver.$('android=new UiSelector().textContains("Filters")');
      const filterModalExists = await filterModal.isExisting();
      logTestResult('Filter modal opens', filterModalExists);

      // Close filter modal
      const closeButton = await driver.$('android=new UiSelector().descriptionContains("close")');
      await closeButton.click();
      await driver.pause(1000);
    } catch (e) {
      logTestResult('Filter modal opens', false, e);
    }

    // Test 3: Test product search
    try {
      console.log('\nTesting product search...');

      // Click on search button
      const searchButton = await driver.$('android=new UiSelector().descriptionContains("search")');
      await searchButton.click();
      await driver.pause(1000);

      // Enter search query
      const searchInput = await driver.$('android=new UiSelector().textContains("Search")');
      await searchInput.setValue('shirt');
      await driver.pause(3000);

      // Check search results
      const searchResults = await driver.$$('android=new UiSelector().resourceId("product-card")');
      logTestResult('Search returns results', searchResults.length >= 0);

      // Clear search
      const clearButton = await driver.$('android=new UiSelector().descriptionContains("clear")');
      if (await clearButton.isExisting()) {
        await clearButton.click();
      }
      await driver.pause(1000);
    } catch (e) {
      logTestResult('Search returns results', false, e);
    }

    // Test 4: Test product details
    try {
      console.log('\nTesting product details...');

      // Click on first product
      const firstProduct = await driver.$('android=new UiSelector().resourceId("product-card")');
      await firstProduct.click();
      await driver.pause(3000);

      // Check if product details screen appears
      const productTitle = await driver.$('android=new UiSelector().resourceId("product-title")');
      const productPrice = await driver.$('android=new UiSelector().resourceId("product-price")');

      const detailsLoaded = (await productTitle.isExisting()) || (await productPrice.isExisting());
      logTestResult('Product details screen loads', detailsLoaded);

      // Test variant selection if available
      try {
        const variantOptions = await driver.$$('android=new UiSelector().resourceId("variant-option")');
        if (variantOptions.length > 0) {
          await variantOptions[0].click();
          await driver.pause(1000);
          logTestResult('Variant selection works', true);
        }
      } catch (e) {
        // Variants might not be available for all products
        console.log('   No variants found or error selecting variant');
      }

      // Test quantity adjustment
      try {
        const plusButton = await driver.$('android=new UiSelector().textContains("+")');
        await plusButton.click();
        await driver.pause(500);
        logTestResult('Quantity adjustment works', true);
      } catch (e) {
        logTestResult('Quantity adjustment works', false, e);
      }

      // Go back to products screen
      const backButton = await driver.$('android=new UiSelector().descriptionContains("back")');
      await backButton.click();
      await driver.pause(2000);
    } catch (e) {
      logTestResult('Product details screen loads', false, e);
    }

    // Take a screenshot for reference
    await driver.saveScreenshot('./test-result.png');
    console.log('\nScreenshot saved as test-result.png');

    // Print test summary
    console.log('\n📊 TEST SUMMARY');
    console.log(`Tests passed: ${testResults.passed}/${testResults.total} (${Math.round(testResults.passed/testResults.total*100)}%)`);
    console.log(`Tests failed: ${testResults.failed}/${testResults.total}`);

    if (testResults.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.name}: ${error.error}`);
      });
    }

    console.log('\n✅ Automated testing completed!');

  } catch (e) {
    console.error('\n❌ Test execution failed with error:', e);
  } finally {
    if (driver) {
      await driver.deleteSession();
      console.log('Appium session ended');
    }
  }
}

runTest().catch(console.error);
