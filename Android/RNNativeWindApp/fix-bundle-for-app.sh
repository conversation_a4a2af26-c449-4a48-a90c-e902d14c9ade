#!/bin/bash

# <PERSON>ript to fix the "AppRegistryBinding::startSurface failed" error
# by properly generating the JavaScript bundle

echo "=================================================================="
echo "🚀 Fixing JavaScript bundle for app"
echo "=================================================================="
echo "Time: $(date)"
echo ""

# Step 1: Clean the project
echo "🧹 Cleaning project..."
cd android
./gradlew clean
cd ..

# Step 2: Create assets directory if it doesn't exist
echo "📁 Setting up asset directories..."
mkdir -p android/app/src/main/assets

# Step 3: Generate the JavaScript bundle with proper settings
echo "📦 Generating JavaScript bundle..."
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# Step 4: Build the debug APK without minification for testing
echo "🔨 Building debug APK without minification..."
cd android
./gradlew assembleDebug -PenableProguardInDebug=false
cd ..

# Check if build was successful
if [ ! -f "android/app/build/outputs/apk/debug/app-debug.apk" ]; then
  echo "❌ Build failed! Debug APK not found."
  exit 1
fi

# Check the debug APK size
DEBUG_APK_SIZE=$(du -h android/app/build/outputs/apk/debug/app-debug.apk | cut -f1)
echo "✅ Debug APK built successfully!"
echo "📱 Debug APK location: android/app/build/outputs/apk/debug/app-debug.apk"
echo "📏 Debug APK size: $DEBUG_APK_SIZE"

echo "=================================================================="
echo "✅ Bundle fix completed! Try installing this APK on your device."
echo "=================================================================="
