FROM ubuntu:22.04

# Prevent interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    openjdk-11-jdk \
    wget \
    unzip \
    git \
    curl \
    x11vnc \
    xvfb \
    novnc \
    websockify \
    libpulse0 \
    libc6-dev-i386 \
    lib32z1 \
    lib32stdc++6 \
    qemu-kvm \
    libvirt-daemon-system \
    libvirt-clients \
    bridge-utils \
    net-tools \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Set up noVNC
RUN mkdir -p /opt/novnc/utils/websockify \
    && wget -qO- https://github.com/novnc/noVNC/archive/v1.3.0.tar.gz | tar xz --strip 1 -C /opt/novnc \
    && wget -qO- https://github.com/novnc/websockify/archive/v0.10.0.tar.gz | tar xz --strip 1 -C /opt/novnc/utils/websockify \
    && ln -s /opt/novnc/vnc.html /opt/novnc/index.html

# Set up environment variables
ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=${PATH}:${ANDROID_HOME}/cmdline-tools/latest/bin:${ANDROID_HOME}/platform-tools:${ANDROID_HOME}/emulator

# Create Android SDK directory
RUN mkdir -p ${ANDROID_HOME}

# Download and install Android SDK Command-line tools
RUN cd ${ANDROID_HOME} && \
    wget https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O cmdline-tools.zip && \
    unzip cmdline-tools.zip && \
    mkdir -p cmdline-tools/latest && \
    mv cmdline-tools/bin cmdline-tools/latest/ && \
    mv cmdline-tools/lib cmdline-tools/latest/ && \
    mv cmdline-tools/NOTICE.txt cmdline-tools/latest/ && \
    mv cmdline-tools/source.properties cmdline-tools/latest/ && \
    rm cmdline-tools.zip

# Accept licenses and install Android SDK components
RUN yes | ${ANDROID_HOME}/cmdline-tools/latest/bin/sdkmanager --licenses && \
    ${ANDROID_HOME}/cmdline-tools/latest/bin/sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.0" "emulator" "system-images;android-33;google_apis;x86_64"

# Create an Android Virtual Device (AVD)
RUN echo "no" | ${ANDROID_HOME}/cmdline-tools/latest/bin/avdmanager create avd \
    --name web_emulator \
    --package "system-images;android-33;google_apis;x86_64" \
    --device "pixel_5"

# Set up working directory
WORKDIR /app

# Copy the emulator scripts and configuration
COPY start-web-emulator.sh /app/
COPY install-apk-on-emulator.sh /app/
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
RUN chmod +x /app/start-web-emulator.sh /app/install-apk-on-emulator.sh

# Create a startup script
RUN echo '#!/bin/bash\n\
# Set environment variables\n\
export DISPLAY=:1\n\
# Start supervisord\n\
/usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf' > /start.sh \
    && chmod +x /start.sh

# Expose ports for noVNC, emulator, and ADB
EXPOSE 6080 5554 5555 5900

# Start the web-accessible emulator
CMD ["/start.sh"]
