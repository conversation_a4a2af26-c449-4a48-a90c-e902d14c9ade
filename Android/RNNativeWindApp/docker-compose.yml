version: '3'

services:
  react-native:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: react-native-nativewind
    volumes:
      - .:/app
      - node_modules:/app/node_modules
      - android-sdk:/opt/android-sdk
      - android-avd:/root/.android/avd
    ports:
      - "8081:8081"  # Metro bundler
      - "5554:5554"  # Emulator ports
      - "5555:5555"
      - "5556:5556"
      - "5557:5557"
      - "2222:22"    # SSH
      - "4723:4723"  # Appium
    environment:
      - NODE_ENV=development
      - DISPLAY=${DISPLAY}
    privileged: true  # Needed for KVM acceleration
    devices:
      - /dev/kvm:/dev/kvm  # KVM for hardware acceleration
    command: /start.sh

volumes:
  node_modules:
  android-sdk:
  android-avd:
