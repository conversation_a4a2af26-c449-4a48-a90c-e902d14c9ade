#!/bin/bash

# <PERSON>ript to install the APK on the emulator

echo "Installing APK on emulator..."

# Set up environment variables
export ANDROID_HOME=/usr/local/android-sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Check if emulator is running
if ! adb devices | grep -q "emulator"; then
  echo "Error: No emulator found. Please start the emulator first with ./run-emulator-simple.sh"
  exit 1
fi

# Generate the JavaScript bundle
echo "Generating JavaScript bundle..."
mkdir -p android/app/src/main/assets
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# Install the APK
echo "Installing APK on emulator..."
adb install -r android/app/build/outputs/apk/debug/app-debug.apk

# Check if installation was successful
if [ $? -eq 0 ]; then
  echo "APK installed successfully!"
  echo "Launching the app..."
  adb shell am start -n com.rnnativewindapp/com.rnnativewindapp.MainActivity
else
  echo "Error: Failed to install APK."
  exit 1
fi

echo "Done!"
