# Build Instructions for Siddhi eCommerce App

This document provides detailed instructions for building and running the Siddhi eCommerce app.

## Prerequisites

- Node.js 18 or higher
- JDK 17
- Android Studio with Android SDK
- React Native CLI

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Apply patches to fix compatibility issues:
   ```bash
   npx patch-package
   ```

## Building for Android

### Debug Build

1. Generate the bundle and build the debug APK:
   ```bash
   ./fix-bundle.sh
   ```
   
   This script will:
   - Clean the project
   - Create the assets directory
   - Generate the bundle
   - Remove duplicate resources
   - Build the debug APK

2. The debug APK will be available at:
   ```
   android/app/build/outputs/apk/debug/app-debug.apk
   ```

### Release Build

1. Generate a signing key (if you don't have one):
   ```bash
   keytool -genkeypair -v -storetype PKCS12 -keystore android/app/my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
   ```

2. Create a `gradle.properties` file in the `android` directory with your signing configuration:
   ```
   MYAPP_RELEASE_STORE_FILE=my-release-key.keystore
   MYAPP_RELEASE_KEY_ALIAS=my-key-alias
   MYAPP_RELEASE_STORE_PASSWORD=*****
   MYAPP_RELEASE_KEY_PASSWORD=*****
   ```

3. Build the release APK:
   ```bash
   cd android && ./gradlew assembleRelease && cd ..
   ```

4. The release APK will be available at:
   ```
   android/app/build/outputs/apk/release/app-release.apk
   ```

## Running the App

### Using React Native CLI

1. Start the Metro bundler:
   ```bash
   npm start
   ```

2. Run on Android:
   ```bash
   npm run android
   ```

### Using Docker

1. Build the Docker image:
   ```bash
   npm run docker-build
   ```

2. Start the Docker container:
   ```bash
   npm run docker-start
   ```

3. Run on Android with remote server:
   ```bash
   npm run android-remote
   ```

## Troubleshooting

### Unable to load script

If you encounter the error "Unable to load script. Make sure you're either running Metro or that your bundle 'index.android.bundle' is packaged correctly for release", try the following:

1. Make sure Metro is running:
   ```bash
   npm start
   ```

2. If the issue persists, regenerate the bundle:
   ```bash
   ./fix-bundle.sh
   ```

### Build Errors

If you encounter build errors related to duplicate resources:

1. Clean the project:
   ```bash
   cd android && ./gradlew clean && cd ..
   ```

2. Remove duplicate resources:
   ```bash
   rm -rf android/app/src/main/res/drawable-*
   rm -rf android/app/src/main/res/raw
   ```

3. Rebuild the app:
   ```bash
   npm run android
   ```

### Payment Gateway Issues

If you encounter issues with the payment gateways:

1. Make sure you have the correct API keys in the code:
   - For Razorpay: Update the key in `src/screens/payment/PaymentScreen.js`
   - For UPI: Update the VPA in `src/screens/payment/PaymentScreen.js`

2. Ensure the AndroidManifest.xml has the necessary permissions and activities.

3. Check that the Razorpay theme is properly defined in styles.xml.
