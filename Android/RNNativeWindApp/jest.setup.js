// Mock the react-native-reanimated module
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  
  // The mock for `call` immediately calls the callback which is incorrect
  // So we override it with a no-op
  Reanimated.default.call = () => {};
  
  return Reanimated;
});

// Mock the react-native-gesture-handler module
jest.mock('react-native-gesture-handler', () => {});

// Mock the react-native-mmkv module
jest.mock('react-native-mmkv', () => ({
  MMKV: class {
    set = jest.fn();
    getString = jest.fn();
    delete = jest.fn();
  },
}));

// Mock the react-native-safe-area-context module
jest.mock('react-native-safe-area-context', () => ({
  SafeAreaProvider: ({ children }) => children,
  useSafeAreaInsets: () => ({ top: 0, right: 0, bottom: 0, left: 0 }),
}));

// Mock the react-native-vector-icons module
jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => 'Icon');

// Mock the react-native-razorpay module
jest.mock('react-native-razorpay', () => ({
  open: jest.fn(),
}));

// Mock the react-native-upi-payment module
jest.mock('react-native-upi-payment', () => ({
  initializePayment: jest.fn(),
}));

// Mock the redux-persist module
jest.mock('redux-persist', () => {
  const real = jest.requireActual('redux-persist');
  return {
    ...real,
    persistReducer: jest.fn().mockImplementation((config, reducers) => reducers),
  };
});

// Mock the redux-persist/integration/react module
jest.mock('redux-persist/integration/react', () => ({
  PersistGate: ({ children }) => children,
}));

// Mock the @react-navigation/native module
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    reset: jest.fn(),
    addListener: jest.fn(),
    removeListener: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
}));

// Mock the react-i18next module
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
    i18n: {
      changeLanguage: jest.fn(),
    },
  }),
  initReactI18next: {
    type: '3rdParty',
    init: jest.fn(),
  },
}));

// Mock the NativeModules for i18n
jest.mock('react-native', () => {
  const rn = jest.requireActual('react-native');
  rn.NativeModules.SettingsManager = {
    settings: {
      AppleLocale: 'en_US',
      AppleLanguages: ['en'],
    },
  };
  rn.NativeModules.I18nManager = {
    localeIdentifier: 'en_US',
  };
  return rn;
});
