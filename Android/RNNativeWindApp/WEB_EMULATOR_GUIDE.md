# Web-Accessible Android Emulator Guide

This guide explains how to use the web-accessible Android emulator to test your React Native app.

## Overview

We've set up an Android emulator that can be accessed through a web browser. This allows you to:

1. Test your app without a physical device
2. Access the emulator remotely from any machine with a web browser
3. Automate testing through command-line scripts

## Starting the Emulator

To start the web-accessible emulator:

```bash
./start-web-emulator.sh
```

This script:
- Creates a virtual display using Xvfb
- Starts a VNC server to capture the display
- Launches noVNC to provide web access
- Starts the Android emulator
- Waits for the emulator to fully boot

## Accessing the Emulator

Once the emulator is running, you can access it through a web browser:

```
http://YOUR_SERVER_IP:6080/vnc.html
```

Replace `YOUR_SERVER_IP` with the IP address of your Ubuntu server.

## Installing the App

To install the app on the emulator:

```bash
./install-apk-on-emulator.sh
```

This script:
- Checks if the emulator is running
- Generates the JavaScript bundle
- Installs the APK on the emulator
- Launches the app

## Troubleshooting

### Emulator Won't Start

If the emulator fails to start:

```bash
# Check if the emulator is already running
ps aux | grep emulator

# Kill any existing emulator processes
pkill -f emulator

# Try starting with more verbose output
emulator -avd web_emulator -verbose
```

### VNC Connection Issues

If you can't connect to the VNC server:

```bash
# Check if the VNC server is running
ps aux | grep x11vnc

# Restart the VNC server
pkill x11vnc
x11vnc -display :1 -nopw -forever -shared &
```

### App Installation Fails

If app installation fails:

```bash
# Check the ADB connection
adb devices

# Restart ADB server
adb kill-server
adb start-server

# Check for installation errors
adb logcat | grep "Package"
```

## Advanced Usage

### Running Automated Tests

You can run automated tests on the emulator:

```bash
# Install Appium
npm install -g appium

# Start Appium server
appium &

# Run your test script
node automated-test.js
```

### Capturing Screenshots

To capture screenshots from the emulator:

```bash
adb shell screencap -p /sdcard/screenshot.png
adb pull /sdcard/screenshot.png
```

### Recording Video

To record video of the emulator:

```bash
adb shell screenrecord /sdcard/recording.mp4
# Press Ctrl+C to stop recording
adb pull /sdcard/recording.mp4
```

## Security Considerations

The VNC server is configured without a password for simplicity. In a production environment, you should:

1. Set a VNC password
2. Use SSL/TLS encryption
3. Restrict access using a firewall
4. Consider using SSH tunneling for secure remote access
