import { useEffect, useState } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { showToast } from '../store/slices/uiSlice';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

/**
 * Custom hook to monitor network status
 * @returns {Object} Network status information
 */
export const useNetworkStatus = () => {
  const [isConnected, setIsConnected] = useState(true);
  const [connectionType, setConnectionType] = useState(null);
  const [isInternetReachable, setIsInternetReachable] = useState(true);
  const dispatch = useDispatch();
  const { t } = useTranslation();

  useEffect(() => {
    // Subscribe to network status updates
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      setIsInternetReachable(state.isInternetReachable);
      
      // Show toast when connection is lost or restored
      if (state.isConnected === false) {
        dispatch(showToast({
          message: t('networkOffline'),
          type: 'error',
        }));
      } else if (state.isConnected === true && state.isInternetReachable === false) {
        dispatch(showToast({
          message: t('noInternetAccess'),
          type: 'warning',
        }));
      } else if (state.isConnected === true && state.isInternetReachable === true) {
        dispatch(showToast({
          message: t('networkOnline'),
          type: 'success',
        }));
      }
    });

    // Fetch initial network state
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      setIsInternetReachable(state.isInternetReachable);
    });

    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  return {
    isConnected,
    connectionType,
    isInternetReachable,
  };
};

/**
 * Check if the device is currently connected to the internet
 * @returns {Promise<boolean>} Whether the device is connected
 */
export const checkNetworkConnection = async () => {
  const state = await NetInfo.fetch();
  return state.isConnected && state.isInternetReachable;
};

export default {
  useNetworkStatus,
  checkNetworkConnection,
};
