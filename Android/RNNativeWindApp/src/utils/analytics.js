import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { MMKV } from 'react-native-mmkv';
import { API } from '../constants';

// Create a dedicated storage instance for analytics
const analyticsStorage = new MMKV({ id: 'analytics' });

// Generate a unique user ID if not already present
const getUserId = () => {
  let userId = analyticsStorage.getString('user_id');
  
  if (!userId) {
    userId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    analyticsStorage.set('user_id', userId);
  }
  
  return userId;
};

// Get device information
const getDeviceInfo = async () => {
  return {
    deviceId: await DeviceInfo.getUniqueId(),
    deviceModel: DeviceInfo.getModel(),
    deviceBrand: DeviceInfo.getBrand(),
    osName: Platform.OS,
    osVersion: Platform.Version.toString(),
    appVersion: DeviceInfo.getVersion(),
    appBuildNumber: DeviceInfo.getBuildNumber(),
  };
};

// Track event
export const trackEvent = async (eventName, eventProperties = {}) => {
  try {
    const userId = getUserId();
    const deviceInfo = await getDeviceInfo();
    const timestamp = new Date().toISOString();
    
    const eventData = {
      event_name: eventName,
      event_properties: eventProperties,
      user_id: userId,
      device_info: deviceInfo,
      timestamp,
    };
    
    // In a real app, you would send this data to your analytics service
    console.log('Analytics Event:', eventData);
    
    // For demonstration purposes, we'll store events locally
    const events = analyticsStorage.getString('events') || '[]';
    const eventsArray = JSON.parse(events);
    eventsArray.push(eventData);
    
    // Limit stored events to prevent excessive storage usage
    if (eventsArray.length > 100) {
      eventsArray.shift(); // Remove oldest event
    }
    
    analyticsStorage.set('events', JSON.stringify(eventsArray));
    
    // In a production app, you would send the event to your analytics service
    // Example: sendToAnalyticsService(eventData);
    
    return true;
  } catch (error) {
    console.error('Error tracking event:', error);
    return false;
  }
};

// Track screen view
export const trackScreenView = async (screenName, screenProperties = {}) => {
  return trackEvent('screen_view', {
    screen_name: screenName,
    ...screenProperties,
  });
};

// Track user action
export const trackUserAction = async (actionName, actionProperties = {}) => {
  return trackEvent('user_action', {
    action_name: actionName,
    ...actionProperties,
  });
};

// Track error
export const trackError = async (errorName, errorProperties = {}) => {
  return trackEvent('error', {
    error_name: errorName,
    ...errorProperties,
  });
};

// Example function to send events to an analytics service
const sendToAnalyticsService = async (eventData) => {
  try {
    // This is a placeholder for a real analytics service API call
    const response = await fetch(`${API.BASE_URL}/analytics/track`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(eventData),
    });
    
    return response.ok;
  } catch (error) {
    console.error('Error sending event to analytics service:', error);
    return false;
  }
};

// Get stored events (for debugging)
export const getStoredEvents = () => {
  const events = analyticsStorage.getString('events') || '[]';
  return JSON.parse(events);
};

// Clear stored events
export const clearStoredEvents = () => {
  analyticsStorage.delete('events');
};

export default {
  trackEvent,
  trackScreenView,
  trackUserAction,
  trackError,
  getStoredEvents,
  clearStoredEvents,
};
