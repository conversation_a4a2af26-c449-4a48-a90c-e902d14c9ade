import { MMKV } from 'react-native-mmkv';
import { checkNetworkConnection } from './networkMonitor';

// Create a dedicated storage instance for offline data
const offlineStorage = new MMKV({ id: 'offline-data' });

// Default expiration time (in milliseconds)
const DEFAULT_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Save data for offline use
 * @param {string} key - Storage key
 * @param {Object} data - Data to store
 * @param {number} expiryTime - Expiration time in milliseconds
 */
export const saveOfflineData = (key, data, expiryTime = DEFAULT_EXPIRY) => {
  const offlineData = {
    data,
    expiry: Date.now() + expiryTime,
    timestamp: Date.now(),
  };
  
  offlineStorage.set(key, JSON.stringify(offlineData));
};

/**
 * Get offline data
 * @param {string} key - Storage key
 * @returns {Object|null} - Stored data or null if not found or expired
 */
export const getOfflineData = (key) => {
  const storedData = offlineStorage.getString(key);
  
  if (!storedData) {
    return null;
  }
  
  try {
    const { data, expiry } = JSON.parse(storedData);
    
    // Check if data has expired
    if (Date.now() > expiry) {
      // Remove expired data
      offlineStorage.delete(key);
      return null;
    }
    
    return data;
  } catch (error) {
    // If there's an error parsing the data, remove it
    offlineStorage.delete(key);
    return null;
  }
};

/**
 * Clear all offline data
 */
export const clearOfflineData = () => {
  offlineStorage.clearAll();
};

/**
 * Clear specific offline data
 * @param {string} key - Storage key to clear
 */
export const clearOfflineDataKey = (key) => {
  offlineStorage.delete(key);
};

/**
 * Get data with offline fallback
 * @param {Function} fetchFunction - Function to fetch data
 * @param {string} storageKey - Storage key for offline data
 * @param {number} expiryTime - Expiration time in milliseconds
 * @returns {Promise<Object>} - Fetched or offline data
 */
export const getDataWithOfflineFallback = async (fetchFunction, storageKey, expiryTime = DEFAULT_EXPIRY) => {
  // Check network connection
  const isConnected = await checkNetworkConnection();
  
  if (isConnected) {
    try {
      // Try to fetch fresh data
      const freshData = await fetchFunction();
      
      // Save for offline use if successful
      if (freshData.success) {
        saveOfflineData(storageKey, freshData, expiryTime);
      }
      
      return freshData;
    } catch (error) {
      // If fetch fails, try to use offline data
      const offlineData = getOfflineData(storageKey);
      
      if (offlineData) {
        return {
          ...offlineData,
          isOfflineData: true,
        };
      }
      
      // If no offline data, return error
      return {
        success: false,
        error: error.message,
        isOffline: true,
      };
    }
  } else {
    // If offline, try to use offline data
    const offlineData = getOfflineData(storageKey);
    
    if (offlineData) {
      return {
        ...offlineData,
        isOfflineData: true,
      };
    }
    
    // If no offline data, return error
    return {
      success: false,
      error: 'No network connection and no offline data available',
      isOffline: true,
    };
  }
};

/**
 * Queue an action for later execution when online
 * @param {string} actionType - Type of action
 * @param {Object} actionData - Action data
 */
export const queueOfflineAction = (actionType, actionData) => {
  // Get existing queue
  const queueString = offlineStorage.getString('action_queue') || '[]';
  const queue = JSON.parse(queueString);
  
  // Add new action to queue
  queue.push({
    id: Date.now().toString(),
    type: actionType,
    data: actionData,
    timestamp: Date.now(),
  });
  
  // Save updated queue
  offlineStorage.set('action_queue', JSON.stringify(queue));
};

/**
 * Get queued offline actions
 * @returns {Array} - Array of queued actions
 */
export const getQueuedActions = () => {
  const queueString = offlineStorage.getString('action_queue') || '[]';
  return JSON.parse(queueString);
};

/**
 * Remove an action from the queue
 * @param {string} actionId - ID of action to remove
 */
export const removeQueuedAction = (actionId) => {
  const queueString = offlineStorage.getString('action_queue') || '[]';
  const queue = JSON.parse(queueString);
  
  const updatedQueue = queue.filter(action => action.id !== actionId);
  
  offlineStorage.set('action_queue', JSON.stringify(updatedQueue));
};

/**
 * Process queued actions
 * @param {Object} processors - Map of action processors
 * @returns {Promise<Array>} - Results of processed actions
 */
export const processQueuedActions = async (processors) => {
  const queue = getQueuedActions();
  const results = [];
  
  for (const action of queue) {
    const processor = processors[action.type];
    
    if (processor) {
      try {
        const result = await processor(action.data);
        results.push({ id: action.id, success: true, result });
        removeQueuedAction(action.id);
      } catch (error) {
        results.push({ id: action.id, success: false, error: error.message });
      }
    } else {
      results.push({ id: action.id, success: false, error: 'No processor found for action type' });
    }
  }
  
  return results;
};

export default {
  saveOfflineData,
  getOfflineData,
  clearOfflineData,
  clearOfflineDataKey,
  getDataWithOfflineFallback,
  queueOfflineAction,
  getQueuedActions,
  removeQueuedAction,
  processQueuedActions,
};
