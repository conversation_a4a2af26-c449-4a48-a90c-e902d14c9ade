import { MMKV } from 'react-native-mmkv';
import { API } from '../constants';

// Create a dedicated storage instance for API cache
const apiCacheStorage = new MMKV({ id: 'api-cache' });

// Default cache expiration time (in milliseconds)
const DEFAULT_CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data for a specific key
 * @param {string} key - Cache key
 * @returns {Object|null} - Cached data or null if not found or expired
 */
export const getCachedData = (key) => {
  const cachedData = apiCacheStorage.getString(key);
  
  if (!cachedData) {
    return null;
  }
  
  try {
    const { data, expiry } = JSON.parse(cachedData);
    
    // Check if cache has expired
    if (Date.now() > expiry) {
      // Remove expired cache
      apiCacheStorage.delete(key);
      return null;
    }
    
    return data;
  } catch (error) {
    // If there's an error parsing the cached data, remove it
    apiCacheStorage.delete(key);
    return null;
  }
};

/**
 * Set data in cache with expiration
 * @param {string} key - Cache key
 * @param {Object} data - Data to cache
 * @param {number} expiryTime - Cache expiration time in milliseconds
 */
export const setCachedData = (key, data, expiryTime = DEFAULT_CACHE_EXPIRY) => {
  const cacheData = {
    data,
    expiry: Date.now() + expiryTime,
  };
  
  apiCacheStorage.set(key, JSON.stringify(cacheData));
};

/**
 * Clear all cached data
 */
export const clearCache = () => {
  apiCacheStorage.clearAll();
};

/**
 * Clear specific cached data
 * @param {string} key - Cache key to clear
 */
export const clearCacheKey = (key) => {
  apiCacheStorage.delete(key);
};

/**
 * Generate a cache key for API requests
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Request parameters
 * @returns {string} - Cache key
 */
export const generateCacheKey = (endpoint, params = {}) => {
  return `${endpoint}:${JSON.stringify(params)}`;
};

/**
 * Fetch data with caching
 * @param {Function} fetchFunction - Function to fetch data
 * @param {string} cacheKey - Cache key
 * @param {number} expiryTime - Cache expiration time in milliseconds
 * @returns {Promise<Object>} - Fetched or cached data
 */
export const fetchWithCache = async (fetchFunction, cacheKey, expiryTime = DEFAULT_CACHE_EXPIRY) => {
  // Check if we have cached data
  const cachedData = getCachedData(cacheKey);
  
  if (cachedData) {
    return cachedData;
  }
  
  // If no cached data, fetch fresh data
  const freshData = await fetchFunction();
  
  // Cache the fresh data
  if (freshData.success) {
    setCachedData(cacheKey, freshData, expiryTime);
  }
  
  return freshData;
};

export default {
  getCachedData,
  setCachedData,
  clearCache,
  clearCacheKey,
  generateCacheKey,
  fetchWithCache,
};
