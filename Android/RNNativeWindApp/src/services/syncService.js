import { NetInfo } from '@react-native-community/netinfo';
import { getQueuedActions, processQueuedActions, removeQueuedAction } from '../utils/offlineStorage';
import { addToCart, updateCart, applyCoupon } from '../api/odooApi';
import { store } from '../store';
import { showToast } from '../store/slices/uiSlice';
import { fetchCart } from '../store/slices/cartSlice';

// Action processors map
const actionProcessors = {
  ADD_TO_CART: async (data) => {
    const { productId, quantity, sessionId } = data;
    return await addToCart(productId, quantity, sessionId);
  },
  UPDATE_CART: async (data) => {
    const { lineId, quantity, sessionId } = data;
    return await updateCart(lineId, quantity, sessionId);
  },
  APPLY_COUPON: async (data) => {
    const { couponCode, sessionId } = data;
    return await applyCoupon(couponCode, sessionId);
  },
};

/**
 * Process all queued actions
 * @returns {Promise<Array>} - Results of processed actions
 */
export const syncQueuedActions = async () => {
  const results = await processQueuedActions(actionProcessors);
  
  // If any actions were processed successfully, refresh cart
  if (results.some(result => result.success)) {
    const { auth } = store.getState();
    if (auth.isAuthenticated) {
      store.dispatch(fetchCart());
    }
    
    // Show toast notification
    store.dispatch(showToast({
      message: 'Offline actions synchronized successfully',
      type: 'success',
    }));
  }
  
  return results;
};

/**
 * Start background synchronization service
 */
export const startSyncService = () => {
  // Check for queued actions on app start
  const checkQueuedActions = async () => {
    const queuedActions = getQueuedActions();
    
    if (queuedActions.length > 0) {
      // Check network connection
      const state = await NetInfo.fetch();
      
      if (state.isConnected && state.isInternetReachable) {
        await syncQueuedActions();
      }
    }
  };
  
  // Run initial check
  checkQueuedActions();
  
  // Subscribe to network state changes
  const unsubscribe = NetInfo.addEventListener(async (state) => {
    if (state.isConnected && state.isInternetReachable) {
      // When connection is restored, sync queued actions
      const queuedActions = getQueuedActions();
      
      if (queuedActions.length > 0) {
        await syncQueuedActions();
      }
    }
  });
  
  // Return unsubscribe function
  return unsubscribe;
};

export default {
  syncQueuedActions,
  startSyncService,
};
