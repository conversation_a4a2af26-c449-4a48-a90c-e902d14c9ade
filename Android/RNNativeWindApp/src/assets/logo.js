import React from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const StyledView = styled(View);
const StyledText = styled(Text);

const Logo = ({ size = 100, showText = false }) => {
  return (
    <StyledView className="items-center">
      <StyledView 
        style={{ 
          width: size, 
          height: size, 
          borderRadius: size / 2 
        }}
        className="bg-white items-center justify-center"
      >
        <Icon 
          name="shopping" 
          size={size * 0.6} 
          color="#0284c7" 
        />
      </StyledView>
      
      {showText && (
        <StyledText 
          className="text-white font-bold mt-2"
          style={{ fontSize: size * 0.2 }}
        >
          Siddhi eCommerce
        </StyledText>
      )}
    </StyledView>
  );
};

export default Logo;
