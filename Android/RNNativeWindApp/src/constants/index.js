// API Constants
export const API = {
  BASE_URL: 'http://104.225.216.83:8069',
  TIMEOUT: 10000,
  DB_NAME: 'siddhi_ecommerce',
};

// Navigation Routes
export const ROUTES = {
  SPLASH: 'Splash',
  LOGIN: 'Login',
  REGISTER: 'Register',
  FORGOT_PASSWORD: 'ForgotPassword',
  HOME: 'Home',
  CATEGORIES: 'Categories',
  PRODUCTS: 'Products',
  PRODUCT_DETAILS: 'ProductDetails',
  CART: 'Cart',
  CHECKOUT: 'Checkout',
  PAYMENT: 'Payment',
  PAYMENT_SUCCESS: 'PaymentSuccess',
  ORDERS: 'Orders',
  ORDER_DETAILS: 'OrderDetails',
  PROFILE: 'Profile',
  EDIT_PROFILE: 'EditProfile',
  ADDRESSES: 'Addresses',
  ADD_ADDRESS: 'AddAddress',
  SETTINGS: 'Settings',
};

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  CART: 'cart',
  THEME: 'theme',
  LANGUAGE: 'language',
};

// Payment Methods
export const PAYMENT_METHODS = {
  RAZORPAY: 'razorpay',
  UPI: 'upi',
  CREDIT_CARD: 'credit_card',
  DEBIT_CARD: 'debit_card',
  NET_BANKING: 'net_banking',
  WALLET: 'wallet',
};

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
};

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded',
};

// Languages
export const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'hi', name: 'हिंदी' },
];

// Regex Patterns
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[0-9]{10}$/,
  PIN_CODE: /^[0-9]{6}$/,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your internet connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  INVALID_CREDENTIALS: 'Invalid email or password.',
  REQUIRED_FIELD: 'This field is required.',
  INVALID_EMAIL: 'Please enter a valid email address.',
  INVALID_PHONE: 'Please enter a valid 10-digit phone number.',
  INVALID_PIN_CODE: 'Please enter a valid 6-digit PIN code.',
  PASSWORD_MISMATCH: 'Passwords do not match.',
  MINIMUM_PASSWORD_LENGTH: 'Password must be at least 8 characters long.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful.',
  REGISTER_SUCCESS: 'Registration successful.',
  PASSWORD_RESET: 'Password reset email sent.',
  ORDER_PLACED: 'Order placed successfully.',
  PAYMENT_SUCCESS: 'Payment successful.',
  PROFILE_UPDATED: 'Profile updated successfully.',
  ADDRESS_ADDED: 'Address added successfully.',
  ADDRESS_UPDATED: 'Address updated successfully.',
  ADDRESS_DELETED: 'Address deleted successfully.',
};

export default {
  API,
  ROUTES,
  STORAGE_KEYS,
  PAYMENT_METHODS,
  ORDER_STATUS,
  PAYMENT_STATUS,
  LANGUAGES,
  REGEX,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
};
