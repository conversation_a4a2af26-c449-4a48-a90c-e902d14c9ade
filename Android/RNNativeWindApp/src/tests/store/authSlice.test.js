import authReducer, { logout, clearError, login } from '../../store/slices/authSlice';
import { authenticate } from '../../api/odooApi';

// Mock the odooApi module
jest.mock('../../api/odooApi', () => ({
  authenticate: jest.fn(),
}));

describe('Auth Slice', () => {
  const initialState = {
    isAuthenticated: false,
    user: null,
    sessionId: null,
    uid: null,
    loading: false,
    error: null,
  };

  it('should handle initial state', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('should handle logout', () => {
    const authenticatedState = {
      isAuthenticated: true,
      user: { name: 'Test User' },
      sessionId: 'test-session-id',
      uid: 123,
      loading: false,
      error: null,
    };

    expect(authReducer(authenticatedState, logout())).toEqual(initialState);
  });

  it('should handle clearError', () => {
    const stateWithError = {
      ...initialState,
      error: 'Test error',
    };

    expect(authReducer(stateWithError, clearError())).toEqual(initialState);
  });

  it('should handle login.pending', () => {
    const action = { type: login.pending.type };
    const state = authReducer(initialState, action);

    expect(state).toEqual({
      ...initialState,
      loading: true,
    });
  });

  it('should handle login.fulfilled', () => {
    const payload = {
      success: true,
      userData: { name: 'Test User' },
      sessionId: 'test-session-id',
      uid: 123,
    };

    const action = { type: login.fulfilled.type, payload };
    const state = authReducer(initialState, action);

    expect(state).toEqual({
      isAuthenticated: true,
      user: payload.userData,
      sessionId: payload.sessionId,
      uid: payload.uid,
      loading: false,
      error: null,
    });
  });

  it('should handle login.rejected', () => {
    const action = { 
      type: login.rejected.type, 
      payload: 'Authentication failed' 
    };
    
    const state = authReducer(initialState, action);

    expect(state).toEqual({
      ...initialState,
      loading: false,
      error: 'Authentication failed',
    });
  });

  // Test the login thunk
  it('should create login.fulfilled when authentication succeeds', async () => {
    const mockResponse = {
      success: true,
      userData: { name: 'Test User' },
      sessionId: 'test-session-id',
      uid: 123,
    };

    authenticate.mockResolvedValue(mockResponse);

    const dispatch = jest.fn();
    const thunk = login({ username: 'testuser', password: 'password' });

    await thunk(dispatch, () => ({}));

    const { calls } = dispatch.mock;
    expect(calls[0][0].type).toBe(login.pending.type);
    expect(calls[1][0].type).toBe(login.fulfilled.type);
    expect(calls[1][0].payload).toEqual(mockResponse);
  });

  it('should create login.rejected when authentication fails', async () => {
    const mockResponse = {
      success: false,
      error: 'Invalid credentials',
    };

    authenticate.mockResolvedValue(mockResponse);

    const dispatch = jest.fn();
    const thunk = login({ username: 'testuser', password: 'password' });

    await thunk(dispatch, () => ({}));

    const { calls } = dispatch.mock;
    expect(calls[0][0].type).toBe(login.pending.type);
    expect(calls[1][0].type).toBe(login.rejected.type);
    expect(calls[1][0].payload).toBe(mockResponse.error);
  });
});
