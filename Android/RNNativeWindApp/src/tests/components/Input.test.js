import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Input from '../../components/Input';

// Mock NativeWind's styled function
jest.mock('nativewind', () => ({
  styled: (component) => component,
}));

// Mock react-native-vector-icons
jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => 'Icon');

describe('Input Component', () => {
  it('renders correctly with default props', () => {
    const { getByPlaceholderText } = render(
      <Input 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={() => {}} 
      />
    );
    
    expect(getByPlaceholderText('Test Placeholder')).toBeTruthy();
  });

  it('displays the label when provided', () => {
    const { getByText } = render(
      <Input 
        label="Test Label" 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={() => {}} 
      />
    );
    
    expect(getByText('Test Label')).toBeTruthy();
  });

  it('calls onChangeText when text changes', () => {
    const onChangeTextMock = jest.fn();
    const { getByPlaceholderText } = render(
      <Input 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={onChangeTextMock} 
      />
    );
    
    fireEvent.changeText(getByPlaceholderText('Test Placeholder'), 'New Value');
    expect(onChangeTextMock).toHaveBeenCalledWith('New Value');
  });

  it('displays error message when error prop is provided', () => {
    const { getByText } = render(
      <Input 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={() => {}} 
        error="Test Error" 
      />
    );
    
    expect(getByText('Test Error')).toBeTruthy();
  });

  it('applies secureTextEntry when specified', () => {
    const { getByPlaceholderText } = render(
      <Input 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={() => {}} 
        secureTextEntry={true} 
      />
    );
    
    expect(getByPlaceholderText('Test Placeholder').props.secureTextEntry).toBe(true);
  });

  it('renders left icon when leftIcon prop is provided', () => {
    const { getByTestId } = render(
      <Input 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={() => {}} 
        leftIcon="account" 
      />
    );
    
    expect(getByTestId('left-icon')).toBeTruthy();
  });

  it('renders right icon when rightIcon prop is provided', () => {
    const { getByTestId } = render(
      <Input 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={() => {}} 
        rightIcon="eye" 
      />
    );
    
    expect(getByTestId('right-icon')).toBeTruthy();
  });

  it('calls onRightIconPress when right icon is pressed', () => {
    const onRightIconPressMock = jest.fn();
    const { getByTestId } = render(
      <Input 
        placeholder="Test Placeholder" 
        value="" 
        onChangeText={() => {}} 
        rightIcon="eye" 
        onRightIconPress={onRightIconPressMock} 
      />
    );
    
    fireEvent.press(getByTestId('right-icon-container'));
    expect(onRightIconPressMock).toHaveBeenCalledTimes(1);
  });
});
