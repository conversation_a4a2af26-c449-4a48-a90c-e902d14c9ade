import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProductCard from '../../components/ProductCard';

// Mock dependencies
jest.mock('nativewind', () => ({
  styled: (component) => component,
}));

jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => 'Icon');

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
  }),
}));

jest.mock('react-redux', () => ({
  useDispatch: () => jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

describe('ProductCard Component', () => {
  const mockProduct = {
    id: 1,
    name: 'Test Product',
    list_price: 99.99,
    image_1920: 'base64encodedimage',
  };

  it('renders correctly with product data', () => {
    const { getByText } = render(<ProductCard product={mockProduct} />);
    
    expect(getByText('Test Product')).toBeTruthy();
    expect(getByText('₹99.99')).toBeTruthy();
    expect(getByText('addToCart')).toBeTruthy();
  });

  it('renders horizontal layout when horizontal prop is true', () => {
    const { getByTestId } = render(<ProductCard product={mockProduct} horizontal={true} />);
    
    const container = getByTestId('product-card');
    expect(container.props.className).toContain('flex-row w-full');
  });

  it('renders vertical layout by default', () => {
    const { getByTestId } = render(<ProductCard product={mockProduct} />);
    
    const container = getByTestId('product-card');
    expect(container.props.className).toContain('w-[48%] mx-1 mb-3');
  });

  it('uses placeholder image when product image is not available', () => {
    const productWithoutImage = { ...mockProduct, image_1920: null };
    const { getByTestId } = render(<ProductCard product={productWithoutImage} />);
    
    const image = getByTestId('product-image');
    expect(image.props.source.uri).toBe('https://via.placeholder.com/150');
  });

  it('navigates to product details when card is pressed', () => {
    const mockNavigate = jest.fn();
    jest.spyOn(require('@react-navigation/native'), 'useNavigation').mockImplementation(() => ({
      navigate: mockNavigate,
    }));

    const { getByTestId } = render(<ProductCard product={mockProduct} />);
    
    fireEvent.press(getByTestId('product-card'));
    expect(mockNavigate).toHaveBeenCalledWith('ProductDetails', { productId: 1 });
  });

  it('dispatches addItemToCart action when add to cart button is pressed', () => {
    const mockDispatch = jest.fn();
    jest.spyOn(require('react-redux'), 'useDispatch').mockImplementation(() => mockDispatch);

    const { getByTestId } = render(<ProductCard product={mockProduct} />);
    
    fireEvent.press(getByTestId('add-to-cart-button'));
    expect(mockDispatch).toHaveBeenCalled();
  });
});
