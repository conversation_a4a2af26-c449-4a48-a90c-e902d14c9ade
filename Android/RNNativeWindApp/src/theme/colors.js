// Color palette for the app
export const colors = {
  // Primary colors
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#082f49',
  },
  
  // Secondary colors
  secondary: {
    50: '#f5f3ff',
    100: '#ede9fe',
    200: '#ddd6fe',
    300: '#c4b5fd',
    400: '#a78bfa',
    500: '#8b5cf6',
    600: '#7c3aed',
    700: '#6d28d9',
    800: '#5b21b6',
    900: '#4c1d95',
    950: '#2e1065',
  },
  
  // Accent colors
  accent: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316',
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
    950: '#431407',
  },
  
  // Neutral colors
  neutral: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712',
  },
  
  // Semantic colors
  success: {
    light: '#86efac',
    default: '#22c55e',
    dark: '#15803d',
  },
  
  warning: {
    light: '#fde68a',
    default: '#f59e0b',
    dark: '#b45309',
  },
  
  error: {
    light: '#fca5a5',
    default: '#ef4444',
    dark: '#b91c1c',
  },
  
  info: {
    light: '#93c5fd',
    default: '#3b82f6',
    dark: '#1d4ed8',
  },
};

// Theme-specific colors
export const lightTheme = {
  background: colors.neutral[50],
  surface: '#ffffff',
  text: colors.neutral[900],
  textSecondary: colors.neutral[600],
  border: colors.neutral[200],
  divider: colors.neutral[200],
  ...colors,
};

export const darkTheme = {
  background: colors.neutral[900],
  surface: colors.neutral[800],
  text: colors.neutral[50],
  textSecondary: colors.neutral[400],
  border: colors.neutral[700],
  divider: colors.neutral[700],
  ...colors,
};

export default {
  light: lightTheme,
  dark: darkTheme,
};
