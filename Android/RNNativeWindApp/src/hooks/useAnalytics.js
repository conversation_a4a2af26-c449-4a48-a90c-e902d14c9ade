import { useEffect } from 'react';
import { useRoute, useNavigation } from '@react-navigation/native';
import { trackScreenView, trackUserAction, trackError } from '../utils/analytics';

/**
 * Custom hook to track screen views
 * @param {string} screenName - Optional screen name override
 * @param {Object} screenProperties - Additional screen properties
 */
export const useScreenTracking = (screenName, screenProperties = {}) => {
  const route = useRoute();
  
  useEffect(() => {
    // Use provided screen name or get from route
    const name = screenName || route.name;
    
    // Track screen view
    trackScreenView(name, {
      ...screenProperties,
      route_params: route.params || {},
    });
  }, [screenName, route.name, route.params]);
};

/**
 * Custom hook to track navigation events
 */
export const useNavigationTracking = () => {
  const navigation = useNavigation();
  
  useEffect(() => {
    // Track navigation events
    const unsubscribeNavigate = navigation.addListener('navigate', (e) => {
      trackUserAction('navigate', {
        to_route: e.target,
        from_route: e.source,
        params: e.data?.params || {},
      });
    });
    
    const unsubscribeGoBack = navigation.addListener('goBack', (e) => {
      trackUserAction('go_back', {
        from_route: e.source,
      });
    });
    
    // Cleanup listeners
    return () => {
      unsubscribeNavigate();
      unsubscribeGoBack();
    };
  }, [navigation]);
};

/**
 * Track a user action
 * @param {string} actionName - Name of the action
 * @param {Object} actionProperties - Additional action properties
 */
export const trackAction = (actionName, actionProperties = {}) => {
  trackUserAction(actionName, actionProperties);
};

/**
 * Track an error
 * @param {string} errorName - Name of the error
 * @param {Object} errorProperties - Additional error properties
 */
export const trackException = (errorName, errorProperties = {}) => {
  trackError(errorName, errorProperties);
};

export default {
  useScreenTracking,
  useNavigationTracking,
  trackAction,
  trackException,
};
