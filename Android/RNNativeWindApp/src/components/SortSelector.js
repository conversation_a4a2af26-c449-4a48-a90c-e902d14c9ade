import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { styled } from 'nativewind';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const SORT_OPTIONS = [
  { id: 'name_asc', label: 'nameAZ', sortBy: 'name', sortOrder: 'asc', icon: 'sort-alphabetical-ascending' },
  { id: 'name_desc', label: 'nameZA', sortBy: 'name', sortOrder: 'desc', icon: 'sort-alphabetical-descending' },
  { id: 'price_asc', label: 'priceLowHigh', sortBy: 'list_price', sortOrder: 'asc', icon: 'sort-numeric-ascending' },
  { id: 'price_desc', label: 'priceHighLow', sortBy: 'list_price', sortOrder: 'desc', icon: 'sort-numeric-descending' },
  { id: 'date_desc', label: 'newest', sortBy: 'create_date', sortOrder: 'desc', icon: 'sort-calendar-descending' },
  { id: 'date_asc', label: 'oldest', sortBy: 'create_date', sortOrder: 'asc', icon: 'sort-calendar-ascending' },
];

const SortSelector = ({ sortBy, sortOrder, onSortChange }) => {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);

  const getCurrentSortOption = () => {
    return SORT_OPTIONS.find(option => option.sortBy === sortBy && option.sortOrder === sortOrder) || SORT_OPTIONS[0];
  };

  const handleSelect = (option) => {
    onSortChange({
      sortBy: option.sortBy,
      sortOrder: option.sortOrder
    });
    setModalVisible(false);
  };

  const currentOption = getCurrentSortOption();

  return (
    <>
      <StyledTouchableOpacity
        onPress={() => setModalVisible(true)}
        className="flex-row items-center px-3 py-1 bg-neutral-100 dark:bg-neutral-700 rounded-full"
      >
        <Icon name={currentOption.icon} size={18} color="#6b7280" />
        <StyledText className="ml-1 text-neutral-700 dark:text-neutral-300">
          {t(currentOption.label)}
        </StyledText>
      </StyledTouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <StyledTouchableOpacity
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
          className="flex-1 justify-center items-center bg-black/50"
        >
          <AnimatedView
            animation="fadeInUp"
            duration={300}
            className="w-64 bg-white dark:bg-neutral-800 rounded-lg overflow-hidden"
            style={{ elevation: 5 }}
          >
            <StyledView className="p-4 border-b border-neutral-200 dark:border-neutral-700">
              <StyledText className="text-neutral-900 dark:text-white text-lg font-medium">
                {t('sortBy')}
              </StyledText>
            </StyledView>

            {SORT_OPTIONS.map((option) => {
              const isSelected = option.sortBy === sortBy && option.sortOrder === sortOrder;
              
              return (
                <StyledTouchableOpacity
                  key={option.id}
                  onPress={() => handleSelect(option)}
                  className={`p-4 border-b border-neutral-200 dark:border-neutral-700 flex-row justify-between items-center ${
                    isSelected ? 'bg-primary-50 dark:bg-primary-900' : ''
                  }`}
                >
                  <StyledView className="flex-row items-center">
                    <Icon 
                      name={option.icon} 
                      size={20} 
                      color={isSelected ? "#4f46e5" : "#6b7280"} 
                    />
                    <StyledText
                      className={`ml-2 ${
                        isSelected
                          ? 'text-primary-600 dark:text-primary-400 font-medium'
                          : 'text-neutral-700 dark:text-neutral-300'
                      }`}
                    >
                      {t(option.label)}
                    </StyledText>
                  </StyledView>

                  {isSelected && (
                    <Icon name="check" size={20} color="#4f46e5" />
                  )}
                </StyledTouchableOpacity>
              );
            })}
          </AnimatedView>
        </StyledTouchableOpacity>
      </Modal>
    </>
  );
};

export default SortSelector;
