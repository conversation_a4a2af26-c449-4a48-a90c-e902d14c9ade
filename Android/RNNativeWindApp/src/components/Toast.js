import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { styled } from 'nativewind';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { hideToast } from '../store/slices/uiSlice';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledAnimatedView = styled(Animated.View);

const Toast = () => {
  const dispatch = useDispatch();
  const { toast } = useSelector((state) => state.ui);
  const translateY = new Animated.Value(-100);
  
  useEffect(() => {
    if (toast.visible) {
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        friction: 8,
      }).start();
      
      const timer = setTimeout(() => {
        handleHide();
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [toast.visible]);
  
  const handleHide = () => {
    Animated.timing(translateY, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      dispatch(hideToast());
    });
  };
  
  if (!toast.visible) {
    return null;
  }
  
  // Toast type styles
  const typeStyles = {
    info: 'bg-info-default',
    success: 'bg-success-default',
    error: 'bg-error-default',
    warning: 'bg-warning-default',
  };
  
  // Toast icons
  const typeIcons = {
    info: 'information',
    success: 'check-circle',
    error: 'alert-circle',
    warning: 'alert',
  };
  
  return (
    <StyledAnimatedView
      className="absolute top-10 left-4 right-4 z-50"
      style={{ transform: [{ translateY }] }}
    >
      <StyledView
        className={`${
          typeStyles[toast.type]
        } rounded-lg shadow-md px-4 py-3 flex-row items-center`}
      >
        <Icon name={typeIcons[toast.type]} size={24} color="#ffffff" />
        
        <StyledText className="flex-1 text-white font-medium ml-3">
          {toast.message}
        </StyledText>
        
        <StyledTouchableOpacity onPress={handleHide}>
          <Icon name="close" size={20} color="#ffffff" />
        </StyledTouchableOpacity>
      </StyledView>
    </StyledAnimatedView>
  );
};

export default Toast;
