import React, { Component } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { styled } from 'nativewind';
import Button from './Button';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
    
    // Here you would typically log to a service like Sentry, Firebase Crashlytics, etc.
  }

  resetError = () => {
    this.setState({ 
      hasError: false,
      error: null,
      errorInfo: null
    });
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <StyledView className="flex-1 bg-white dark:bg-neutral-900 p-6 justify-center">
          <StyledView className="bg-error-light dark:bg-error-dark p-4 rounded-lg mb-4">
            <StyledText className="text-error-dark dark:text-error-light text-lg font-bold mb-2">
              Oops! Something went wrong
            </StyledText>
            <StyledText className="text-neutral-700 dark:text-neutral-300">
              The app encountered an unexpected error. Please try again.
            </StyledText>
          </StyledView>
          
          {__DEV__ && (
            <StyledScrollView className="bg-neutral-100 dark:bg-neutral-800 p-4 rounded-lg mb-4 max-h-60">
              <StyledText className="text-neutral-900 dark:text-white font-medium mb-2">
                Error Details:
              </StyledText>
              <StyledText className="text-neutral-700 dark:text-neutral-300 font-mono text-xs">
                {this.state.error && this.state.error.toString()}
                {'\n\n'}
                {this.state.errorInfo && this.state.errorInfo.componentStack}
              </StyledText>
            </StyledScrollView>
          )}
          
          <Button
            title="Try Again"
            onPress={this.resetError}
            fullWidth
          />
        </StyledView>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
