import React, { useState, useRef } from 'react';
import { View, FlatList, TouchableOpacity, Dimensions, Modal } from 'react-native';
import { styled } from 'nativewind';
import FastImage from 'react-native-fast-image';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';
import { PinchGestureHandler, State } from 'react-native-gesture-handler';

const StyledView = styled(View);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledImage = styled(FastImage);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const { width, height } = Dimensions.get('window');

const ImageGallery = ({ images, fallbackImage }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [zoomVisible, setZoomVisible] = useState(false);
  const [scale, setScale] = useState(1);
  const mainListRef = useRef(null);
  const thumbnailListRef = useRef(null);
  
  // Handle pinch to zoom
  const onPinchGestureEvent = ({ nativeEvent }) => {
    setScale(nativeEvent.scale);
  };
  
  const onPinchHandlerStateChange = ({ nativeEvent }) => {
    if (nativeEvent.oldState === State.ACTIVE) {
      if (nativeEvent.scale < 1) {
        setScale(1);
      }
    }
  };
  
  // Prepare images array
  const imageArray = images && images.length > 0 
    ? images 
    : fallbackImage 
      ? [{ uri: fallbackImage }] 
      : [{ uri: 'https://via.placeholder.com/300' }];
  
  // Handle image change
  const handleImageChange = (index) => {
    setActiveIndex(index);
    mainListRef.current?.scrollToIndex({ index, animated: true });
    
    // Also scroll thumbnail list if needed
    if (thumbnailListRef.current) {
      thumbnailListRef.current.scrollToIndex({
        index: Math.max(0, index - 1),
        animated: true,
        viewPosition: 0.5
      });
    }
  };
  
  // Render main image item
  const renderImageItem = ({ item, index }) => {
    return (
      <StyledTouchableOpacity
        onPress={() => setZoomVisible(true)}
        activeOpacity={0.9}
        className="w-full justify-center items-center"
        style={{ width }}
      >
        <StyledImage
          source={{
            uri: item.uri,
            priority: FastImage.priority.high,
            cache: FastImage.cacheControl.immutable
          }}
          className="w-full h-80"
          resizeMode={FastImage.resizeMode.cover}
        />
      </StyledTouchableOpacity>
    );
  };
  
  // Render thumbnail item
  const renderThumbnailItem = ({ item, index }) => {
    const isActive = index === activeIndex;
    
    return (
      <StyledTouchableOpacity
        onPress={() => handleImageChange(index)}
        className={`mr-2 rounded-md overflow-hidden border-2 ${
          isActive 
            ? 'border-primary-600 dark:border-primary-400' 
            : 'border-transparent'
        }`}
      >
        <StyledImage
          source={{
            uri: item.uri,
            priority: FastImage.priority.normal,
            cache: FastImage.cacheControl.immutable
          }}
          className="w-16 h-16"
          resizeMode={FastImage.resizeMode.cover}
        />
      </StyledTouchableOpacity>
    );
  };
  
  return (
    <>
      {/* Main image carousel */}
      <FlatList
        ref={mainListRef}
        data={imageArray}
        renderItem={renderImageItem}
        keyExtractor={(_, index) => `main-image-${index}`}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={(event) => {
          const newIndex = Math.round(event.nativeEvent.contentOffset.x / width);
          setActiveIndex(newIndex);
        }}
        initialScrollIndex={activeIndex}
        getItemLayout={(_, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
      />
      
      {/* Image indicators */}
      <StyledView className="absolute bottom-2 left-0 right-0 flex-row justify-center">
        {imageArray.map((_, index) => (
          <StyledView
            key={`indicator-${index}`}
            className={`w-2 h-2 rounded-full mx-1 ${
              index === activeIndex 
                ? 'bg-primary-600 dark:bg-primary-400' 
                : 'bg-neutral-300 dark:bg-neutral-700'
            }`}
          />
        ))}
      </StyledView>
      
      {/* Thumbnails */}
      {imageArray.length > 1 && (
        <StyledView className="mt-2 px-4">
          <FlatList
            ref={thumbnailListRef}
            data={imageArray}
            renderItem={renderThumbnailItem}
            keyExtractor={(_, index) => `thumbnail-${index}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerClassName="py-2"
          />
        </StyledView>
      )}
      
      {/* Zoom modal */}
      <Modal
        visible={zoomVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setZoomVisible(false)}
      >
        <StyledView className="flex-1 bg-black justify-center items-center">
          <StyledTouchableOpacity
            className="absolute top-4 right-4 z-10 bg-black/50 rounded-full p-2"
            onPress={() => {
              setScale(1);
              setZoomVisible(false);
            }}
          >
            <Icon name="close" size={24} color="#ffffff" />
          </StyledTouchableOpacity>
          
          <PinchGestureHandler
            onGestureEvent={onPinchGestureEvent}
            onHandlerStateChange={onPinchHandlerStateChange}
          >
            <AnimatedView 
              className="flex-1 justify-center items-center"
              style={{ transform: [{ scale }] }}
            >
              <StyledImage
                source={{
                  uri: imageArray[activeIndex]?.uri,
                  priority: FastImage.priority.high,
                  cache: FastImage.cacheControl.immutable
                }}
                className="w-full h-80"
                resizeMode={FastImage.resizeMode.contain}
              />
            </AnimatedView>
          </PinchGestureHandler>
        </StyledView>
      </Modal>
    </>
  );
};

export default ImageGallery;
