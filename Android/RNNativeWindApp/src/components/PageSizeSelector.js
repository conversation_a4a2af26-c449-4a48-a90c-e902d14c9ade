import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { styled } from 'nativewind';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const PAGE_SIZE_OPTIONS = [20, 50, 100];

const PageSizeSelector = ({ pageSize, onPageSizeChange }) => {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);

  const handleSelect = (size) => {
    onPageSizeChange(size);
    setModalVisible(false);
  };

  return (
    <>
      <StyledTouchableOpacity
        onPress={() => setModalVisible(true)}
        className="flex-row items-center px-3 py-1 bg-neutral-100 dark:bg-neutral-700 rounded-full"
      >
        <Icon name="format-list-numbered" size={18} color="#6b7280" />
        <StyledText className="ml-1 text-neutral-700 dark:text-neutral-300">
          {pageSize} {t('perPage')}
        </StyledText>
      </StyledTouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <StyledTouchableOpacity
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
          className="flex-1 justify-center items-center bg-black/50"
        >
          <AnimatedView
            animation="fadeInUp"
            duration={300}
            className="w-64 bg-white dark:bg-neutral-800 rounded-lg overflow-hidden"
            style={{ elevation: 5 }}
          >
            <StyledView className="p-4 border-b border-neutral-200 dark:border-neutral-700">
              <StyledText className="text-neutral-900 dark:text-white text-lg font-medium">
                {t('selectPageSize')}
              </StyledText>
            </StyledView>

            {PAGE_SIZE_OPTIONS.map((size) => (
              <StyledTouchableOpacity
                key={size}
                onPress={() => handleSelect(size)}
                className={`p-4 border-b border-neutral-200 dark:border-neutral-700 flex-row justify-between items-center ${
                  pageSize === size ? 'bg-primary-50 dark:bg-primary-900' : ''
                }`}
              >
                <StyledText
                  className={`${
                    pageSize === size
                      ? 'text-primary-600 dark:text-primary-400 font-medium'
                      : 'text-neutral-700 dark:text-neutral-300'
                  }`}
                >
                  {size} {t('items')}
                </StyledText>

                {pageSize === size && (
                  <Icon name="check" size={20} color="#4f46e5" />
                )}
              </StyledTouchableOpacity>
            ))}
          </AnimatedView>
        </StyledTouchableOpacity>
      </Modal>
    </>
  );
};

export default PageSizeSelector;
