import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { styled } from 'nativewind';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const AttributeSelector = ({ 
  attribute, 
  selectedValues, 
  onValueChange, 
  disabled = false 
}) => {
  const { t } = useTranslation();
  
  // Check if a value is selected
  const isSelected = (valueId) => {
    if (Array.isArray(selectedValues)) {
      return selectedValues.includes(valueId);
    }
    return selectedValues === valueId;
  };
  
  // Render color swatch
  const renderColorSwatch = () => {
    return (
      <StyledScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        className="mb-4"
      >
        {attribute.options.map((option) => (
          <StyledTouchableOpacity
            key={option.id}
            onPress={() => onValueChange(attribute.id, option.id)}
            disabled={disabled}
            className={`mr-3 ${disabled ? 'opacity-50' : ''}`}
          >
            <StyledView 
              className={`w-10 h-10 rounded-full justify-center items-center ${
                isSelected(option.id) 
                  ? 'border-2 border-primary-600 dark:border-primary-400' 
                  : 'border border-neutral-300 dark:border-neutral-700'
              }`}
            >
              <StyledView 
                className="w-8 h-8 rounded-full"
                style={{ backgroundColor: option.htmlColor || '#ccc' }}
              />
            </StyledView>
            <StyledText 
              className={`text-center mt-1 text-xs ${
                isSelected(option.id)
                  ? 'text-primary-600 dark:text-primary-400 font-medium'
                  : 'text-neutral-700 dark:text-neutral-300'
              }`}
            >
              {option.name}
            </StyledText>
          </StyledTouchableOpacity>
        ))}
      </StyledScrollView>
    );
  };
  
  // Render size grid
  const renderSizeGrid = () => {
    return (
      <StyledView className="flex-row flex-wrap mb-4">
        {attribute.options.map((option) => (
          <StyledTouchableOpacity
            key={option.id}
            onPress={() => onValueChange(attribute.id, option.id)}
            disabled={disabled}
            className={`border rounded-lg w-14 h-14 justify-center items-center mr-2 mb-2 ${
              isSelected(option.id)
                ? 'border-primary-600 bg-primary-50 dark:bg-primary-900'
                : 'border-neutral-300 dark:border-neutral-700'
            } ${disabled ? 'opacity-50' : ''}`}
          >
            <StyledText
              className={`text-center ${
                isSelected(option.id)
                  ? 'text-primary-600 dark:text-primary-400 font-medium'
                  : 'text-neutral-700 dark:text-neutral-300'
              }`}
            >
              {option.name}
            </StyledText>
          </StyledTouchableOpacity>
        ))}
      </StyledView>
    );
  };
  
  // Render radio buttons (single select)
  const renderRadioButtons = () => {
    return (
      <StyledView className="mb-4">
        {attribute.options.map((option) => (
          <StyledTouchableOpacity
            key={option.id}
            onPress={() => onValueChange(attribute.id, option.id)}
            disabled={disabled}
            className={`flex-row items-center py-2 border-b border-neutral-200 dark:border-neutral-700 ${disabled ? 'opacity-50' : ''}`}
          >
            <Icon
              name={isSelected(option.id) ? "radiobox-marked" : "radiobox-blank"}
              size={24}
              color={isSelected(option.id) ? "#4f46e5" : "#6b7280"}
            />
            <StyledText className="ml-2 text-neutral-700 dark:text-neutral-300">
              {option.name}
            </StyledText>
            {option.priceAdjustment > 0 && (
              <StyledText className="ml-auto text-neutral-500 dark:text-neutral-400">
                +₹{option.priceAdjustment.toFixed(2)}
              </StyledText>
            )}
          </StyledTouchableOpacity>
        ))}
      </StyledView>
    );
  };
  
  // Render checkboxes (multi select)
  const renderCheckboxes = () => {
    return (
      <StyledView className="mb-4">
        {attribute.options.map((option) => (
          <StyledTouchableOpacity
            key={option.id}
            onPress={() => onValueChange(attribute.id, option.id)}
            disabled={disabled}
            className={`flex-row items-center py-2 border-b border-neutral-200 dark:border-neutral-700 ${disabled ? 'opacity-50' : ''}`}
          >
            <Icon
              name={isSelected(option.id) ? "checkbox-marked" : "checkbox-blank-outline"}
              size={24}
              color={isSelected(option.id) ? "#4f46e5" : "#6b7280"}
            />
            <StyledText className="ml-2 text-neutral-700 dark:text-neutral-300">
              {option.name}
            </StyledText>
            {option.priceAdjustment > 0 && (
              <StyledText className="ml-auto text-neutral-500 dark:text-neutral-400">
                +₹{option.priceAdjustment.toFixed(2)}
              </StyledText>
            )}
          </StyledTouchableOpacity>
        ))}
      </StyledView>
    );
  };
  
  // Render dropdown (for long lists)
  const renderDropdown = () => {
    const selectedOption = attribute.options.find(option => isSelected(option.id));
    
    return (
      <StyledTouchableOpacity
        onPress={() => {
          // In a real implementation, this would open a modal or dropdown
          // For simplicity, we'll just cycle through options
          const currentIndex = attribute.options.findIndex(option => isSelected(option.id));
          const nextIndex = (currentIndex + 1) % attribute.options.length;
          onValueChange(attribute.id, attribute.options[nextIndex].id);
        }}
        disabled={disabled}
        className={`flex-row items-center justify-between p-3 border rounded-lg mb-4 ${
          disabled ? 'opacity-50 border-neutral-300 dark:border-neutral-700' : 'border-neutral-300 dark:border-neutral-700'
        }`}
      >
        <StyledText className="text-neutral-700 dark:text-neutral-300">
          {selectedOption ? selectedOption.name : t('select')}
        </StyledText>
        <Icon name="chevron-down" size={20} color="#6b7280" />
      </StyledTouchableOpacity>
    );
  };
  
  return (
    <AnimatedView animation="fadeInUp" duration={300}>
      <StyledText className="text-neutral-700 dark:text-neutral-300 font-medium mb-2">
        {attribute.name}
      </StyledText>
      
      {attribute.displayType === 'color' && renderColorSwatch()}
      {attribute.displayType === 'size' && renderSizeGrid()}
      {attribute.displayType === 'radio' && renderRadioButtons()}
      {attribute.displayType === 'select' && attribute.options.length > 5 
        ? renderDropdown() 
        : attribute.displayType === 'select' && renderRadioButtons()}
      {attribute.displayType === 'multi' && renderCheckboxes()}
    </AnimatedView>
  );
};

export default AttributeSelector;
