import React from 'react';
import { View, Text, Image } from 'react-native';
import { styled } from 'nativewind';
import Button from './Button';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledImage = styled(Image);

const EmptyState = ({
  title,
  message,
  buttonTitle,
  onButtonPress,
  image,
  imageSource,
}) => {
  return (
    <StyledView className="flex-1 items-center justify-center p-6">
      {image && (
        <StyledImage
          source={image}
          className="w-48 h-48 mb-6"
          resizeMode="contain"
        />
      )}
      
      {imageSource && (
        <StyledImage
          source={{ uri: imageSource }}
          className="w-48 h-48 mb-6"
          resizeMode="contain"
        />
      )}
      
      <StyledText className="text-neutral-900 dark:text-white text-xl font-bold mb-2 text-center">
        {title}
      </StyledText>
      
      <StyledText className="text-neutral-600 dark:text-neutral-400 text-base mb-6 text-center">
        {message}
      </StyledText>
      
      {buttonTitle && onButtonPress && (
        <Button
          title={buttonTitle}
          onPress={onButtonPress}
          variant="primary"
          size="medium"
        />
      )}
    </StyledView>
  );
};

export default EmptyState;
