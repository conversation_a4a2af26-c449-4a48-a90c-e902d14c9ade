import React, { useState, useEffect, useRef } from 'react';
import { View, TextInput, TouchableOpacity, FlatList, Text, Keyboard } from 'react-native';
import { styled } from 'nativewind';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';
import { useDispatch, useSelector } from 'react-redux';
import { debounce } from 'lodash';

import { searchProducts, clearSearchResults, addToSearchHistory } from '../store/slices/searchSlice';

const StyledView = styled(View);
const StyledTextInput = styled(TextInput);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledText = styled(Text);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const SearchBar = ({ onSearch, onFocus, onBlur, autoFocus = false }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const inputRef = useRef(null);
  
  const { searchResults, searchHistory, loading } = useSelector((state) => state.search);
  const { theme } = useSelector((state) => state.ui);
  
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(autoFocus);
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  // Create debounced search function
  const debouncedSearch = useRef(
    debounce((searchQuery) => {
      if (searchQuery.trim().length > 2) {
        dispatch(searchProducts(searchQuery));
      } else {
        dispatch(clearSearchResults());
      }
    }, 300)
  ).current;
  
  // Handle query change
  useEffect(() => {
    debouncedSearch(query);
    
    // Show suggestions if query is not empty and input is focused
    setShowSuggestions(query.trim().length > 0 && isFocused);
    
    return () => {
      debouncedSearch.cancel();
    };
  }, [query, isFocused]);
  
  // Handle search submission
  const handleSearch = () => {
    if (query.trim()) {
      Keyboard.dismiss();
      setShowSuggestions(false);
      dispatch(addToSearchHistory(query));
      onSearch(query);
    }
  };
  
  // Handle suggestion selection
  const handleSelectSuggestion = (suggestion) => {
    setQuery(suggestion);
    Keyboard.dismiss();
    setShowSuggestions(false);
    dispatch(addToSearchHistory(suggestion));
    onSearch(suggestion);
  };
  
  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    if (onFocus) onFocus();
  };
  
  // Handle blur
  const handleBlur = () => {
    // Delay hiding suggestions to allow for selection
    setTimeout(() => {
      setIsFocused(false);
      if (onBlur) onBlur();
    }, 200);
  };
  
  // Clear search
  const clearSearch = () => {
    setQuery('');
    dispatch(clearSearchResults());
    inputRef.current?.focus();
  };
  
  // Render suggestion item
  const renderSuggestionItem = ({ item }) => {
    return (
      <StyledTouchableOpacity
        onPress={() => handleSelectSuggestion(item.name || item)}
        className="flex-row items-center px-4 py-3 border-b border-neutral-200 dark:border-neutral-700"
      >
        <Icon 
          name={item.type === 'history' ? 'history' : 'magnify'} 
          size={20} 
          color="#6b7280" 
        />
        <StyledText className="ml-2 text-neutral-800 dark:text-neutral-200">
          {item.name || item}
        </StyledText>
      </StyledTouchableOpacity>
    );
  };
  
  // Prepare suggestions list
  const getSuggestions = () => {
    const suggestions = [];
    
    // Add search results
    if (searchResults.length > 0) {
      searchResults.forEach(result => {
        suggestions.push({
          id: `result-${result.id}`,
          name: result.name,
          type: 'result'
        });
      });
    }
    
    // Add search history if query is short
    if (query.length < 3 && searchHistory.length > 0) {
      const filteredHistory = searchHistory
        .filter(item => item.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 5);
      
      filteredHistory.forEach(item => {
        suggestions.push({
          id: `history-${item}`,
          name: item,
          type: 'history'
        });
      });
    }
    
    return suggestions;
  };
  
  return (
    <StyledView className="z-10">
      <StyledView className="flex-row items-center bg-neutral-100 dark:bg-neutral-800 rounded-full px-4 py-2">
        <Icon name="magnify" size={20} color="#6b7280" />
        
        <StyledTextInput
          ref={inputRef}
          className="flex-1 ml-2 text-neutral-900 dark:text-white"
          placeholder={t('searchProducts')}
          placeholderTextColor="#9ca3af"
          value={query}
          onChangeText={setQuery}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
          autoCapitalize="none"
          autoCorrect={false}
          autoFocus={autoFocus}
        />
        
        {query.length > 0 && (
          <StyledTouchableOpacity onPress={clearSearch}>
            <Icon name="close-circle" size={20} color="#6b7280" />
          </StyledTouchableOpacity>
        )}
      </StyledView>
      
      {/* Suggestions dropdown */}
      {showSuggestions && (
        <AnimatedView
          animation="fadeIn"
          duration={200}
          className="absolute top-12 left-0 right-0 bg-white dark:bg-neutral-800 rounded-lg shadow-md z-20 max-h-80"
          style={{ elevation: 5 }}
        >
          {loading ? (
            <StyledView className="p-4 items-center">
              <StyledText className="text-neutral-500 dark:text-neutral-400">
                {t('searching')}...
              </StyledText>
            </StyledView>
          ) : (
            <FlatList
              data={getSuggestions()}
              renderItem={renderSuggestionItem}
              keyExtractor={(item) => item.id || item}
              ListEmptyComponent={
                <StyledView className="p-4 items-center">
                  <StyledText className="text-neutral-500 dark:text-neutral-400">
                    {query.length < 3 
                      ? t('typeMoreToSearch') 
                      : t('noResultsFound')}
                  </StyledText>
                </StyledView>
              }
            />
          )}
        </AnimatedView>
      )}
    </StyledView>
  );
};

export default SearchBar;
