import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import FastImage from 'react-native-fast-image';

import { addItemToCart } from '../store/slices/cartSlice';
import { ROUTES } from '../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledImage = styled(FastImage);

const ProductCard = ({ product, horizontal = false }) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const handleAddToCart = () => {
    dispatch(addItemToCart({ productId: product.id, quantity: 1 }));
  };

  const handlePress = () => {
    navigation.navigate(ROUTES.PRODUCT_DETAILS, { productId: product.id });
  };

  // Convert base64 image to URI
  const imageUri = product.image_1920
    ? `data:image/jpeg;base64,${product.image_1920}`
    : 'https://via.placeholder.com/150';

  return (
    <StyledTouchableOpacity
      onPress={handlePress}
      testID="product-card"
      className={`bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 ${
        horizontal ? 'flex-row w-full' : 'w-[48%] mx-1 mb-3'
      }`}
    >
      <StyledImage
        source={{
          uri: imageUri,
          priority: FastImage.priority.normal,
          cache: FastImage.cacheControl.immutable
        }}
        className={`${horizontal ? 'w-1/3 h-full' : 'w-full h-32'}`}
        resizeMode={FastImage.resizeMode.cover}
        testID="product-image"
      />

      <StyledView className={`p-3 ${horizontal ? 'w-2/3' : 'w-full'}`}>
        <StyledText
          numberOfLines={2}
          className="text-neutral-900 dark:text-white font-medium text-sm mb-1"
        >
          {product.name}
        </StyledText>

        <StyledText className="text-primary-600 dark:text-primary-400 font-bold text-base mb-2">
          ₹{product.list_price.toFixed(2)}
        </StyledText>

        <StyledView className="flex-row justify-between items-center">
          <StyledTouchableOpacity
            onPress={handleAddToCart}
            className="bg-primary-600 rounded-full p-1.5"
            testID="add-to-cart-button"
          >
            <Icon name="cart-plus" size={16} color="#ffffff" />
          </StyledTouchableOpacity>

          <StyledText className="text-neutral-500 dark:text-neutral-400 text-xs">
            {t('addToCart')}
          </StyledText>
        </StyledView>
      </StyledView>
    </StyledTouchableOpacity>
  );
};

export default ProductCard;
