import React from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { styled } from 'nativewind';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

const StyledView = styled(View);
const StyledActivityIndicator = styled(ActivityIndicator);
const StyledText = styled(Text);

const LoadingIndicator = ({ 
  fullScreen = false, 
  message = '', 
  size = 'large',
  color = null,
}) => {
  const { t } = useTranslation();
  const { theme } = useSelector((state) => state.ui);
  
  const indicatorColor = color || (theme === 'dark' ? '#ffffff' : '#0284c7');
  
  if (fullScreen) {
    return (
      <StyledView className="flex-1 items-center justify-center bg-white dark:bg-neutral-900">
        <StyledActivityIndicator size={size} color={indicatorColor} />
        <StyledText className="text-neutral-600 dark:text-neutral-300 mt-4 text-base">
          {message || t('loading')}
        </StyledText>
      </StyledView>
    );
  }
  
  return (
    <StyledView className="py-4 items-center justify-center">
      <StyledActivityIndicator size={size} color={indicatorColor} />
      {message && (
        <StyledText className="text-neutral-600 dark:text-neutral-300 mt-2 text-sm">
          {message}
        </StyledText>
      )}
    </StyledView>
  );
};

export default LoadingIndicator;
