import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal, TextInput } from 'react-native';
import { styled } from 'nativewind';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);
const StyledTextInput = styled(TextInput);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const ProductFilters = ({
  filters,
  selectedFilters,
  onFilterChange,
  categories,
  selectedCategory,
  onCategoryChange,
  productCount,
  onReset,
  onApply,
}) => {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [activeFilterGroup, setActiveFilterGroup] = useState(null);
  const [tempFilters, setTempFilters] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showCategoryTree, setShowCategoryTree] = useState(false);

  // Initialize temp filters when selected filters change
  useEffect(() => {
    setTempFilters(selectedFilters);
  }, [selectedFilters]);

  // Get active filter count
  const getActiveFilterCount = () => {
    return Object.values(selectedFilters).reduce((count, values) => {
      return count + (Array.isArray(values) ? values.length : (values ? 1 : 0));
    }, 0);
  };

  // Handle filter selection
  const handleFilterSelect = (group, value, isMulti = false) => {
    setTempFilters(prev => {
      const newFilters = { ...prev };
      
      if (isMulti) {
        // For multi-select filters (checkboxes)
        if (!newFilters[group]) {
          newFilters[group] = [value];
        } else if (newFilters[group].includes(value)) {
          newFilters[group] = newFilters[group].filter(v => v !== value);
          if (newFilters[group].length === 0) {
            delete newFilters[group];
          }
        } else {
          newFilters[group] = [...newFilters[group], value];
        }
      } else {
        // For single-select filters (radio buttons)
        if (newFilters[group] === value) {
          delete newFilters[group];
        } else {
          newFilters[group] = value;
        }
      }
      
      return newFilters;
    });
  };

  // Apply filters
  const applyFilters = () => {
    onFilterChange(tempFilters);
    setModalVisible(false);
  };

  // Reset filters
  const resetFilters = () => {
    setTempFilters({});
    setModalVisible(false);
    onReset();
  };

  // Render filter chips
  const renderFilterChips = () => {
    const chips = [];
    
    Object.entries(selectedFilters).forEach(([group, values]) => {
      if (Array.isArray(values)) {
        values.forEach(value => {
          const filterGroup = filters.find(f => f.id === group);
          const filterOption = filterGroup?.options.find(o => o.id === value);
          
          if (filterGroup && filterOption) {
            chips.push({
              id: `${group}-${value}`,
              label: `${filterGroup.name}: ${filterOption.name}`,
              onRemove: () => {
                const newFilters = { ...selectedFilters };
                newFilters[group] = newFilters[group].filter(v => v !== value);
                if (newFilters[group].length === 0) {
                  delete newFilters[group];
                }
                onFilterChange(newFilters);
              }
            });
          }
        });
      } else {
        const filterGroup = filters.find(f => f.id === group);
        const filterOption = filterGroup?.options.find(o => o.id === values);
        
        if (filterGroup && filterOption) {
          chips.push({
            id: `${group}-${values}`,
            label: `${filterGroup.name}: ${filterOption.name}`,
            onRemove: () => {
              const newFilters = { ...selectedFilters };
              delete newFilters[group];
              onFilterChange(newFilters);
            }
          });
        }
      }
    });

    // Add category chip if selected
    if (selectedCategory) {
      const category = categories.find(c => c.id === selectedCategory);
      if (category) {
        chips.push({
          id: `category-${selectedCategory}`,
          label: `${t('category')}: ${category.name}`,
          onRemove: () => onCategoryChange(null)
        });
      }
    }
    
    return chips;
  };

  // Filter options based on search query
  const getFilteredOptions = (options) => {
    if (!searchQuery) return options;
    return options.filter(option => 
      option.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  // Get breadcrumb path for a category
  const getCategoryBreadcrumb = (categoryId) => {
    const breadcrumb = [];
    let currentCategory = categories.find(c => c.id === categoryId);
    
    while (currentCategory) {
      breadcrumb.unshift(currentCategory);
      currentCategory = currentCategory.parent_id 
        ? categories.find(c => c.id === currentCategory.parent_id[0]) 
        : null;
    }
    
    return breadcrumb;
  };

  // Get child categories
  const getChildCategories = (parentId = null) => {
    return categories.filter(c => 
      parentId 
        ? c.parent_id && c.parent_id[0] === parentId
        : !c.parent_id || !c.parent_id[0]
    );
  };

  return (
    <>
      {/* Filter bar */}
      <StyledView className="flex-row items-center justify-between px-4 py-2 bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700">
        <StyledView className="flex-row items-center">
          <StyledTouchableOpacity 
            onPress={() => setModalVisible(true)}
            className="flex-row items-center mr-2 px-3 py-1 bg-neutral-100 dark:bg-neutral-700 rounded-full"
          >
            <Icon name="filter-variant" size={18} color="#6b7280" />
            <StyledText className="ml-1 text-neutral-700 dark:text-neutral-300">
              {t('filter')}
              {getActiveFilterCount() > 0 && ` (${getActiveFilterCount()})`}
            </StyledText>
          </StyledTouchableOpacity>
          
          {selectedCategory && (
            <StyledTouchableOpacity 
              onPress={() => setShowCategoryTree(true)}
              className="flex-row items-center px-3 py-1 bg-neutral-100 dark:bg-neutral-700 rounded-full"
            >
              <Icon name="folder-outline" size={18} color="#6b7280" />
              <StyledText className="ml-1 text-neutral-700 dark:text-neutral-300">
                {categories.find(c => c.id === selectedCategory)?.name || t('categories')}
              </StyledText>
            </StyledTouchableOpacity>
          )}
        </StyledView>
        
        <StyledText className="text-neutral-500 dark:text-neutral-400 text-sm">
          {productCount} {t('products')}
        </StyledText>
      </StyledView>
      
      {/* Active filter chips */}
      {getActiveFilterCount() > 0 && (
        <StyledScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          className="px-4 py-2 bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700"
        >
          {renderFilterChips().map(chip => (
            <AnimatedView
              key={chip.id}
              animation="fadeIn"
              duration={300}
              className="flex-row items-center bg-primary-50 dark:bg-primary-900 px-2 py-1 rounded-full mr-2"
            >
              <StyledText className="text-primary-700 dark:text-primary-300 text-sm mr-1">
                {chip.label}
              </StyledText>
              <StyledTouchableOpacity onPress={chip.onRemove}>
                <Icon name="close-circle" size={16} color="#4f46e5" />
              </StyledTouchableOpacity>
            </AnimatedView>
          ))}
          
          {getActiveFilterCount() > 1 && (
            <StyledTouchableOpacity 
              onPress={resetFilters}
              className="flex-row items-center bg-neutral-100 dark:bg-neutral-700 px-3 py-1 rounded-full"
            >
              <StyledText className="text-neutral-700 dark:text-neutral-300 text-sm">
                {t('clearAll')}
              </StyledText>
            </StyledTouchableOpacity>
          )}
        </StyledScrollView>
      )}
      
      {/* Filter modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <StyledView className="flex-1 bg-white dark:bg-neutral-900">
          {/* Modal header */}
          <StyledView className="flex-row items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
            <StyledText className="text-neutral-900 dark:text-white text-lg font-bold">
              {t('filters')}
            </StyledText>
            
            <StyledTouchableOpacity onPress={() => setModalVisible(false)}>
              <Icon name="close" size={24} color="#6b7280" />
            </StyledTouchableOpacity>
          </StyledView>
          
          {/* Filter content */}
          {activeFilterGroup ? (
            // Filter options view
            <StyledView className="flex-1">
              <StyledView className="flex-row items-center p-4 border-b border-neutral-200 dark:border-neutral-700">
                <StyledTouchableOpacity onPress={() => setActiveFilterGroup(null)}>
                  <Icon name="arrow-left" size={24} color="#6b7280" />
                </StyledTouchableOpacity>
                
                <StyledText className="ml-2 text-neutral-900 dark:text-white text-lg">
                  {filters.find(f => f.id === activeFilterGroup)?.name}
                </StyledText>
              </StyledView>
              
              <StyledTextInput
                className="mx-4 mt-4 p-2 bg-neutral-100 dark:bg-neutral-800 rounded-lg text-neutral-900 dark:text-white"
                placeholder={t('search')}
                placeholderTextColor="#9ca3af"
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              
              <StyledScrollView className="flex-1 p-4">
                {getFilteredOptions(filters.find(f => f.id === activeFilterGroup)?.options || []).map(option => {
                  const isMulti = filters.find(f => f.id === activeFilterGroup)?.type === 'multi';
                  const isSelected = isMulti
                    ? tempFilters[activeFilterGroup]?.includes(option.id)
                    : tempFilters[activeFilterGroup] === option.id;
                  
                  return (
                    <StyledTouchableOpacity
                      key={option.id}
                      onPress={() => handleFilterSelect(activeFilterGroup, option.id, isMulti)}
                      className="flex-row items-center justify-between py-3 border-b border-neutral-200 dark:border-neutral-700"
                    >
                      <StyledView className="flex-row items-center flex-1">
                        <StyledText className="text-neutral-900 dark:text-white flex-1">
                          {option.name}
                        </StyledText>
                        
                        {option.count > 0 && (
                          <StyledText className="text-neutral-500 dark:text-neutral-400 text-sm ml-2">
                            ({option.count})
                          </StyledText>
                        )}
                      </StyledView>
                      
                      {isMulti ? (
                        <Icon
                          name={isSelected ? "checkbox-marked" : "checkbox-blank-outline"}
                          size={24}
                          color={isSelected ? "#4f46e5" : "#6b7280"}
                        />
                      ) : (
                        <Icon
                          name={isSelected ? "radiobox-marked" : "radiobox-blank"}
                          size={24}
                          color={isSelected ? "#4f46e5" : "#6b7280"}
                        />
                      )}
                    </StyledTouchableOpacity>
                  );
                })}
              </StyledScrollView>
            </StyledView>
          ) : (
            // Filter groups view
            <StyledScrollView className="flex-1 p-4">
              {filters.map(filter => (
                <StyledTouchableOpacity
                  key={filter.id}
                  onPress={() => {
                    setActiveFilterGroup(filter.id);
                    setSearchQuery('');
                  }}
                  className="flex-row items-center justify-between py-3 border-b border-neutral-200 dark:border-neutral-700"
                >
                  <StyledView className="flex-row items-center flex-1">
                    <StyledText className="text-neutral-900 dark:text-white flex-1">
                      {filter.name}
                    </StyledText>
                    
                    {tempFilters[filter.id] && (
                      <StyledView className="bg-primary-100 dark:bg-primary-900 px-2 py-0.5 rounded-full">
                        <StyledText className="text-primary-700 dark:text-primary-300 text-xs">
                          {Array.isArray(tempFilters[filter.id])
                            ? tempFilters[filter.id].length
                            : 1}
                        </StyledText>
                      </StyledView>
                    )}
                  </StyledView>
                  
                  <Icon name="chevron-right" size={24} color="#6b7280" />
                </StyledTouchableOpacity>
              ))}
            </StyledScrollView>
          )}
          
          {/* Modal footer */}
          <StyledView className="flex-row p-4 border-t border-neutral-200 dark:border-neutral-700">
            <StyledTouchableOpacity
              onPress={resetFilters}
              className="flex-1 py-3 mr-2 bg-neutral-100 dark:bg-neutral-700 rounded-lg"
            >
              <StyledText className="text-center text-neutral-700 dark:text-neutral-300">
                {t('reset')}
              </StyledText>
            </StyledTouchableOpacity>
            
            <StyledTouchableOpacity
              onPress={applyFilters}
              className="flex-1 py-3 ml-2 bg-primary-600 rounded-lg"
            >
              <StyledText className="text-center text-white">
                {t('apply')}
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </Modal>
      
      {/* Category tree modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showCategoryTree}
        onRequestClose={() => setShowCategoryTree(false)}
      >
        <StyledView className="flex-1 bg-white dark:bg-neutral-900">
          {/* Modal header */}
          <StyledView className="flex-row items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
            <StyledText className="text-neutral-900 dark:text-white text-lg font-bold">
              {t('categories')}
            </StyledText>
            
            <StyledTouchableOpacity onPress={() => setShowCategoryTree(false)}>
              <Icon name="close" size={24} color="#6b7280" />
            </StyledTouchableOpacity>
          </StyledView>
          
          {/* Breadcrumb */}
          {selectedCategory && (
            <StyledScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              className="px-4 py-2 border-b border-neutral-200 dark:border-neutral-700"
            >
              <StyledTouchableOpacity
                onPress={() => onCategoryChange(null)}
                className="flex-row items-center mr-1"
              >
                <Icon name="home" size={16} color="#6b7280" />
                <StyledText className="ml-1 text-neutral-700 dark:text-neutral-300">
                  {t('home')}
                </StyledText>
              </StyledTouchableOpacity>
              
              {getCategoryBreadcrumb(selectedCategory).map((category, index, array) => (
                <StyledView key={category.id} className="flex-row items-center">
                  <Icon name="chevron-right" size={16} color="#6b7280" />
                  <StyledTouchableOpacity
                    onPress={() => onCategoryChange(category.id)}
                    className="ml-1"
                  >
                    <StyledText 
                      className={`${
                        index === array.length - 1
                          ? 'text-primary-600 dark:text-primary-400 font-medium'
                          : 'text-neutral-700 dark:text-neutral-300'
                      }`}
                    >
                      {category.name}
                    </StyledText>
                  </StyledTouchableOpacity>
                </StyledView>
              ))}
            </StyledScrollView>
          )}
          
          {/* Category list */}
          <StyledScrollView className="flex-1 p-4">
            {getChildCategories(selectedCategory).map(category => {
              const hasChildren = categories.some(c => c.parent_id && c.parent_id[0] === category.id);
              const productCount = category.product_count || 0;
              
              return (
                <StyledTouchableOpacity
                  key={category.id}
                  onPress={() => {
                    onCategoryChange(category.id);
                    setShowCategoryTree(false);
                  }}
                  className="flex-row items-center justify-between py-3 border-b border-neutral-200 dark:border-neutral-700"
                >
                  <StyledView className="flex-row items-center flex-1">
                    <Icon 
                      name={hasChildren ? "folder-outline" : "file-outline"} 
                      size={20} 
                      color="#6b7280" 
                    />
                    <StyledText className="ml-2 text-neutral-900 dark:text-white flex-1">
                      {category.name}
                    </StyledText>
                    
                    {productCount > 0 && (
                      <StyledText className="text-neutral-500 dark:text-neutral-400 text-sm">
                        ({productCount})
                      </StyledText>
                    )}
                  </StyledView>
                  
                  {hasChildren && (
                    <Icon name="chevron-right" size={24} color="#6b7280" />
                  )}
                </StyledTouchableOpacity>
              );
            })}
          </StyledScrollView>
        </StyledView>
      </Modal>
    </>
  );
};

export default ProductFilters;
