import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { styled } from 'nativewind';

const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledText = styled(Text);
const StyledActivityIndicator = styled(ActivityIndicator);

const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  className = '',
  textClassName = '',
  ...props
}) => {
  // Button variants
  const variantStyles = {
    primary: 'bg-primary-600 border-primary-600',
    secondary: 'bg-secondary-600 border-secondary-600',
    outline: 'bg-transparent border-primary-600',
    ghost: 'bg-transparent border-transparent',
    danger: 'bg-error-default border-error-default',
    success: 'bg-success-default border-success-default',
  };

  // Button sizes
  const sizeStyles = {
    small: 'py-1 px-3',
    medium: 'py-2 px-4',
    large: 'py-3 px-6',
  };

  // Text colors
  const textColors = {
    primary: 'text-white',
    secondary: 'text-white',
    outline: 'text-primary-600',
    ghost: 'text-primary-600',
    danger: 'text-white',
    success: 'text-white',
  };

  // Text sizes
  const textSizes = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg',
  };

  // Disabled styles
  const disabledStyles = disabled
    ? 'opacity-50'
    : '';

  // Full width styles
  const widthStyles = fullWidth
    ? 'w-full'
    : '';

  return (
    <StyledTouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      className={`rounded-lg border ${sizeStyles[size]} ${variantStyles[variant]} ${disabledStyles} ${widthStyles} items-center justify-center flex-row ${className}`}
      {...props}
    >
      {loading ? (
        <StyledActivityIndicator
          testID="loading-indicator"
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? '#0284c7' : '#ffffff'}
          className="mr-2"
        />
      ) : null}
      <StyledText
        className={`font-medium ${textColors[variant]} ${textSizes[size]} ${textClassName}`}
      >
        {title}
      </StyledText>
    </StyledTouchableOpacity>
  );
};

export default Button;
