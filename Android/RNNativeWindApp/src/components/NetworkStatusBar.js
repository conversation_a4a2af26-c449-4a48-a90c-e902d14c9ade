import React, { useEffect, useRef } from 'react';
import { View, Text, Animated } from 'react-native';
import { styled } from 'nativewind';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { useNetworkStatus } from '../utils/networkMonitor';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledAnimatedView = styled(Animated.View);

const NetworkStatusBar = () => {
  const { isConnected, isInternetReachable } = useNetworkStatus();
  const { t } = useTranslation();
  
  const translateY = useRef(new Animated.Value(-50)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    if (!isConnected || !isInternetReachable) {
      // Show the status bar
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Hide the status bar
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: -50,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isConnected, isInternetReachable]);
  
  if (isConnected && isInternetReachable) {
    return null;
  }
  
  return (
    <StyledAnimatedView
      className="absolute top-0 left-0 right-0 bg-error-default z-50 px-4 py-2"
      style={{
        transform: [{ translateY }],
        opacity,
      }}
    >
      <StyledView className="flex-row items-center justify-center">
        <Icon name="wifi-off" size={16} color="#ffffff" />
        <StyledText className="text-white font-medium ml-2">
          {!isConnected ? t('networkOffline') : t('noInternetAccess')}
        </StyledText>
      </StyledView>
    </StyledAnimatedView>
  );
};

export default NetworkStatusBar;
