import React from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';

const StyledView = styled(View);
const StyledText = styled(Text);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));
const AnimatedText = Animatable.createAnimatableComponent(styled(Text));

const PriceDisplay = ({ 
  basePrice, 
  finalPrice, 
  discount = 0, 
  tax = 0, 
  quantity = 1,
  attributeAdjustments = [],
  currency = '₹',
  animate = true
}) => {
  const { t } = useTranslation();
  
  // Calculate total attribute adjustments
  const totalAdjustments = attributeAdjustments.reduce((sum, adj) => sum + adj.amount, 0);
  
  // Check if there's a discount
  const hasDiscount = discount > 0 || finalPrice < basePrice;
  
  // Calculate discount percentage if not provided
  const discountPercentage = discount > 0 
    ? discount 
    : hasDiscount 
      ? Math.round(((basePrice - finalPrice) / basePrice) * 100) 
      : 0;
  
  // Calculate total price with quantity
  const totalPrice = finalPrice * quantity;
  
  // Wrapper component based on animation setting
  const Wrapper = animate ? AnimatedView : StyledView;
  const TextWrapper = animate ? AnimatedText : StyledText;
  
  return (
    <Wrapper className="mb-4">
      {/* Main price display */}
      <StyledView className="flex-row items-baseline">
        <TextWrapper 
          className="text-primary-600 dark:text-primary-400 text-xl font-bold"
          animation={animate ? "fadeIn" : undefined}
          duration={500}
        >
          {currency}{finalPrice.toFixed(2)}
        </TextWrapper>
        
        {hasDiscount && (
          <TextWrapper 
            className="text-neutral-500 dark:text-neutral-400 text-sm line-through ml-2"
            animation={animate ? "fadeIn" : undefined}
            duration={500}
            delay={100}
          >
            {currency}{basePrice.toFixed(2)}
          </TextWrapper>
        )}
        
        {hasDiscount && (
          <TextWrapper 
            className="text-green-600 dark:text-green-400 text-sm ml-2"
            animation={animate ? "fadeIn" : undefined}
            duration={500}
            delay={200}
          >
            {discountPercentage}% {t('off')}
          </TextWrapper>
        )}
      </StyledView>
      
      {/* Quantity and total */}
      {quantity > 1 && (
        <TextWrapper 
          className="text-neutral-600 dark:text-neutral-400 text-sm mt-1"
          animation={animate ? "fadeIn" : undefined}
          duration={500}
          delay={300}
        >
          {quantity} {t('items')}: {currency}{totalPrice.toFixed(2)}
        </TextWrapper>
      )}
      
      {/* Attribute adjustments */}
      {attributeAdjustments.length > 0 && (
        <StyledView className="mt-2">
          <TextWrapper 
            className="text-neutral-700 dark:text-neutral-300 text-sm font-medium"
            animation={animate ? "fadeIn" : undefined}
            duration={500}
            delay={400}
          >
            {t('priceDetails')}:
          </TextWrapper>
          
          <TextWrapper 
            className="text-neutral-600 dark:text-neutral-400 text-sm"
            animation={animate ? "fadeIn" : undefined}
            duration={500}
            delay={500}
          >
            {t('basePrice')}: {currency}{basePrice.toFixed(2)}
          </TextWrapper>
          
          {attributeAdjustments.map((adjustment, index) => (
            <TextWrapper 
              key={`adj-${index}`}
              className="text-neutral-600 dark:text-neutral-400 text-sm"
              animation={animate ? "fadeIn" : undefined}
              duration={500}
              delay={600 + (index * 100)}
            >
              {adjustment.name}: {adjustment.amount > 0 ? '+' : ''}{currency}{adjustment.amount.toFixed(2)}
            </TextWrapper>
          ))}
          
          {totalAdjustments !== 0 && (
            <TextWrapper 
              className="text-neutral-600 dark:text-neutral-400 text-sm"
              animation={animate ? "fadeIn" : undefined}
              duration={500}
              delay={600 + (attributeAdjustments.length * 100)}
            >
              {t('totalAdjustments')}: {totalAdjustments > 0 ? '+' : ''}{currency}{totalAdjustments.toFixed(2)}
            </TextWrapper>
          )}
        </StyledView>
      )}
      
      {/* Tax information */}
      {tax > 0 && (
        <TextWrapper 
          className="text-neutral-600 dark:text-neutral-400 text-sm mt-1"
          animation={animate ? "fadeIn" : undefined}
          duration={500}
          delay={700 + (attributeAdjustments.length * 100)}
        >
          {t('includingTax')}: {currency}{tax.toFixed(2)}
        </TextWrapper>
      )}
    </Wrapper>
  );
};

export default PriceDisplay;
