import React from 'react';
import { View, TextInput, Text } from 'react-native';
import { styled } from 'nativewind';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const StyledView = styled(View);
const StyledTextInput = styled(TextInput);
const StyledText = styled(Text);

const Input = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'none',
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  className = '',
  inputClassName = '',
  labelClassName = '',
  errorClassName = '',
  ...props
}) => {
  return (
    <StyledView className={`mb-4 ${className}`}>
      {label && (
        <StyledText className={`text-neutral-700 dark:text-neutral-300 mb-1 font-medium ${labelClassName}`}>
          {label}
        </StyledText>
      )}

      <StyledView className="relative">
        {leftIcon && (
          <StyledView className="absolute left-3 top-0 bottom-0 justify-center z-10" testID="left-icon-container">
            <Icon name={leftIcon} size={20} color="#6b7280" testID="left-icon" />
          </StyledView>
        )}

        <StyledTextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          className={`border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg px-4 py-2.5 text-neutral-900 dark:text-white ${
            leftIcon ? 'pl-10' : ''
          } ${rightIcon ? 'pr-10' : ''} ${
            error ? 'border-error-default' : ''
          } ${inputClassName}`}
          placeholderTextColor="#9ca3af"
          {...props}
        />

        {rightIcon && (
          <StyledView
            className="absolute right-3 top-0 bottom-0 justify-center z-10"
            onTouchEnd={onRightIconPress}
            testID="right-icon-container"
          >
            <Icon name={rightIcon} size={20} color="#6b7280" testID="right-icon" />
          </StyledView>
        )}
      </StyledView>

      {error && (
        <StyledText className={`text-error-default text-sm mt-1 ${errorClassName}`}>
          {error}
        </StyledText>
      )}
    </StyledView>
  );
};

export default Input;
