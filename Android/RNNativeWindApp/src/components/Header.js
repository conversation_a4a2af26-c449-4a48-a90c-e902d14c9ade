import React from 'react';
import { View, Text, TouchableOpacity, StatusBar } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

const Header = ({
  title,
  showBack = true,
  showCart = true,
  showSearch = false,
  onSearchPress,
  rightComponent,
  transparent = false,
}) => {
  const navigation = useNavigation();
  const { count } = useSelector((state) => state.cart);
  const { theme } = useSelector((state) => state.ui);
  
  const isDarkMode = theme === 'dark';

  return (
    <>
      <StatusBar
        barStyle={isDarkMode || transparent ? 'light-content' : 'dark-content'}
        backgroundColor={
          transparent
            ? 'transparent'
            : isDarkMode
            ? '#1f2937'
            : '#ffffff'
        }
        translucent={transparent}
      />
      
      <StyledView
        className={`px-4 flex-row items-center justify-between ${
          transparent
            ? 'bg-transparent'
            : 'bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700'
        } ${transparent ? 'pt-10' : 'pt-2'} pb-2 h-16`}
      >
        <StyledView className="flex-row items-center">
          {showBack && (
            <StyledTouchableOpacity
              onPress={() => navigation.goBack()}
              className="mr-3"
            >
              <Icon
                name="arrow-left"
                size={24}
                color={
                  transparent
                    ? '#ffffff'
                    : isDarkMode
                    ? '#ffffff'
                    : '#1f2937'
                }
              />
            </StyledTouchableOpacity>
          )}
          
          <StyledText
            className={`font-bold text-lg ${
              transparent
                ? 'text-white'
                : 'text-neutral-900 dark:text-white'
            }`}
          >
            {title}
          </StyledText>
        </StyledView>
        
        <StyledView className="flex-row items-center">
          {showSearch && (
            <StyledTouchableOpacity
              onPress={onSearchPress}
              className="mr-4"
            >
              <Icon
                name="magnify"
                size={24}
                color={
                  transparent
                    ? '#ffffff'
                    : isDarkMode
                    ? '#ffffff'
                    : '#1f2937'
                }
              />
            </StyledTouchableOpacity>
          )}
          
          {showCart && (
            <StyledTouchableOpacity
              onPress={() => navigation.navigate('CartTab')}
              className="relative"
            >
              <Icon
                name="cart-outline"
                size={24}
                color={
                  transparent
                    ? '#ffffff'
                    : isDarkMode
                    ? '#ffffff'
                    : '#1f2937'
                }
              />
              
              {count > 0 && (
                <StyledView className="absolute -top-2 -right-2 bg-primary-600 rounded-full w-5 h-5 items-center justify-center">
                  <StyledText className="text-white text-xs font-bold">
                    {count}
                  </StyledText>
                </StyledView>
              )}
            </StyledTouchableOpacity>
          )}
          
          {rightComponent && rightComponent}
        </StyledView>
      </StyledView>
    </>
  );
};

export default Header;
