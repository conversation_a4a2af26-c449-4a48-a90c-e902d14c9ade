import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

import Input from '../../components/Input';
import Button from '../../components/Button';
import Header from '../../components/Header';
import { showToast } from '../../store/slices/uiSlice';
import { ROUTES, REGEX, ERROR_MESSAGES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledKeyboardAvoidingView = styled(KeyboardAvoidingView);

const RegisterScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  
  const handleChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value,
    });
    
    // Clear error when user types
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: null,
      });
    }
  };
  
  const validate = () => {
    const newErrors = {};
    
    if (!formData.name) {
      newErrors.name = t('requiredField');
    }
    
    if (!formData.email) {
      newErrors.email = t('requiredField');
    } else if (!REGEX.EMAIL.test(formData.email)) {
      newErrors.email = t('invalidEmail');
    }
    
    if (!formData.phone) {
      newErrors.phone = t('requiredField');
    } else if (!REGEX.PHONE.test(formData.phone)) {
      newErrors.phone = t('invalidPhone');
    }
    
    if (!formData.password) {
      newErrors.password = t('requiredField');
    } else if (formData.password.length < 8) {
      newErrors.password = t('minimumPasswordLength');
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t('requiredField');
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('passwordMismatch');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleRegister = () => {
    if (validate()) {
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        dispatch(showToast({
          message: t('registerSuccess'),
          type: 'success',
        }));
        navigation.navigate(ROUTES.LOGIN);
      }, 1500);
    }
  };
  
  return (
    <StyledKeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white dark:bg-neutral-900"
    >
      <Header title={t('register')} />
      
      <StyledScrollView
        contentContainerClassName="flex-grow"
        keyboardShouldPersistTaps="handled"
      >
        <StyledView className="flex-1 p-6">
          <Input
            label={t('fullName')}
            value={formData.name}
            onChangeText={(value) => handleChange('name', value)}
            placeholder={t('fullName')}
            autoCapitalize="words"
            leftIcon="account"
            error={errors.name}
          />
          
          <Input
            label={t('email')}
            value={formData.email}
            onChangeText={(value) => handleChange('email', value)}
            placeholder={t('email')}
            keyboardType="email-address"
            autoCapitalize="none"
            leftIcon="email"
            error={errors.email}
          />
          
          <Input
            label={t('phone')}
            value={formData.phone}
            onChangeText={(value) => handleChange('phone', value)}
            placeholder={t('phone')}
            keyboardType="phone-pad"
            leftIcon="phone"
            error={errors.phone}
          />
          
          <Input
            label={t('password')}
            value={formData.password}
            onChangeText={(value) => handleChange('password', value)}
            placeholder={t('password')}
            secureTextEntry
            leftIcon="lock"
            error={errors.password}
          />
          
          <Input
            label={t('confirmPassword')}
            value={formData.confirmPassword}
            onChangeText={(value) => handleChange('confirmPassword', value)}
            placeholder={t('confirmPassword')}
            secureTextEntry
            leftIcon="lock-check"
            error={errors.confirmPassword}
          />
          
          <Button
            title={t('register')}
            onPress={handleRegister}
            loading={loading}
            fullWidth
            className="mt-4"
          />
          
          <StyledView className="flex-row justify-center mt-6">
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {t('alreadyHaveAccount')}
            </StyledText>
            <StyledTouchableOpacity
              onPress={() => navigation.navigate(ROUTES.LOGIN)}
              className="ml-1"
            >
              <StyledText className="text-primary-600 dark:text-primary-400 font-medium">
                {t('login')}
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledKeyboardAvoidingView>
  );
};

export default RegisterScreen;
