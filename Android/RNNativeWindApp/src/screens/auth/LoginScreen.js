import React, { useState, useEffect } from 'react';
import { View, Text, Image, ScrollView, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

import Input from '../../components/Input';
import Button from '../../components/Button';
import { login, clearError } from '../../store/slices/authSlice';
import { showToast } from '../../store/slices/uiSlice';
import { ROUTES, ERROR_MESSAGES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledImage = styled(Image);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledKeyboardAvoidingView = styled(KeyboardAvoidingView);

const LoginScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { loading, error } = useSelector((state) => state.auth);
  
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  
  useEffect(() => {
    if (error) {
      dispatch(showToast({
        message: error,
        type: 'error',
      }));
      dispatch(clearError());
    }
  }, [error, dispatch]);
  
  const validate = () => {
    const newErrors = {};
    
    if (!username) {
      newErrors.username = t('requiredField');
    }
    
    if (!password) {
      newErrors.password = t('requiredField');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleLogin = () => {
    if (validate()) {
      dispatch(login({ username, password }));
    }
  };
  
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };
  
  return (
    <StyledKeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white dark:bg-neutral-900"
    >
      <StyledScrollView
        contentContainerClassName="flex-grow"
        keyboardShouldPersistTaps="handled"
      >
        <StyledView className="flex-1 p-6">
          <StyledView className="items-center my-10">
            <StyledImage
              source={{ uri: 'https://via.placeholder.com/100' }}
              className="w-20 h-20"
              resizeMode="contain"
            />
            <StyledText className="text-2xl font-bold text-neutral-900 dark:text-white mt-4">
              {t('appName')}
            </StyledText>
          </StyledView>
          
          <StyledText className="text-2xl font-bold text-neutral-900 dark:text-white mb-6">
            {t('login')}
          </StyledText>
          
          <Input
            label={t('username')}
            value={username}
            onChangeText={setUsername}
            placeholder={t('username')}
            autoCapitalize="none"
            leftIcon="account"
            error={errors.username}
          />
          
          <Input
            label={t('password')}
            value={password}
            onChangeText={setPassword}
            placeholder={t('password')}
            secureTextEntry={!showPassword}
            leftIcon="lock"
            rightIcon={showPassword ? 'eye-off' : 'eye'}
            onRightIconPress={toggleShowPassword}
            error={errors.password}
          />
          
          <StyledTouchableOpacity
            onPress={() => navigation.navigate(ROUTES.FORGOT_PASSWORD)}
            className="self-end mb-6"
          >
            <StyledText className="text-primary-600 dark:text-primary-400">
              {t('forgotPassword')}
            </StyledText>
          </StyledTouchableOpacity>
          
          <Button
            title={t('login')}
            onPress={handleLogin}
            loading={loading}
            fullWidth
          />
          
          <StyledView className="flex-row justify-center mt-6">
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {t('dontHaveAccount')}
            </StyledText>
            <StyledTouchableOpacity
              onPress={() => navigation.navigate(ROUTES.REGISTER)}
              className="ml-1"
            >
              <StyledText className="text-primary-600 dark:text-primary-400 font-medium">
                {t('register')}
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledKeyboardAvoidingView>
  );
};

export default LoginScreen;
