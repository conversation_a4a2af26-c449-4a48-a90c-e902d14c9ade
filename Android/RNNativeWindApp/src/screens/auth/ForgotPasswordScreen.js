import React, { useState } from 'react';
import { View, Text, KeyboardAvoidingView, Platform } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

import Input from '../../components/Input';
import Button from '../../components/Button';
import Header from '../../components/Header';
import { showToast } from '../../store/slices/uiSlice';
import { ROUTES, REGEX } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledKeyboardAvoidingView = styled(KeyboardAvoidingView);

const ForgotPasswordScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const validate = () => {
    if (!email) {
      setError(t('requiredField'));
      return false;
    }
    
    if (!REGEX.EMAIL.test(email)) {
      setError(t('invalidEmail'));
      return false;
    }
    
    setError('');
    return true;
  };
  
  const handleResetPassword = () => {
    if (validate()) {
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        dispatch(showToast({
          message: t('passwordReset'),
          type: 'success',
        }));
        navigation.navigate(ROUTES.LOGIN);
      }, 1500);
    }
  };
  
  return (
    <StyledKeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white dark:bg-neutral-900"
    >
      <Header title={t('forgotPassword')} />
      
      <StyledView className="flex-1 p-6">
        <StyledText className="text-neutral-700 dark:text-neutral-300 mb-6">
          {t('forgotPasswordDescription')}
        </StyledText>
        
        <Input
          label={t('email')}
          value={email}
          onChangeText={(value) => {
            setEmail(value);
            setError('');
          }}
          placeholder={t('email')}
          keyboardType="email-address"
          autoCapitalize="none"
          leftIcon="email"
          error={error}
        />
        
        <Button
          title={t('resetPassword')}
          onPress={handleResetPassword}
          loading={loading}
          fullWidth
          className="mt-4"
        />
      </StyledView>
    </StyledKeyboardAvoidingView>
  );
};

export default ForgotPasswordScreen;
