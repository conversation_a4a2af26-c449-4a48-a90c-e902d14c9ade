import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, Image, RefreshControl } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import LoadingIndicator from '../../components/LoadingIndicator';
import EmptyState from '../../components/EmptyState';
import { fetchCategories } from '../../store/slices/productsSlice';
import { ROUTES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledImage = styled(Image);
const StyledFlatList = styled(FlatList);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const CategoriesScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { categories, loading } = useSelector((state) => state.products);
  const { theme } = useSelector((state) => state.ui);
  
  const [refreshing, setRefreshing] = useState(false);
  
  useEffect(() => {
    loadData();
  }, []);
  
  const loadData = () => {
    dispatch(fetchCategories());
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
    setRefreshing(false);
  };
  
  const navigateToCategory = (categoryId, categoryName) => {
    navigation.navigate(ROUTES.PRODUCTS, { categoryId, categoryName });
  };
  
  const renderCategoryItem = ({ item, index }) => {
    // Convert base64 image to URI
    const imageUri = item.image_1920
      ? `data:image/jpeg;base64,${item.image_1920}`
      : 'https://via.placeholder.com/100';
    
    return (
      <AnimatedView
        animation="fadeInUp"
        delay={index * 100}
        duration={500}
      >
        <StyledTouchableOpacity
          onPress={() => navigateToCategory(item.id, item.name)}
          className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4 flex-row"
        >
          <StyledImage
            source={{ uri: imageUri }}
            className="w-20 h-20"
            resizeMode="cover"
          />
          
          <StyledView className="flex-1 p-3 justify-center">
            <StyledText className="text-neutral-900 dark:text-white font-medium text-base">
              {item.name}
            </StyledText>
            
            {item.parent_id && (
              <StyledText className="text-neutral-500 dark:text-neutral-400 text-sm">
                {categories.find(cat => cat.id === item.parent_id[0])?.name || ''}
              </StyledText>
            )}
          </StyledView>
        </StyledTouchableOpacity>
      </AnimatedView>
    );
  };
  
  if (loading && !refreshing && !categories.length) {
    return <LoadingIndicator fullScreen />;
  }
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header
        title={t('categories')}
        showSearch
        onSearchPress={() => navigation.navigate(ROUTES.SEARCH)}
      />
      
      <StyledFlatList
        data={categories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerClassName="p-4"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme === 'dark' ? '#ffffff' : '#0284c7']}
            tintColor={theme === 'dark' ? '#ffffff' : '#0284c7'}
          />
        }
        ListEmptyComponent={
          <EmptyState
            title={t('noCategories')}
            message={t('noCategoriesDescription')}
            buttonTitle={t('refresh')}
            onButtonPress={loadData}
          />
        }
      />
    </StyledView>
  );
};

export default CategoriesScreen;
