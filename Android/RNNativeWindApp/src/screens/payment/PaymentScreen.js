import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Linking } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import RazorpayCheckout from 'react-native-razorpay';
import UPIPayment from 'react-native-upi-payment';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Button from '../../components/Button';
import LoadingIndicator from '../../components/LoadingIndicator';
import { showToast } from '../../store/slices/uiSlice';
import { clearCart } from '../../store/slices/cartSlice';
import { ROUTES, PAYMENT_METHODS } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledImage = styled(Image);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const PaymentScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { shippingInfo, billingInfo, total } = route.params || {};
  const { user } = useSelector((state) => state.auth);
  
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(PAYMENT_METHODS.RAZORPAY);
  const [loading, setLoading] = useState(false);
  
  const paymentMethods = [
    {
      id: PAYMENT_METHODS.RAZORPAY,
      name: t('razorpay'),
      icon: 'credit-card',
      description: t('razorpayDescription'),
    },
    {
      id: PAYMENT_METHODS.UPI,
      name: t('upi'),
      icon: 'cellphone',
      description: t('upiDescription'),
    },
  ];
  
  const handlePaymentMethodSelect = (methodId) => {
    setSelectedPaymentMethod(methodId);
  };
  
  const handleRazorpayPayment = () => {
    setLoading(true);
    
    const options = {
      description: 'Payment for your order',
      image: 'https://via.placeholder.com/100',
      currency: 'INR',
      key: 'rzp_test_YOUR_KEY_HERE', // Replace with your Razorpay key
      amount: total * 100, // Razorpay expects amount in paise
      name: 'Siddhi eCommerce',
      prefill: {
        email: billingInfo.email,
        contact: billingInfo.phone,
        name: `${billingInfo.firstName} ${billingInfo.lastName}`,
      },
      theme: { color: '#0284c7' },
    };
    
    RazorpayCheckout.open(options)
      .then((data) => {
        // Handle success
        setLoading(false);
        dispatch(showToast({
          message: t('paymentSuccessful'),
          type: 'success',
        }));
        dispatch(clearCart());
        navigation.navigate(ROUTES.PAYMENT_SUCCESS, {
          paymentId: data.razorpay_payment_id,
          orderId: Date.now().toString(),
        });
      })
      .catch((error) => {
        // Handle failure
        setLoading(false);
        dispatch(showToast({
          message: error.description || t('paymentFailed'),
          type: 'error',
        }));
      });
  };
  
  const handleUPIPayment = () => {
    setLoading(true);
    
    // Generate a unique transaction reference ID
    const transactionRef = `TXN${Date.now()}`;
    
    UPIPayment.initializePayment({
      vpa: 'your-merchant-vpa@upi', // Replace with your UPI ID
      payeeName: 'Siddhi eCommerce',
      transactionRef,
      transactionNote: 'Payment for your order',
      amount: total.toString(),
      currency: 'INR',
    })
      .then((response) => {
        setLoading(false);
        
        if (response.Status === 'SUCCESS') {
          dispatch(showToast({
            message: t('paymentSuccessful'),
            type: 'success',
          }));
          dispatch(clearCart());
          navigation.navigate(ROUTES.PAYMENT_SUCCESS, {
            paymentId: response.txnId,
            orderId: Date.now().toString(),
          });
        } else {
          dispatch(showToast({
            message: response.Status === 'FAILED' ? t('paymentFailed') : t('paymentCancelled'),
            type: 'error',
          }));
        }
      })
      .catch((error) => {
        setLoading(false);
        dispatch(showToast({
          message: error.message || t('paymentFailed'),
          type: 'error',
        }));
      });
  };
  
  const handlePayment = () => {
    if (selectedPaymentMethod === PAYMENT_METHODS.RAZORPAY) {
      handleRazorpayPayment();
    } else if (selectedPaymentMethod === PAYMENT_METHODS.UPI) {
      handleUPIPayment();
    }
  };
  
  // For demo purposes, let's simulate a successful payment
  const handleDemoPayment = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      dispatch(showToast({
        message: t('paymentSuccessful'),
        type: 'success',
      }));
      dispatch(clearCart());
      navigation.navigate(ROUTES.PAYMENT_SUCCESS, {
        paymentId: `PAY${Date.now()}`,
        orderId: `ORD${Date.now()}`,
      });
    }, 2000);
  };
  
  if (loading) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('payment')} />
        <LoadingIndicator fullScreen message={t('processingPayment')} />
      </StyledView>
    );
  }
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('payment')} />
      
      <StyledScrollView className="flex-1 p-4">
        <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-4">
          {t('selectPaymentMethod')}
        </StyledText>
        
        {paymentMethods.map((method) => (
          <AnimatedView
            key={method.id}
            animation="fadeInUp"
            duration={300}
            delay={paymentMethods.indexOf(method) * 100}
          >
            <StyledTouchableOpacity
              onPress={() => handlePaymentMethodSelect(method.id)}
              className={`border rounded-lg p-4 mb-4 flex-row items-center ${
                selectedPaymentMethod === method.id
                  ? 'border-primary-600 bg-primary-50 dark:bg-primary-900'
                  : 'border-neutral-300 dark:border-neutral-700'
              }`}
            >
              <StyledView 
                className={`w-10 h-10 rounded-full items-center justify-center ${
                  selectedPaymentMethod === method.id
                    ? 'bg-primary-600'
                    : 'bg-neutral-200 dark:bg-neutral-700'
                }`}
              >
                <Icon 
                  name={method.icon} 
                  size={20} 
                  color={selectedPaymentMethod === method.id ? '#ffffff' : '#6b7280'} 
                />
              </StyledView>
              
              <StyledView className="ml-3 flex-1">
                <StyledText 
                  className={`font-medium ${
                    selectedPaymentMethod === method.id
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-neutral-900 dark:text-white'
                  }`}
                >
                  {method.name}
                </StyledText>
                
                <StyledText className="text-neutral-600 dark:text-neutral-400 text-sm">
                  {method.description}
                </StyledText>
              </StyledView>
              
              <Icon 
                name={selectedPaymentMethod === method.id ? 'radiobox-marked' : 'radiobox-blank'} 
                size={24} 
                color={selectedPaymentMethod === method.id ? '#0284c7' : '#6b7280'} 
              />
            </StyledTouchableOpacity>
          </AnimatedView>
        ))}
        
        <StyledView className="bg-neutral-100 dark:bg-neutral-800 rounded-lg p-4 mb-4">
          <StyledText className="text-neutral-900 dark:text-white font-medium mb-2">
            {t('orderSummary')}
          </StyledText>
          
          <StyledView className="flex-row justify-between mb-2">
            <StyledText className="text-neutral-700 dark:text-neutral-300">
              {t('subtotal')}
            </StyledText>
            <StyledText className="text-neutral-900 dark:text-white">
              ₹{total.toFixed(2)}
            </StyledText>
          </StyledView>
          
          <StyledView className="flex-row justify-between mb-2">
            <StyledText className="text-neutral-700 dark:text-neutral-300">
              {t('shipping')}
            </StyledText>
            <StyledText className="text-neutral-900 dark:text-white">
              ₹0.00
            </StyledText>
          </StyledView>
          
          <StyledView className="flex-row justify-between mb-2">
            <StyledText className="text-neutral-700 dark:text-neutral-300">
              {t('tax')}
            </StyledText>
            <StyledText className="text-neutral-900 dark:text-white">
              ₹0.00
            </StyledText>
          </StyledView>
          
          <StyledView className="border-t border-neutral-300 dark:border-neutral-600 mt-2 pt-2">
            <StyledView className="flex-row justify-between">
              <StyledText className="text-neutral-900 dark:text-white font-bold">
                {t('total')}
              </StyledText>
              <StyledText className="text-primary-600 dark:text-primary-400 font-bold">
                ₹{total.toFixed(2)}
              </StyledText>
            </StyledView>
          </StyledView>
        </StyledView>
        
        <StyledText className="text-neutral-500 dark:text-neutral-400 text-xs text-center mb-4">
          {t('paymentDisclaimer')}
        </StyledText>
      </StyledScrollView>
      
      <StyledView className="p-4 border-t border-neutral-200 dark:border-neutral-700">
        <Button
          title={t('payNow')}
          onPress={handleDemoPayment} // Use handlePayment for real implementation
          fullWidth
        />
      </StyledView>
    </StyledView>
  );
};

export default PaymentScreen;
