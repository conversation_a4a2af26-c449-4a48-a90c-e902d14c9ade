import React, { useEffect } from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import Button from '../../components/Button';
import { ROUTES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const PaymentSuccessScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { t } = useTranslation();

  const { paymentId, orderId } = route.params || {};

  useEffect(() => {
    // Reset navigation stack when going back
    navigation.addListener('beforeRemove', (e) => {
      // Prevent default behavior of going back
      if (e.data.action.type === 'GO_BACK') {
        e.preventDefault();

        // Navigate to home screen
        navigation.reset({
          index: 0,
          routes: [{ name: 'HomeTab' }],
        });
      }
    });

    return () => {
      navigation.removeListener('beforeRemove', () => {});
    };
  }, [navigation]);

  const handleViewOrder = () => {
    navigation.navigate('OrdersTab', {
      screen: ROUTES.ORDER_DETAILS,
      params: { orderId },
    });
  };

  const handleContinueShopping = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'HomeTab' }],
    });
  };

  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900 items-center justify-center p-6">
      <AnimatedView
        animation="zoomIn"
        duration={500}
        className="items-center"
      >
        <StyledView className="bg-success-default rounded-full p-6 mb-6">
          <Icon
            name="check"
            size={60}
            color="#ffffff"
          />
        </StyledView>
      </AnimatedView>

      <AnimatedView
        animation="fadeInUp"
        duration={500}
        delay={300}
        className="items-center"
      >
        <StyledText className="text-2xl font-bold text-neutral-900 dark:text-white mb-2 text-center">
          {t('paymentSuccessful')}
        </StyledText>

        <StyledText className="text-neutral-600 dark:text-neutral-400 text-center mb-6">
          {t('orderPlacedSuccessfully')}
        </StyledText>

        <StyledView className="bg-neutral-100 dark:bg-neutral-800 rounded-lg p-4 w-full mb-6">
          <StyledView className="flex-row justify-between mb-2">
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {t('orderId')}
            </StyledText>
            <StyledText className="text-neutral-900 dark:text-white font-medium">
              {orderId}
            </StyledText>
          </StyledView>

          <StyledView className="flex-row justify-between">
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {t('paymentId')}
            </StyledText>
            <StyledText className="text-neutral-900 dark:text-white font-medium">
              {paymentId}
            </StyledText>
          </StyledView>
        </StyledView>

        <Button
          title={t('viewOrder')}
          onPress={handleViewOrder}
          fullWidth
          className="mb-3"
        />

        <Button
          title={t('continueShopping')}
          onPress={handleContinueShopping}
          variant="outline"
          fullWidth
        />
      </AnimatedView>
    </StyledView>
  );
};

export default PaymentSuccessScreen;
