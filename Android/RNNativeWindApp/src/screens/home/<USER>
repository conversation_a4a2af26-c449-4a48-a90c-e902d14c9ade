import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, FlatList, TouchableOpacity, Image, RefreshControl } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import { useScreenTracking, useNavigationTracking, trackAction } from '../../hooks/useAnalytics';

import Header from '../../components/Header';
import ProductCard from '../../components/ProductCard';
import LoadingIndicator from '../../components/LoadingIndicator';
import { fetchCategories, fetchProducts } from '../../store/slices/productsSlice';
import { ROUTES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledImage = styled(Image);
const StyledFlatList = styled(FlatList);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const HomeScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  // Track screen view
  useScreenTracking('Home');
  useNavigationTracking();

  const { categories, products, loading } = useSelector((state) => state.products);
  const { theme } = useSelector((state) => state.ui);

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    dispatch(fetchCategories());
    dispatch(fetchProducts({ page: 0, limit: 10 }));
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
    setRefreshing(false);
  };

  const navigateToCategory = (categoryId) => {
    trackAction('select_category', { category_id: categoryId });
    navigation.navigate(ROUTES.PRODUCTS, { categoryId });
  };

  const navigateToProductDetails = (productId) => {
    trackAction('select_product', { product_id: productId });
    navigation.navigate(ROUTES.PRODUCT_DETAILS, { productId });
  };

  const renderCategoryItem = ({ item, index }) => {
    // Convert base64 image to URI
    const imageUri = item.image_1920
      ? `data:image/jpeg;base64,${item.image_1920}`
      : 'https://via.placeholder.com/100';

    return (
      <AnimatedView
        animation="fadeInUp"
        delay={index * 100}
        duration={500}
      >
        <StyledTouchableOpacity
          onPress={() => navigateToCategory(item.id)}
          className="items-center mr-4"
        >
          <StyledView className="w-16 h-16 rounded-full overflow-hidden bg-neutral-200 dark:bg-neutral-700 mb-2">
            <StyledImage
              source={{ uri: imageUri }}
              className="w-full h-full"
              resizeMode="cover"
            />
          </StyledView>
          <StyledText
            numberOfLines={1}
            className="text-neutral-800 dark:text-neutral-200 text-xs text-center w-20"
          >
            {item.name}
          </StyledText>
        </StyledTouchableOpacity>
      </AnimatedView>
    );
  };

  const renderFeaturedItem = ({ item, index }) => {
    return (
      <AnimatedView
        animation="fadeInRight"
        delay={index * 100}
        duration={500}
      >
        <ProductCard product={item} horizontal />
      </AnimatedView>
    );
  };

  const renderNewArrivalItem = ({ item, index }) => {
    return (
      <AnimatedView
        animation="fadeInUp"
        delay={index * 100}
        duration={500}
      >
        <ProductCard product={item} />
      </AnimatedView>
    );
  };

  if (loading && !refreshing && !products.length) {
    return <LoadingIndicator fullScreen />;
  }

  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header
        title={t('appName')}
        showBack={false}
        showSearch
        onSearchPress={() => navigation.navigate(ROUTES.SEARCH)}
      />

      <StyledScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme === 'dark' ? '#ffffff' : '#0284c7']}
            tintColor={theme === 'dark' ? '#ffffff' : '#0284c7'}
          />
        }
      >
        {/* Banner */}
        <StyledView className="p-4">
          <StyledView className="bg-primary-600 rounded-xl p-4 flex-row items-center">
            <StyledView className="flex-1">
              <StyledText className="text-white text-lg font-bold mb-1">
                {t('specialOffer')}
              </StyledText>
              <StyledText className="text-white text-sm mb-3">
                {t('getDiscount')}
              </StyledText>
              <StyledTouchableOpacity className="bg-white rounded-full py-1 px-3 self-start">
                <StyledText className="text-primary-600 font-medium">
                  {t('shopNow')}
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
            <StyledImage
              source={{ uri: 'https://via.placeholder.com/100' }}
              className="w-20 h-20"
              resizeMode="contain"
            />
          </StyledView>
        </StyledView>

        {/* Categories */}
        <StyledView className="mb-6">
          <StyledView className="flex-row justify-between items-center px-4 mb-3">
            <StyledText className="text-neutral-900 dark:text-white font-bold text-lg">
              {t('categories')}
            </StyledText>
            <StyledTouchableOpacity
              onPress={() => navigation.navigate(ROUTES.CATEGORIES)}
            >
              <StyledText className="text-primary-600 dark:text-primary-400">
                {t('viewAll')}
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>

          <StyledFlatList
            data={categories.slice(0, 10)}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerClassName="px-4"
            ListEmptyComponent={
              <StyledView className="items-center justify-center w-screen py-4">
                <StyledText className="text-neutral-500 dark:text-neutral-400">
                  {t('noCategories')}
                </StyledText>
              </StyledView>
            }
          />
        </StyledView>

        {/* Featured Products */}
        <StyledView className="mb-6">
          <StyledView className="flex-row justify-between items-center px-4 mb-3">
            <StyledText className="text-neutral-900 dark:text-white font-bold text-lg">
              {t('featuredProducts')}
            </StyledText>
            <StyledTouchableOpacity
              onPress={() => navigation.navigate(ROUTES.PRODUCTS)}
            >
              <StyledText className="text-primary-600 dark:text-primary-400">
                {t('viewAll')}
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>

          <StyledFlatList
            data={products.slice(0, 5)}
            renderItem={renderFeaturedItem}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerClassName="px-4"
            ItemSeparatorComponent={() => <StyledView className="w-3" />}
            ListEmptyComponent={
              <StyledView className="items-center justify-center w-screen py-4">
                <StyledText className="text-neutral-500 dark:text-neutral-400">
                  {t('noProducts')}
                </StyledText>
              </StyledView>
            }
          />
        </StyledView>

        {/* New Arrivals */}
        <StyledView className="mb-6">
          <StyledView className="flex-row justify-between items-center px-4 mb-3">
            <StyledText className="text-neutral-900 dark:text-white font-bold text-lg">
              {t('newArrivals')}
            </StyledText>
            <StyledTouchableOpacity
              onPress={() => navigation.navigate(ROUTES.PRODUCTS)}
            >
              <StyledText className="text-primary-600 dark:text-primary-400">
                {t('viewAll')}
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>

          <StyledView className="px-4 flex-row flex-wrap justify-between">
            {products.slice(0, 4).map((item, index) => (
              <AnimatedView
                key={item.id.toString()}
                animation="fadeInUp"
                delay={index * 100}
                duration={500}
                className="w-[48%] mb-3"
              >
                <ProductCard product={item} />
              </AnimatedView>
            ))}

            {products.length === 0 && (
              <StyledView className="items-center justify-center w-full py-4">
                <StyledText className="text-neutral-500 dark:text-neutral-400">
                  {t('noProducts')}
                </StyledText>
              </StyledView>
            )}
          </StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledView>
  );
};

export default HomeScreen;
