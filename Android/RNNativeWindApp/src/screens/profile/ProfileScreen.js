import React from 'react';
import { View, Text, ScrollView, Image, TouchableOpacity } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Button from '../../components/Button';
import { logout } from '../../store/slices/authSlice';
import { showToast } from '../../store/slices/uiSlice';
import { ROUTES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledImage = styled(Image);
const StyledTouchableOpacity = styled(TouchableOpacity);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const ProfileScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  
  const menuItems = [
    {
      id: 'edit_profile',
      title: t('editProfile'),
      icon: 'account-edit',
      route: ROUTES.EDIT_PROFILE,
      requiresAuth: true,
    },
    {
      id: 'addresses',
      title: t('addresses'),
      icon: 'map-marker',
      route: ROUTES.ADDRESSES,
      requiresAuth: true,
    },
    {
      id: 'orders',
      title: t('orders'),
      icon: 'clipboard-list',
      route: ROUTES.ORDERS,
      requiresAuth: true,
    },
    {
      id: 'settings',
      title: t('settings'),
      icon: 'cog',
      route: ROUTES.SETTINGS,
      requiresAuth: false,
    },
    {
      id: 'help',
      title: t('help'),
      icon: 'help-circle',
      route: null,
      onPress: () => {
        dispatch(showToast({
          message: t('helpNotAvailable'),
          type: 'info',
        }));
      },
      requiresAuth: false,
    },
    {
      id: 'about',
      title: t('aboutUs'),
      icon: 'information',
      route: null,
      onPress: () => {
        dispatch(showToast({
          message: t('aboutNotAvailable'),
          type: 'info',
        }));
      },
      requiresAuth: false,
    },
  ];
  
  const handleLogout = () => {
    dispatch(logout());
    dispatch(showToast({
      message: t('logoutSuccess'),
      type: 'success',
    }));
    navigation.reset({
      index: 0,
      routes: [{ name: 'Auth', screen: ROUTES.LOGIN }],
    });
  };
  
  const handleMenuItemPress = (item) => {
    if (item.requiresAuth && !isAuthenticated) {
      navigation.navigate('Auth', { screen: ROUTES.LOGIN });
      return;
    }
    
    if (item.route) {
      navigation.navigate(item.route);
    } else if (item.onPress) {
      item.onPress();
    }
  };
  
  const renderMenuItem = (item, index) => {
    return (
      <AnimatedView
        key={item.id}
        animation="fadeInRight"
        delay={index * 100}
        duration={300}
      >
        <StyledTouchableOpacity
          onPress={() => handleMenuItemPress(item)}
          className="flex-row items-center py-3 px-4 border-b border-neutral-200 dark:border-neutral-700"
        >
          <StyledView className="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900 items-center justify-center">
            <Icon name={item.icon} size={18} color="#0284c7" />
          </StyledView>
          
          <StyledText className="flex-1 text-neutral-900 dark:text-white ml-3">
            {item.title}
          </StyledText>
          
          <Icon name="chevron-right" size={20} color="#9ca3af" />
        </StyledTouchableOpacity>
      </AnimatedView>
    );
  };
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('profile')} showBack={false} />
      
      <StyledScrollView>
        <AnimatedView animation="fadeIn" duration={500}>
          <StyledView className="items-center justify-center py-6 bg-primary-600">
            <StyledView className="w-20 h-20 rounded-full bg-white items-center justify-center mb-3">
              {user?.image ? (
                <StyledImage
                  source={{ uri: `data:image/jpeg;base64,${user.image}` }}
                  className="w-20 h-20 rounded-full"
                  resizeMode="cover"
                />
              ) : (
                <Icon name="account" size={40} color="#0284c7" />
              )}
            </StyledView>
            
            {isAuthenticated ? (
              <StyledView className="items-center">
                <StyledText className="text-white text-lg font-bold mb-1">
                  {user?.name || t('user')}
                </StyledText>
                <StyledText className="text-white text-sm">
                  {user?.email || ''}
                </StyledText>
              </StyledView>
            ) : (
              <Button
                title={t('login')}
                onPress={() => navigation.navigate('Auth', { screen: ROUTES.LOGIN })}
                variant="outline"
                className="border-white"
                textClassName="text-white"
              />
            )}
          </StyledView>
        </AnimatedView>
        
        <StyledView className="mt-4">
          {menuItems
            .filter(item => !item.requiresAuth || isAuthenticated)
            .map(renderMenuItem)}
        </StyledView>
        
        {isAuthenticated && (
          <AnimatedView animation="fadeIn" duration={500} delay={600}>
            <StyledView className="p-4 mt-4">
              <Button
                title={t('logout')}
                onPress={handleLogout}
                variant="outline"
                fullWidth
              />
            </StyledView>
          </AnimatedView>
        )}
        
        <StyledView className="items-center justify-center py-6">
          <StyledText className="text-neutral-500 dark:text-neutral-400 text-sm">
            {t('appVersion')} 1.0.0
          </StyledText>
        </StyledView>
      </StyledScrollView>
    </StyledView>
  );
};

export default ProfileScreen;
