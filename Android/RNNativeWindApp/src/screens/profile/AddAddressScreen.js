import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Switch, KeyboardAvoidingView, Platform } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Input from '../../components/Input';
import Button from '../../components/Button';
import { showToast } from '../../store/slices/uiSlice';
import { REGEX } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledSwitch = styled(Switch);
const StyledKeyboardAvoidingView = styled(KeyboardAvoidingView);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const AddAddressScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { address } = route.params || {};
  const isEditing = !!address;
  
  const [formData, setFormData] = useState({
    name: '',
    fullName: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'India',
    isDefault: false,
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    if (isEditing) {
      setFormData(address);
    }
  }, [isEditing, address]);
  
  const handleChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value,
    });
    
    // Clear error when user types
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: null,
      });
    }
  };
  
  const validate = () => {
    const newErrors = {};
    
    if (!formData.name) {
      newErrors.name = t('requiredField');
    }
    
    if (!formData.fullName) {
      newErrors.fullName = t('requiredField');
    }
    
    if (!formData.phone) {
      newErrors.phone = t('requiredField');
    } else if (!REGEX.PHONE.test(formData.phone)) {
      newErrors.phone = t('invalidPhone');
    }
    
    if (!formData.address) {
      newErrors.address = t('requiredField');
    }
    
    if (!formData.city) {
      newErrors.city = t('requiredField');
    }
    
    if (!formData.state) {
      newErrors.state = t('requiredField');
    }
    
    if (!formData.zipCode) {
      newErrors.zipCode = t('requiredField');
    } else if (!REGEX.PIN_CODE.test(formData.zipCode)) {
      newErrors.zipCode = t('invalidZipCode');
    }
    
    if (!formData.country) {
      newErrors.country = t('requiredField');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSave = () => {
    if (validate()) {
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        dispatch(showToast({
          message: isEditing ? t('addressUpdated') : t('addressAdded'),
          type: 'success',
        }));
        navigation.goBack();
      }, 1000);
    }
  };
  
  return (
    <StyledKeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white dark:bg-neutral-900"
    >
      <Header title={isEditing ? t('editAddress') : t('addAddress')} />
      
      <StyledScrollView
        contentContainerClassName="flex-grow"
        keyboardShouldPersistTaps="handled"
      >
        <StyledView className="p-4">
          <AnimatedView animation="fadeInUp" duration={300} delay={100}>
            <Input
              label={t('addressName')}
              value={formData.name}
              onChangeText={(value) => handleChange('name', value)}
              placeholder={t('addressNamePlaceholder')}
              error={errors.name}
            />
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={150}>
            <Input
              label={t('fullName')}
              value={formData.fullName}
              onChangeText={(value) => handleChange('fullName', value)}
              placeholder={t('fullName')}
              error={errors.fullName}
            />
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={200}>
            <Input
              label={t('phone')}
              value={formData.phone}
              onChangeText={(value) => handleChange('phone', value)}
              placeholder={t('phone')}
              keyboardType="phone-pad"
              error={errors.phone}
            />
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={250}>
            <Input
              label={t('address')}
              value={formData.address}
              onChangeText={(value) => handleChange('address', value)}
              placeholder={t('address')}
              error={errors.address}
            />
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={300}>
            <StyledView className="flex-row">
              <Input
                label={t('city')}
                value={formData.city}
                onChangeText={(value) => handleChange('city', value)}
                placeholder={t('city')}
                error={errors.city}
                className="flex-1 mr-2"
              />
              
              <Input
                label={t('state')}
                value={formData.state}
                onChangeText={(value) => handleChange('state', value)}
                placeholder={t('state')}
                error={errors.state}
                className="flex-1 ml-2"
              />
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={350}>
            <StyledView className="flex-row">
              <Input
                label={t('zipCode')}
                value={formData.zipCode}
                onChangeText={(value) => handleChange('zipCode', value)}
                placeholder={t('zipCode')}
                keyboardType="numeric"
                error={errors.zipCode}
                className="flex-1 mr-2"
              />
              
              <Input
                label={t('country')}
                value={formData.country}
                onChangeText={(value) => handleChange('country', value)}
                placeholder={t('country')}
                error={errors.country}
                className="flex-1 ml-2"
              />
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={400}>
            <StyledView className="flex-row items-center mt-4 mb-6">
              <StyledSwitch
                value={formData.isDefault}
                onValueChange={(value) => handleChange('isDefault', value)}
                trackColor={{ false: '#767577', true: '#0284c7' }}
                thumbColor={formData.isDefault ? '#ffffff' : '#f4f3f4'}
              />
              <StyledText className="text-neutral-700 dark:text-neutral-300 ml-2">
                {t('setAsDefaultAddress')}
              </StyledText>
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={450}>
            <Button
              title={isEditing ? t('updateAddress') : t('saveAddress')}
              onPress={handleSave}
              loading={loading}
              fullWidth
            />
          </AnimatedView>
        </StyledView>
      </StyledScrollView>
    </StyledKeyboardAvoidingView>
  );
};

export default AddAddressScreen;
