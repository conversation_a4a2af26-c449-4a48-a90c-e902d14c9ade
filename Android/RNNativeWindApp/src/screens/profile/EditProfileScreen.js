import React, { useState } from 'react';
import { View, Text, ScrollView, Image, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Input from '../../components/Input';
import Button from '../../components/Button';
import { showToast } from '../../store/slices/uiSlice';
import { REGEX } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledImage = styled(Image);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledKeyboardAvoidingView = styled(KeyboardAvoidingView);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const EditProfileScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { user } = useSelector((state) => state.auth);
  
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  
  const handleChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value,
    });
    
    // Clear error when user types
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: null,
      });
    }
  };
  
  const validate = () => {
    const newErrors = {};
    
    if (!formData.name) {
      newErrors.name = t('requiredField');
    }
    
    if (!formData.email) {
      newErrors.email = t('requiredField');
    } else if (!REGEX.EMAIL.test(formData.email)) {
      newErrors.email = t('invalidEmail');
    }
    
    if (!formData.phone) {
      newErrors.phone = t('requiredField');
    } else if (!REGEX.PHONE.test(formData.phone)) {
      newErrors.phone = t('invalidPhone');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSave = () => {
    if (validate()) {
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        dispatch(showToast({
          message: t('profileUpdated'),
          type: 'success',
        }));
        navigation.goBack();
      }, 1000);
    }
  };
  
  const handleUploadImage = () => {
    dispatch(showToast({
      message: t('imageUploadNotAvailable'),
      type: 'info',
    }));
  };
  
  return (
    <StyledKeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white dark:bg-neutral-900"
    >
      <Header title={t('editProfile')} />
      
      <StyledScrollView
        contentContainerClassName="flex-grow"
        keyboardShouldPersistTaps="handled"
      >
        <StyledView className="p-4">
          <AnimatedView animation="fadeIn" duration={300}>
            <StyledView className="items-center mb-6">
              <StyledView className="relative">
                <StyledView className="w-24 h-24 rounded-full bg-neutral-200 dark:bg-neutral-700 items-center justify-center overflow-hidden">
                  {user?.image ? (
                    <StyledImage
                      source={{ uri: `data:image/jpeg;base64,${user.image}` }}
                      className="w-24 h-24"
                      resizeMode="cover"
                    />
                  ) : (
                    <Icon name="account" size={60} color="#6b7280" />
                  )}
                </StyledView>
                
                <StyledTouchableOpacity
                  onPress={handleUploadImage}
                  className="absolute bottom-0 right-0 bg-primary-600 rounded-full p-2"
                >
                  <Icon name="camera" size={16} color="#ffffff" />
                </StyledTouchableOpacity>
              </StyledView>
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={100}>
            <Input
              label={t('fullName')}
              value={formData.name}
              onChangeText={(value) => handleChange('name', value)}
              placeholder={t('fullName')}
              leftIcon="account"
              error={errors.name}
            />
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={200}>
            <Input
              label={t('email')}
              value={formData.email}
              onChangeText={(value) => handleChange('email', value)}
              placeholder={t('email')}
              keyboardType="email-address"
              leftIcon="email"
              error={errors.email}
            />
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={300}>
            <Input
              label={t('phone')}
              value={formData.phone}
              onChangeText={(value) => handleChange('phone', value)}
              placeholder={t('phone')}
              keyboardType="phone-pad"
              leftIcon="phone"
              error={errors.phone}
            />
          </AnimatedView>
          
          <AnimatedView animation="fadeInUp" duration={300} delay={400}>
            <StyledView className="mt-4">
              <Button
                title={t('save')}
                onPress={handleSave}
                loading={loading}
                fullWidth
              />
            </StyledView>
          </AnimatedView>
        </StyledView>
      </StyledScrollView>
    </StyledKeyboardAvoidingView>
  );
};

export default EditProfileScreen;
