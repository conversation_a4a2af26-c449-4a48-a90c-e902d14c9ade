import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Button from '../../components/Button';
import LoadingIndicator from '../../components/LoadingIndicator';
import EmptyState from '../../components/EmptyState';
import { showToast } from '../../store/slices/uiSlice';
import { ROUTES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledFlatList = styled(FlatList);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const AddressesScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate API call to fetch addresses
    setTimeout(() => {
      setAddresses([
        {
          id: '1',
          name: 'Home',
          fullName: 'John Doe',
          address: '123 Main Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001',
          country: 'India',
          phone: '9876543210',
          isDefault: true,
        },
        {
          id: '2',
          name: 'Office',
          fullName: 'John Doe',
          address: '456 Business Park',
          city: 'Bangalore',
          state: 'Karnataka',
          zipCode: '560001',
          country: 'India',
          phone: '9876543210',
          isDefault: false,
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);
  
  const handleAddAddress = () => {
    navigation.navigate(ROUTES.ADD_ADDRESS);
  };
  
  const handleEditAddress = (address) => {
    navigation.navigate(ROUTES.ADD_ADDRESS, { address });
  };
  
  const handleDeleteAddress = (addressId) => {
    setAddresses(addresses.filter(address => address.id !== addressId));
    dispatch(showToast({
      message: t('addressDeleted'),
      type: 'success',
    }));
  };
  
  const handleSetDefaultAddress = (addressId) => {
    setAddresses(addresses.map(address => ({
      ...address,
      isDefault: address.id === addressId,
    })));
    dispatch(showToast({
      message: t('defaultAddressUpdated'),
      type: 'success',
    }));
  };
  
  const renderAddressItem = ({ item, index }) => {
    return (
      <AnimatedView
        animation="fadeInUp"
        delay={index * 100}
        duration={300}
      >
        <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4">
          <StyledView className="border-b border-neutral-200 dark:border-neutral-700 p-3 flex-row justify-between items-center">
            <StyledView className="flex-row items-center">
              <Icon 
                name={item.name === 'Home' ? 'home' : 'office-building'} 
                size={18} 
                color="#0284c7" 
              />
              <StyledText className="text-neutral-900 dark:text-white font-medium ml-2">
                {item.name}
              </StyledText>
            </StyledView>
            
            {item.isDefault && (
              <StyledView className="bg-primary-100 dark:bg-primary-900 px-2 py-1 rounded-full">
                <StyledText className="text-primary-600 dark:text-primary-400 text-xs font-medium">
                  {t('default')}
                </StyledText>
              </StyledView>
            )}
          </StyledView>
          
          <StyledView className="p-3">
            <StyledText className="text-neutral-900 dark:text-white font-medium mb-1">
              {item.fullName}
            </StyledText>
            
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {item.address}
            </StyledText>
            
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {`${item.city}, ${item.state} ${item.zipCode}`}
            </StyledText>
            
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {item.country}
            </StyledText>
            
            <StyledText className="text-neutral-600 dark:text-neutral-400 mt-1">
              {`${t('phone')}: ${item.phone}`}
            </StyledText>
          </StyledView>
          
          <StyledView className="border-t border-neutral-200 dark:border-neutral-700 p-3 flex-row">
            <StyledTouchableOpacity
              onPress={() => handleEditAddress(item)}
              className="flex-1 flex-row items-center justify-center"
            >
              <Icon name="pencil" size={16} color="#0284c7" />
              <StyledText className="text-primary-600 dark:text-primary-400 ml-1">
                {t('edit')}
              </StyledText>
            </StyledTouchableOpacity>
            
            <StyledView className="border-l border-neutral-200 dark:border-neutral-700 h-6 my-auto mx-2" />
            
            <StyledTouchableOpacity
              onPress={() => handleDeleteAddress(item.id)}
              className="flex-1 flex-row items-center justify-center"
            >
              <Icon name="delete" size={16} color="#ef4444" />
              <StyledText className="text-error-default ml-1">
                {t('delete')}
              </StyledText>
            </StyledTouchableOpacity>
            
            {!item.isDefault && (
              <>
                <StyledView className="border-l border-neutral-200 dark:border-neutral-700 h-6 my-auto mx-2" />
                
                <StyledTouchableOpacity
                  onPress={() => handleSetDefaultAddress(item.id)}
                  className="flex-1 flex-row items-center justify-center"
                >
                  <Icon name="check-circle" size={16} color="#22c55e" />
                  <StyledText className="text-success-default ml-1">
                    {t('setDefault')}
                  </StyledText>
                </StyledTouchableOpacity>
              </>
            )}
          </StyledView>
        </StyledView>
      </AnimatedView>
    );
  };
  
  if (loading) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('addresses')} />
        <LoadingIndicator fullScreen />
      </StyledView>
    );
  }
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('addresses')} />
      
      <StyledFlatList
        data={addresses}
        renderItem={renderAddressItem}
        keyExtractor={(item) => item.id}
        contentContainerClassName="p-4"
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <EmptyState
            title={t('noAddresses')}
            message={t('noAddressesDescription')}
            buttonTitle={t('addAddress')}
            onButtonPress={handleAddAddress}
          />
        }
      />
      
      <StyledView className="p-4 border-t border-neutral-200 dark:border-neutral-700">
        <Button
          title={t('addNewAddress')}
          onPress={handleAddAddress}
          fullWidth
        />
      </StyledView>
    </StyledView>
  );
};

export default AddressesScreen;
