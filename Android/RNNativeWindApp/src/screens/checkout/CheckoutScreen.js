import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Button from '../../components/Button';
import Input from '../../components/Input';
import LoadingIndicator from '../../components/LoadingIndicator';
import { fetchCart } from '../../store/slices/cartSlice';
import { showToast } from '../../store/slices/uiSlice';
import { ROUTES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledSwitch = styled(Switch);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const CheckoutScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { items, total, loading } = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);
  
  const [shippingInfo, setShippingInfo] = useState({
    firstName: user?.name ? user.name.split(' ')[0] : '',
    lastName: user?.name ? user.name.split(' ').slice(1).join(' ') : '',
    email: user?.email || '',
    phone: user?.phone || '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'India',
  });
  
  const [billingInfo, setBillingInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'India',
  });
  
  const [sameAsShipping, setSameAsShipping] = useState(true);
  const [errors, setErrors] = useState({});
  const [step, setStep] = useState(1); // 1: Shipping, 2: Billing, 3: Review
  
  useEffect(() => {
    dispatch(fetchCart());
  }, []);
  
  useEffect(() => {
    if (sameAsShipping) {
      setBillingInfo(shippingInfo);
    }
  }, [sameAsShipping, shippingInfo]);
  
  const handleShippingChange = (field, value) => {
    setShippingInfo({
      ...shippingInfo,
      [field]: value,
    });
    
    // Clear error when user types
    if (errors[`shipping_${field}`]) {
      setErrors({
        ...errors,
        [`shipping_${field}`]: null,
      });
    }
  };
  
  const handleBillingChange = (field, value) => {
    setBillingInfo({
      ...billingInfo,
      [field]: value,
    });
    
    // Clear error when user types
    if (errors[`billing_${field}`]) {
      setErrors({
        ...errors,
        [`billing_${field}`]: null,
      });
    }
  };
  
  const validateShipping = () => {
    const newErrors = {};
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'state', 'zipCode'];
    
    requiredFields.forEach(field => {
      if (!shippingInfo[field]) {
        newErrors[`shipping_${field}`] = t('requiredField');
      }
    });
    
    // Email validation
    if (shippingInfo.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(shippingInfo.email)) {
      newErrors.shipping_email = t('invalidEmail');
    }
    
    // Phone validation
    if (shippingInfo.phone && !/^[0-9]{10}$/.test(shippingInfo.phone)) {
      newErrors.shipping_phone = t('invalidPhone');
    }
    
    // ZIP code validation
    if (shippingInfo.zipCode && !/^[0-9]{6}$/.test(shippingInfo.zipCode)) {
      newErrors.shipping_zipCode = t('invalidZipCode');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const validateBilling = () => {
    if (sameAsShipping) return true;
    
    const newErrors = {};
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'state', 'zipCode'];
    
    requiredFields.forEach(field => {
      if (!billingInfo[field]) {
        newErrors[`billing_${field}`] = t('requiredField');
      }
    });
    
    // Email validation
    if (billingInfo.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(billingInfo.email)) {
      newErrors.billing_email = t('invalidEmail');
    }
    
    // Phone validation
    if (billingInfo.phone && !/^[0-9]{10}$/.test(billingInfo.phone)) {
      newErrors.billing_phone = t('invalidPhone');
    }
    
    // ZIP code validation
    if (billingInfo.zipCode && !/^[0-9]{6}$/.test(billingInfo.zipCode)) {
      newErrors.billing_zipCode = t('invalidZipCode');
    }
    
    setErrors({
      ...errors,
      ...newErrors,
    });
    
    return Object.keys(newErrors).length === 0;
  };
  
  const handleContinue = () => {
    if (step === 1) {
      if (validateShipping()) {
        setStep(2);
      }
    } else if (step === 2) {
      if (validateBilling()) {
        setStep(3);
      }
    } else if (step === 3) {
      navigation.navigate(ROUTES.PAYMENT, {
        shippingInfo,
        billingInfo: sameAsShipping ? shippingInfo : billingInfo,
        total,
      });
    }
  };
  
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    } else {
      navigation.goBack();
    }
  };
  
  if (loading && !items.length) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('checkout')} />
        <LoadingIndicator fullScreen />
      </StyledView>
    );
  }
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('checkout')} />
      
      <StyledView className="flex-row justify-between px-4 py-2 bg-neutral-100 dark:bg-neutral-800">
        <StyledView className="items-center">
          <StyledView 
            className={`w-8 h-8 rounded-full items-center justify-center ${
              step >= 1 ? 'bg-primary-600' : 'bg-neutral-300 dark:bg-neutral-600'
            }`}
          >
            <Icon 
              name="map-marker" 
              size={18} 
              color="#ffffff" 
            />
          </StyledView>
          <StyledText 
            className={`text-xs mt-1 ${
              step >= 1 
                ? 'text-primary-600 dark:text-primary-400' 
                : 'text-neutral-500 dark:text-neutral-400'
            }`}
          >
            {t('shipping')}
          </StyledText>
        </StyledView>
        
        <StyledView className="flex-1 items-center justify-center">
          <StyledView 
            className={`h-0.5 w-full ${
              step >= 2 ? 'bg-primary-600' : 'bg-neutral-300 dark:bg-neutral-600'
            }`} 
          />
        </StyledView>
        
        <StyledView className="items-center">
          <StyledView 
            className={`w-8 h-8 rounded-full items-center justify-center ${
              step >= 2 ? 'bg-primary-600' : 'bg-neutral-300 dark:bg-neutral-600'
            }`}
          >
            <Icon 
              name="credit-card" 
              size={18} 
              color="#ffffff" 
            />
          </StyledView>
          <StyledText 
            className={`text-xs mt-1 ${
              step >= 2 
                ? 'text-primary-600 dark:text-primary-400' 
                : 'text-neutral-500 dark:text-neutral-400'
            }`}
          >
            {t('billing')}
          </StyledText>
        </StyledView>
        
        <StyledView className="flex-1 items-center justify-center">
          <StyledView 
            className={`h-0.5 w-full ${
              step >= 3 ? 'bg-primary-600' : 'bg-neutral-300 dark:bg-neutral-600'
            }`} 
          />
        </StyledView>
        
        <StyledView className="items-center">
          <StyledView 
            className={`w-8 h-8 rounded-full items-center justify-center ${
              step >= 3 ? 'bg-primary-600' : 'bg-neutral-300 dark:bg-neutral-600'
            }`}
          >
            <Icon 
              name="check" 
              size={18} 
              color="#ffffff" 
            />
          </StyledView>
          <StyledText 
            className={`text-xs mt-1 ${
              step >= 3 
                ? 'text-primary-600 dark:text-primary-400' 
                : 'text-neutral-500 dark:text-neutral-400'
            }`}
          >
            {t('review')}
          </StyledText>
        </StyledView>
      </StyledView>
      
      <StyledScrollView className="flex-1 p-4">
        {step === 1 && (
          <AnimatedView animation="fadeIn" duration={300}>
            <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-4">
              {t('shippingAddress')}
            </StyledText>
            
            <StyledView className="flex-row">
              <Input
                label={t('firstName')}
                value={shippingInfo.firstName}
                onChangeText={(value) => handleShippingChange('firstName', value)}
                placeholder={t('firstName')}
                error={errors.shipping_firstName}
                className="flex-1 mr-2"
              />
              
              <Input
                label={t('lastName')}
                value={shippingInfo.lastName}
                onChangeText={(value) => handleShippingChange('lastName', value)}
                placeholder={t('lastName')}
                error={errors.shipping_lastName}
                className="flex-1 ml-2"
              />
            </StyledView>
            
            <Input
              label={t('email')}
              value={shippingInfo.email}
              onChangeText={(value) => handleShippingChange('email', value)}
              placeholder={t('email')}
              keyboardType="email-address"
              error={errors.shipping_email}
            />
            
            <Input
              label={t('phone')}
              value={shippingInfo.phone}
              onChangeText={(value) => handleShippingChange('phone', value)}
              placeholder={t('phone')}
              keyboardType="phone-pad"
              error={errors.shipping_phone}
            />
            
            <Input
              label={t('address')}
              value={shippingInfo.address}
              onChangeText={(value) => handleShippingChange('address', value)}
              placeholder={t('address')}
              error={errors.shipping_address}
            />
            
            <StyledView className="flex-row">
              <Input
                label={t('city')}
                value={shippingInfo.city}
                onChangeText={(value) => handleShippingChange('city', value)}
                placeholder={t('city')}
                error={errors.shipping_city}
                className="flex-1 mr-2"
              />
              
              <Input
                label={t('state')}
                value={shippingInfo.state}
                onChangeText={(value) => handleShippingChange('state', value)}
                placeholder={t('state')}
                error={errors.shipping_state}
                className="flex-1 ml-2"
              />
            </StyledView>
            
            <StyledView className="flex-row">
              <Input
                label={t('zipCode')}
                value={shippingInfo.zipCode}
                onChangeText={(value) => handleShippingChange('zipCode', value)}
                placeholder={t('zipCode')}
                keyboardType="numeric"
                error={errors.shipping_zipCode}
                className="flex-1 mr-2"
              />
              
              <Input
                label={t('country')}
                value={shippingInfo.country}
                onChangeText={(value) => handleShippingChange('country', value)}
                placeholder={t('country')}
                error={errors.shipping_country}
                className="flex-1 ml-2"
              />
            </StyledView>
          </AnimatedView>
        )}
        
        {step === 2 && (
          <AnimatedView animation="fadeIn" duration={300}>
            <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-4">
              {t('billingAddress')}
            </StyledText>
            
            <StyledView className="flex-row items-center mb-4">
              <StyledSwitch
                value={sameAsShipping}
                onValueChange={setSameAsShipping}
                trackColor={{ false: '#767577', true: '#0284c7' }}
                thumbColor={sameAsShipping ? '#ffffff' : '#f4f3f4'}
              />
              <StyledText className="text-neutral-700 dark:text-neutral-300 ml-2">
                {t('sameAsShipping')}
              </StyledText>
            </StyledView>
            
            {!sameAsShipping && (
              <>
                <StyledView className="flex-row">
                  <Input
                    label={t('firstName')}
                    value={billingInfo.firstName}
                    onChangeText={(value) => handleBillingChange('firstName', value)}
                    placeholder={t('firstName')}
                    error={errors.billing_firstName}
                    className="flex-1 mr-2"
                  />
                  
                  <Input
                    label={t('lastName')}
                    value={billingInfo.lastName}
                    onChangeText={(value) => handleBillingChange('lastName', value)}
                    placeholder={t('lastName')}
                    error={errors.billing_lastName}
                    className="flex-1 ml-2"
                  />
                </StyledView>
                
                <Input
                  label={t('email')}
                  value={billingInfo.email}
                  onChangeText={(value) => handleBillingChange('email', value)}
                  placeholder={t('email')}
                  keyboardType="email-address"
                  error={errors.billing_email}
                />
                
                <Input
                  label={t('phone')}
                  value={billingInfo.phone}
                  onChangeText={(value) => handleBillingChange('phone', value)}
                  placeholder={t('phone')}
                  keyboardType="phone-pad"
                  error={errors.billing_phone}
                />
                
                <Input
                  label={t('address')}
                  value={billingInfo.address}
                  onChangeText={(value) => handleBillingChange('address', value)}
                  placeholder={t('address')}
                  error={errors.billing_address}
                />
                
                <StyledView className="flex-row">
                  <Input
                    label={t('city')}
                    value={billingInfo.city}
                    onChangeText={(value) => handleBillingChange('city', value)}
                    placeholder={t('city')}
                    error={errors.billing_city}
                    className="flex-1 mr-2"
                  />
                  
                  <Input
                    label={t('state')}
                    value={billingInfo.state}
                    onChangeText={(value) => handleBillingChange('state', value)}
                    placeholder={t('state')}
                    error={errors.billing_state}
                    className="flex-1 ml-2"
                  />
                </StyledView>
                
                <StyledView className="flex-row">
                  <Input
                    label={t('zipCode')}
                    value={billingInfo.zipCode}
                    onChangeText={(value) => handleBillingChange('zipCode', value)}
                    placeholder={t('zipCode')}
                    keyboardType="numeric"
                    error={errors.billing_zipCode}
                    className="flex-1 mr-2"
                  />
                  
                  <Input
                    label={t('country')}
                    value={billingInfo.country}
                    onChangeText={(value) => handleBillingChange('country', value)}
                    placeholder={t('country')}
                    error={errors.billing_country}
                    className="flex-1 ml-2"
                  />
                </StyledView>
              </>
            )}
          </AnimatedView>
        )}
        
        {step === 3 && (
          <AnimatedView animation="fadeIn" duration={300}>
            <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-4">
              {t('orderSummary')}
            </StyledText>
            
            <StyledView className="bg-neutral-100 dark:bg-neutral-800 rounded-lg p-4 mb-4">
              <StyledText className="text-neutral-900 dark:text-white font-medium mb-2">
                {t('shippingAddress')}
              </StyledText>
              
              <StyledText className="text-neutral-700 dark:text-neutral-300">
                {`${shippingInfo.firstName} ${shippingInfo.lastName}`}
              </StyledText>
              <StyledText className="text-neutral-700 dark:text-neutral-300">
                {shippingInfo.address}
              </StyledText>
              <StyledText className="text-neutral-700 dark:text-neutral-300">
                {`${shippingInfo.city}, ${shippingInfo.state} ${shippingInfo.zipCode}`}
              </StyledText>
              <StyledText className="text-neutral-700 dark:text-neutral-300">
                {shippingInfo.country}
              </StyledText>
              <StyledText className="text-neutral-700 dark:text-neutral-300">
                {`${t('phone')}: ${shippingInfo.phone}`}
              </StyledText>
              <StyledText className="text-neutral-700 dark:text-neutral-300">
                {`${t('email')}: ${shippingInfo.email}`}
              </StyledText>
            </StyledView>
            
            <StyledView className="bg-neutral-100 dark:bg-neutral-800 rounded-lg p-4 mb-4">
              <StyledText className="text-neutral-900 dark:text-white font-medium mb-2">
                {t('billingAddress')}
              </StyledText>
              
              {sameAsShipping ? (
                <StyledText className="text-neutral-700 dark:text-neutral-300 italic">
                  {t('sameAsShipping')}
                </StyledText>
              ) : (
                <>
                  <StyledText className="text-neutral-700 dark:text-neutral-300">
                    {`${billingInfo.firstName} ${billingInfo.lastName}`}
                  </StyledText>
                  <StyledText className="text-neutral-700 dark:text-neutral-300">
                    {billingInfo.address}
                  </StyledText>
                  <StyledText className="text-neutral-700 dark:text-neutral-300">
                    {`${billingInfo.city}, ${billingInfo.state} ${billingInfo.zipCode}`}
                  </StyledText>
                  <StyledText className="text-neutral-700 dark:text-neutral-300">
                    {billingInfo.country}
                  </StyledText>
                  <StyledText className="text-neutral-700 dark:text-neutral-300">
                    {`${t('phone')}: ${billingInfo.phone}`}
                  </StyledText>
                  <StyledText className="text-neutral-700 dark:text-neutral-300">
                    {`${t('email')}: ${billingInfo.email}`}
                  </StyledText>
                </>
              )}
            </StyledView>
            
            <StyledView className="bg-neutral-100 dark:bg-neutral-800 rounded-lg p-4 mb-4">
              <StyledText className="text-neutral-900 dark:text-white font-medium mb-2">
                {t('orderItems')}
              </StyledText>
              
              {items.map((item) => (
                <StyledView key={item.id} className="flex-row justify-between mb-2">
                  <StyledText className="text-neutral-700 dark:text-neutral-300 flex-1">
                    {`${item.product_name} x ${item.product_uom_qty}`}
                  </StyledText>
                  <StyledText className="text-neutral-900 dark:text-white font-medium">
                    ₹{(item.price_unit * item.product_uom_qty).toFixed(2)}
                  </StyledText>
                </StyledView>
              ))}
              
              <StyledView className="border-t border-neutral-300 dark:border-neutral-600 mt-2 pt-2">
                <StyledView className="flex-row justify-between">
                  <StyledText className="text-neutral-900 dark:text-white font-bold">
                    {t('total')}
                  </StyledText>
                  <StyledText className="text-primary-600 dark:text-primary-400 font-bold">
                    ₹{total.toFixed(2)}
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>
          </AnimatedView>
        )}
      </StyledScrollView>
      
      <StyledView className="p-4 border-t border-neutral-200 dark:border-neutral-700 flex-row">
        <Button
          title={t('back')}
          onPress={handleBack}
          variant="outline"
          className="flex-1 mr-2"
        />
        
        <Button
          title={step === 3 ? t('proceedToPayment') : t('continue')}
          onPress={handleContinue}
          className="flex-1 ml-2"
        />
      </StyledView>
    </StyledView>
  );
};

export default CheckoutScreen;
