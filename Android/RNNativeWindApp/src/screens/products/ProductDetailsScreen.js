import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Dimensions, Alert } from 'react-native';
import { styled } from 'nativewind';
import { useRoute, useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';
import FastImage from 'react-native-fast-image';

import Header from '../../components/Header';
import Button from '../../components/Button';
import LoadingIndicator from '../../components/LoadingIndicator';
import AttributeSelector from '../../components/AttributeSelector';
import ImageGallery from '../../components/ImageGallery';
import PriceDisplay from '../../components/PriceDisplay';
import { fetchProductDetails, clearCurrentProduct } from '../../store/slices/productsSlice';
import { addItemToCart } from '../../store/slices/cartSlice';
import { showToast } from '../../store/slices/uiSlice';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledImage = styled(FastImage);
const StyledTouchableOpacity = styled(TouchableOpacity);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const { width } = Dimensions.get('window');

const ProductDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { productId } = route.params || {};
  const { currentProduct, variants, attributes, loading } = useSelector((state) => state.products);
  const { isAuthenticated } = useSelector((state) => state.auth);

  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [selectedAttributes, setSelectedAttributes] = useState({});
  const [variantImages, setVariantImages] = useState([]);
  const [stockStatus, setStockStatus] = useState({ inStock: true, quantity: 0 });
  const [priceDetails, setPriceDetails] = useState({
    basePrice: 0,
    finalPrice: 0,
    discount: 0,
    tax: 0,
    attributeAdjustments: []
  });

  // Fetch product details when component mounts
  useEffect(() => {
    if (productId) {
      dispatch(fetchProductDetails(productId));
    }

    return () => {
      dispatch(clearCurrentProduct());
    };
  }, [productId]);

  // Set initial variant and attributes when product data loads
  useEffect(() => {
    if (variants.length > 0) {
      // Select first variant by default
      const firstVariant = variants[0];
      setSelectedVariant(firstVariant);

      // Extract attribute values from the variant
      if (firstVariant.attribute_value_ids) {
        const initialAttributes = {};
        firstVariant.attribute_value_ids.forEach(valueId => {
          // Find the attribute this value belongs to
          for (const attr of attributes) {
            const option = attr.options.find(opt => opt.id === valueId);
            if (option) {
              initialAttributes[attr.id] = valueId;
              break;
            }
          }
        });
        setSelectedAttributes(initialAttributes);
      }

      // Set variant images
      if (firstVariant.image_1920) {
        setVariantImages([{ uri: `data:image/jpeg;base64,${firstVariant.image_1920}` }]);
      }

      // Set stock status
      setStockStatus({
        inStock: firstVariant.qty_available > 0,
        quantity: firstVariant.qty_available || 0
      });
    }
  }, [variants, attributes]);

  // Update price details when variant or quantity changes
  useEffect(() => {
    if (currentProduct && selectedVariant) {
      // Calculate base price
      const basePrice = currentProduct.list_price;

      // Calculate attribute adjustments
      const adjustments = [];
      Object.entries(selectedAttributes).forEach(([attrId, valueId]) => {
        const attribute = attributes.find(a => a.id === parseInt(attrId));
        if (attribute) {
          const option = attribute.options.find(o => o.id === valueId);
          if (option && option.priceAdjustment) {
            adjustments.push({
              name: `${attribute.name}: ${option.name}`,
              amount: option.priceAdjustment
            });
          }
        }
      });

      // Calculate final price
      const variantPrice = selectedVariant.list_price;
      const finalPrice = variantPrice;

      // Calculate tax (example: 18% GST)
      const taxRate = 0.18;
      const tax = finalPrice * taxRate;

      // Update price details
      setPriceDetails({
        basePrice,
        finalPrice,
        discount: basePrice > finalPrice ? ((basePrice - finalPrice) / basePrice) * 100 : 0,
        tax,
        attributeAdjustments: adjustments
      });
    }
  }, [currentProduct, selectedVariant, selectedAttributes, quantity, attributes]);

  // Find matching variant based on selected attributes
  const findMatchingVariant = useCallback((attributeSelections) => {
    if (!variants || variants.length === 0) return null;

    // If no attributes selected or only one variant, return the first variant
    if (Object.keys(attributeSelections).length === 0 || variants.length === 1) {
      return variants[0];
    }

    // Find variant that matches all selected attributes
    return variants.find(variant => {
      if (!variant.attribute_value_ids) return false;

      // Check if all selected attributes are in this variant
      for (const [attrId, valueId] of Object.entries(attributeSelections)) {
        const hasAttribute = variant.attribute_value_ids.includes(valueId);
        if (!hasAttribute) return false;
      }

      return true;
    });
  }, [variants]);

  // Handle attribute selection
  const handleAttributeChange = useCallback((attributeId, valueId) => {
    // Update selected attributes
    const newAttributes = { ...selectedAttributes, [attributeId]: valueId };
    setSelectedAttributes(newAttributes);

    // Find matching variant
    const matchingVariant = findMatchingVariant(newAttributes);
    if (matchingVariant) {
      setSelectedVariant(matchingVariant);

      // Update variant images
      if (matchingVariant.image_1920) {
        setVariantImages([{ uri: `data:image/jpeg;base64,${matchingVariant.image_1920}` }]);
      } else if (currentProduct.image_1920) {
        setVariantImages([{ uri: `data:image/jpeg;base64,${currentProduct.image_1920}` }]);
      }

      // Update stock status
      setStockStatus({
        inStock: matchingVariant.qty_available > 0,
        quantity: matchingVariant.qty_available || 0
      });
    }
  }, [selectedAttributes, findMatchingVariant, currentProduct]);

  // Check if the current attribute selection is valid
  const isValidSelection = useMemo(() => {
    // If no variants, any selection is valid
    if (!variants || variants.length === 0) return true;

    // If we have a matching variant, the selection is valid
    return !!findMatchingVariant(selectedAttributes);
  }, [variants, selectedAttributes, findMatchingVariant]);

  // Get available options for an attribute based on current selections
  const getAvailableOptions = useCallback((attributeId) => {
    if (!attributes || attributes.length === 0) return [];

    const attribute = attributes.find(attr => attr.id === attributeId);
    if (!attribute) return [];

    // If no other attributes are selected, all options are available
    const otherSelections = { ...selectedAttributes };
    delete otherSelections[attributeId];

    if (Object.keys(otherSelections).length === 0) {
      return attribute.options;
    }

    // Filter options based on other selected attributes
    return attribute.options.filter(option => {
      // Check if there's at least one variant with this option and other selections
      return variants.some(variant => {
        if (!variant.attribute_value_ids) return false;

        // Must have the current option
        if (!variant.attribute_value_ids.includes(option.id)) return false;

        // Must have all other selected attributes
        for (const [attrId, valueId] of Object.entries(otherSelections)) {
          if (!variant.attribute_value_ids.includes(valueId)) return false;
        }

        return true;
      });
    });
  }, [attributes, variants, selectedAttributes]);

  // Handle adding to cart
  const handleAddToCart = () => {
    if (!isAuthenticated) {
      dispatch(showToast({
        message: t('loginToAddToCart'),
        type: 'warning',
      }));
      return;
    }

    // Validate selection
    if (!isValidSelection) {
      dispatch(showToast({
        message: t('pleaseSelectValidOptions'),
        type: 'error',
      }));
      return;
    }

    // Check stock
    if (!stockStatus.inStock || stockStatus.quantity < quantity) {
      dispatch(showToast({
        message: t('notEnoughStock'),
        type: 'error',
      }));
      return;
    }

    const id = selectedVariant ? selectedVariant.id : currentProduct.id;
    dispatch(addItemToCart({
      productId: id,
      quantity,
      attributes: selectedAttributes
    }));
    dispatch(showToast({
      message: t('addedToCart'),
      type: 'success',
    }));
  };

  // Handle buy now
  const handleBuyNow = () => {
    if (!isAuthenticated) {
      dispatch(showToast({
        message: t('loginToBuy'),
        type: 'warning',
      }));
      return;
    }

    // Same validation as add to cart
    if (!isValidSelection || !stockStatus.inStock || stockStatus.quantity < quantity) {
      return;
    }

    handleAddToCart();
    navigation.navigate('Cart');
  };

  // Handle quantity changes
  const increaseQuantity = () => {
    // Don't allow increasing beyond available stock
    if (stockStatus.inStock && quantity < stockStatus.quantity) {
      setQuantity(quantity + 1);
    } else if (!stockStatus.inStock) {
      dispatch(showToast({
        message: t('outOfStock'),
        type: 'error',
      }));
    } else {
      dispatch(showToast({
        message: t('maxQuantityReached'),
        type: 'warning',
      }));
    }
  };

  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  if (loading || !currentProduct) {
    return <LoadingIndicator fullScreen />;
  }

  // Prepare images for gallery
  const images = variantImages.length > 0
    ? variantImages
    : currentProduct.image_1920
      ? [{ uri: `data:image/jpeg;base64,${currentProduct.image_1920}` }]
      : [];

  // Get SKU
  const sku = selectedVariant?.default_code || currentProduct.default_code || '';

  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header
        title={t('productDetails')}
        transparent
      />

      <StyledScrollView showsVerticalScrollIndicator={false}>
        {/* Image Gallery */}
        <ImageGallery
          images={images}
          fallbackImage="https://via.placeholder.com/300"
        />

        <StyledView className="p-4">
          {/* Product Title and SKU */}
          <AnimatedView animation="fadeInUp" duration={500} delay={100}>
            <StyledText className="text-neutral-900 dark:text-white text-xl font-bold mb-1">
              {currentProduct.name}
            </StyledText>

            {sku && (
              <StyledText className="text-neutral-500 dark:text-neutral-400 text-sm mb-2">
                {t('sku')}: {sku}
              </StyledText>
            )}
          </AnimatedView>

          {/* Price Display */}
          <AnimatedView animation="fadeInUp" duration={500} delay={150}>
            <PriceDisplay
              basePrice={priceDetails.basePrice}
              finalPrice={priceDetails.finalPrice}
              discount={priceDetails.discount}
              tax={priceDetails.tax}
              quantity={quantity}
              attributeAdjustments={priceDetails.attributeAdjustments}
            />
          </AnimatedView>

          {/* Stock Status */}
          <AnimatedView animation="fadeInUp" duration={500} delay={200}>
            <StyledView className="flex-row items-center mb-4">
              <Icon
                name={stockStatus.inStock ? "check-circle" : "alert-circle"}
                size={20}
                color={stockStatus.inStock ? "#10b981" : "#ef4444"}
              />
              <StyledText
                className={`ml-2 ${stockStatus.inStock ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}
              >
                {stockStatus.inStock
                  ? t('inStock', { count: stockStatus.quantity })
                  : t('outOfStock')}
              </StyledText>
            </StyledView>
          </AnimatedView>

          {/* Dynamic Attributes */}
          {attributes && attributes.length > 0 && (
            <AnimatedView animation="fadeInUp" duration={500} delay={250}>
              <StyledText className="text-neutral-700 dark:text-neutral-300 font-medium mb-2">
                {t('productOptions')}
              </StyledText>

              {attributes.map(attribute => (
                <AttributeSelector
                  key={attribute.id}
                  attribute={{
                    ...attribute,
                    options: getAvailableOptions(attribute.id)
                  }}
                  selectedValues={selectedAttributes[attribute.id]}
                  onValueChange={handleAttributeChange}
                  disabled={!stockStatus.inStock}
                />
              ))}
            </AnimatedView>
          )}

          {/* Quantity Selector */}
          <AnimatedView animation="fadeInUp" duration={500} delay={300}>
            <StyledText className="text-neutral-700 dark:text-neutral-300 font-medium mb-2 mt-4">
              {t('quantity')}
            </StyledText>

            <StyledView className="flex-row items-center mb-6">
              <StyledTouchableOpacity
                onPress={decreaseQuantity}
                className="border border-neutral-300 dark:border-neutral-700 rounded-l-lg p-2"
                disabled={!stockStatus.inStock}
              >
                <Icon name="minus" size={20} color={stockStatus.inStock ? "#6b7280" : "#9ca3af"} />
              </StyledTouchableOpacity>

              <StyledView className="border-t border-b border-neutral-300 dark:border-neutral-700 py-2 px-4">
                <StyledText className="text-neutral-900 dark:text-white text-base min-w-[30px] text-center">
                  {quantity}
                </StyledText>
              </StyledView>

              <StyledTouchableOpacity
                onPress={increaseQuantity}
                className="border border-neutral-300 dark:border-neutral-700 rounded-r-lg p-2"
                disabled={!stockStatus.inStock || quantity >= stockStatus.quantity}
              >
                <Icon
                  name="plus"
                  size={20}
                  color={stockStatus.inStock && quantity < stockStatus.quantity ? "#6b7280" : "#9ca3af"}
                />
              </StyledTouchableOpacity>
            </StyledView>
          </AnimatedView>

          {/* Description */}
          <AnimatedView animation="fadeInUp" duration={500} delay={350}>
            <StyledText className="text-neutral-700 dark:text-neutral-300 font-medium mb-2">
              {t('description')}
            </StyledText>

            <StyledText className="text-neutral-600 dark:text-neutral-400 mb-6">
              {currentProduct.description_sale || t('noDescription')}
            </StyledText>
          </AnimatedView>

          {/* Action Buttons */}
          <AnimatedView animation="fadeInUp" duration={500} delay={400}>
            <StyledView className="flex-row mb-4">
              <Button
                title={t('addToCart')}
                onPress={handleAddToCart}
                className="flex-1 mr-2"
                disabled={!stockStatus.inStock || !isValidSelection}
              />

              <Button
                title={t('buyNow')}
                onPress={handleBuyNow}
                variant="secondary"
                className="flex-1 ml-2"
                disabled={!stockStatus.inStock || !isValidSelection}
              />
            </StyledView>
          </AnimatedView>
        </StyledView>
      </StyledScrollView>
    </StyledView>
  );
};

export default ProductDetailsScreen;
