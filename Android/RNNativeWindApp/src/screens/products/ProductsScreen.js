import React, { useEffect, useState, useRef, useCallback } from 'react';
import { View, Text, FlatList, RefreshControl, TouchableOpacity } from 'react-native';
import { styled } from 'nativewind';
import { useRoute, useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as Animatable from 'react-native-reanimated';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import Header from '../../components/Header';
import ProductCard from '../../components/ProductCard';
import LoadingIndicator from '../../components/LoadingIndicator';
import EmptyState from '../../components/EmptyState';
import SearchBar from '../../components/SearchBar';
import ProductFilters from '../../components/ProductFilters';
import PageSizeSelector from '../../components/PageSizeSelector';
import SortSelector from '../../components/SortSelector';
import {
  fetchProducts,
  clearProducts,
  fetchProductAttributes,
  setFilters,
  setSearchQuery,
  setSorting,
  setPageSize,
  saveScrollPosition,
  resetFilters
} from '../../store/slices/productsSlice';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledFlatList = styled(FlatList);
const StyledTouchableOpacity = styled(TouchableOpacity);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const ProductsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const flatListRef = useRef(null);

  const { categoryId, categoryName } = route.params || {};
  const {
    products,
    loading,
    hasMore,
    page,
    pageSize,
    totalCount,
    filters,
    searchQuery,
    sortBy,
    sortOrder,
    scrollPosition,
    attributes,
    categories
  } = useSelector((state) => state.products);
  const { theme } = useSelector((state) => state.ui);

  const [refreshing, setRefreshing] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  // Load products and attributes when component mounts or category changes
  useEffect(() => {
    dispatch(clearProducts());
    loadProducts();
    dispatch(fetchProductAttributes(categoryId));

    // Clear products when component unmounts
    return () => {
      dispatch(clearProducts());
    };
  }, [categoryId]);

  // Restore scroll position when returning to the screen
  useEffect(() => {
    if (scrollPosition > 0 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current.scrollToOffset({ offset: scrollPosition, animated: false });
      }, 100);
    }
  }, []);

  // Load products with current filters and search
  const loadProducts = useCallback(() => {
    dispatch(fetchProducts({
      categoryId,
      page: 0,
      limit: pageSize,
      filters,
      search: searchQuery,
      sortBy,
      sortOrder
    }));
  }, [categoryId, pageSize, filters, searchQuery, sortBy, sortOrder]);

  // Load more products for pagination
  const loadMoreProducts = () => {
    if (hasMore && !loading) {
      dispatch(fetchProducts({
        categoryId,
        page: page + 1,
        limit: pageSize,
        filters,
        search: searchQuery,
        sortBy,
        sortOrder
      }));
    }
  };

  // Handle pull-to-refresh
  const handleRefresh = () => {
    setRefreshing(true);
    dispatch(clearProducts());
    loadProducts();
    setRefreshing(false);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    dispatch(setFilters(newFilters));
    dispatch(fetchProducts({
      categoryId,
      page: 0,
      limit: pageSize,
      filters: newFilters,
      search: searchQuery,
      sortBy,
      sortOrder
    }));
  };

  // Handle category change
  const handleCategoryChange = (newCategoryId) => {
    navigation.setParams({ categoryId: newCategoryId, categoryName: categories.find(c => c.id === newCategoryId)?.name });
  };

  // Handle search
  const handleSearch = (query) => {
    dispatch(setSearchQuery(query));
    dispatch(fetchProducts({
      categoryId,
      page: 0,
      limit: pageSize,
      filters,
      search: query,
      sortBy,
      sortOrder
    }));
    setShowSearch(false);
  };

  // Handle sort change
  const handleSortChange = (sortOptions) => {
    dispatch(setSorting(sortOptions));
    dispatch(fetchProducts({
      categoryId,
      page: 0,
      limit: pageSize,
      filters,
      search: searchQuery,
      sortBy: sortOptions.sortBy,
      sortOrder: sortOptions.sortOrder
    }));
  };

  // Handle page size change
  const handlePageSizeChange = (size) => {
    dispatch(setPageSize(size));
    dispatch(fetchProducts({
      categoryId,
      page: 0,
      limit: size,
      filters,
      search: searchQuery,
      sortBy,
      sortOrder
    }));
  };

  // Handle scroll to save position
  const handleScroll = (event) => {
    const scrollOffset = event.nativeEvent.contentOffset.y;
    dispatch(saveScrollPosition(scrollOffset));
  };

  // Reset all filters
  const handleResetFilters = () => {
    dispatch(resetFilters());
    dispatch(setSearchQuery(''));
    dispatch(fetchProducts({
      categoryId,
      page: 0,
      limit: pageSize,
      filters: {},
      search: '',
      sortBy,
      sortOrder,
      resetFilters: true
    }));
  };

  // Render product item
  const renderProductItem = ({ item, index }) => {
    return (
      <AnimatedView
        animation="fadeInUp"
        delay={index % 10 * 50}
        duration={500}
        className="w-[48%] mx-1 mb-3"
      >
        <ProductCard product={item} />
      </AnimatedView>
    );
  };

  // Render footer with loading indicator
  const renderFooter = () => {
    if (!loading || refreshing) return null;
    return <LoadingIndicator />;
  };

  // Render toolbar with sorting and page size options
  const renderToolbar = () => {
    return (
      <StyledView className="flex-row items-center justify-between px-4 py-2 bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700">
        <StyledView className="flex-row items-center">
          <SortSelector
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSortChange={handleSortChange}
          />

          <StyledView className="w-2" />

          <PageSizeSelector
            pageSize={pageSize}
            onPageSizeChange={handlePageSizeChange}
          />
        </StyledView>

        {(Object.keys(filters).length > 0 || searchQuery) && (
          <StyledTouchableOpacity
            onPress={handleResetFilters}
            className="px-3 py-1 bg-neutral-100 dark:bg-neutral-700 rounded-full"
          >
            <Icon name="refresh" size={18} color="#6b7280" />
          </StyledTouchableOpacity>
        )}
      </StyledView>
    );
  };

  // Show loading indicator when initially loading
  if (loading && !refreshing && !products.length) {
    return <LoadingIndicator fullScreen />;
  }

  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header
        title={categoryName || t('products')}
        showSearch
        onSearchPress={() => setShowSearch(true)}
      />

      {showSearch && (
        <StyledView className="px-4 py-2 bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700">
          <SearchBar
            onSearch={handleSearch}
            onBlur={() => setShowSearch(false)}
            autoFocus
          />
        </StyledView>
      )}

      <ProductFilters
        filters={attributes}
        selectedFilters={filters}
        onFilterChange={handleFilterChange}
        categories={categories}
        selectedCategory={categoryId}
        onCategoryChange={handleCategoryChange}
        productCount={totalCount}
        onReset={handleResetFilters}
      />

      {renderToolbar()}

      <StyledFlatList
        ref={flatListRef}
        data={products}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        contentContainerClassName="p-2"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme === 'dark' ? '#ffffff' : '#0284c7']}
            tintColor={theme === 'dark' ? '#ffffff' : '#0284c7'}
          />
        }
        onEndReached={loadMoreProducts}
        onEndReachedThreshold={0.5}
        onScroll={handleScroll}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          <EmptyState
            title={searchQuery ? t('noSearchResults') : t('noProducts')}
            message={searchQuery ? t('tryDifferentSearch') : t('noProductsDescription')}
            buttonTitle={t('refresh')}
            onButtonPress={loadProducts}
          />
        }
      />
    </StyledView>
  );
};

export default ProductsScreen;
