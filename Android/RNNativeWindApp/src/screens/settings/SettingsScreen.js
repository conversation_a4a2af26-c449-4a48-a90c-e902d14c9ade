import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { styled } from 'nativewind';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import { setTheme, setLanguage } from '../../store/slices/uiSlice';
import { showToast } from '../../store/slices/uiSlice';
import { LANGUAGES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledSwitch = styled(Switch);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const SettingsScreen = () => {
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  
  const { theme, language } = useSelector((state) => state.ui);
  
  const [showLanguageOptions, setShowLanguageOptions] = useState(false);
  
  const handleThemeChange = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    dispatch(setTheme(newTheme));
  };
  
  const handleLanguageChange = (languageCode) => {
    dispatch(setLanguage(languageCode));
    i18n.changeLanguage(languageCode);
    setShowLanguageOptions(false);
  };
  
  const handleNotificationToggle = () => {
    dispatch(showToast({
      message: t('notificationsNotAvailable'),
      type: 'info',
    }));
  };
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('settings')} />
      
      <StyledScrollView>
        <AnimatedView animation="fadeIn" duration={300}>
          <StyledView className="p-4">
            <StyledText className="text-neutral-500 dark:text-neutral-400 text-xs uppercase mb-2">
              {t('appearance')}
            </StyledText>
            
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-6">
              <StyledView className="flex-row items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
                <StyledView className="flex-row items-center">
                  <Icon name="theme-light-dark" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('darkMode')}
                  </StyledText>
                </StyledView>
                
                <StyledSwitch
                  value={theme === 'dark'}
                  onValueChange={handleThemeChange}
                  trackColor={{ false: '#767577', true: '#0284c7' }}
                  thumbColor={theme === 'dark' ? '#ffffff' : '#f4f3f4'}
                />
              </StyledView>
              
              <StyledTouchableOpacity
                onPress={() => setShowLanguageOptions(!showLanguageOptions)}
                className="flex-row items-center justify-between p-4"
              >
                <StyledView className="flex-row items-center">
                  <Icon name="translate" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('language')}
                  </StyledText>
                </StyledView>
                
                <StyledView className="flex-row items-center">
                  <StyledText className="text-neutral-600 dark:text-neutral-400 mr-2">
                    {LANGUAGES.find(lang => lang.code === language)?.name || 'English'}
                  </StyledText>
                  <Icon 
                    name={showLanguageOptions ? 'chevron-up' : 'chevron-down'} 
                    size={20} 
                    color="#6b7280" 
                  />
                </StyledView>
              </StyledTouchableOpacity>
              
              {showLanguageOptions && (
                <StyledView className="border-t border-neutral-200 dark:border-neutral-700">
                  {LANGUAGES.map((lang) => (
                    <StyledTouchableOpacity
                      key={lang.code}
                      onPress={() => handleLanguageChange(lang.code)}
                      className={`p-4 flex-row items-center justify-between ${
                        lang.code === language
                          ? 'bg-primary-50 dark:bg-primary-900'
                          : ''
                      }`}
                    >
                      <StyledText 
                        className={`ml-8 ${
                          lang.code === language
                            ? 'text-primary-600 dark:text-primary-400 font-medium'
                            : 'text-neutral-900 dark:text-white'
                        }`}
                      >
                        {lang.name}
                      </StyledText>
                      
                      {lang.code === language && (
                        <Icon name="check" size={20} color="#0284c7" />
                      )}
                    </StyledTouchableOpacity>
                  ))}
                </StyledView>
              )}
            </StyledView>
            
            <StyledText className="text-neutral-500 dark:text-neutral-400 text-xs uppercase mb-2">
              {t('notifications')}
            </StyledText>
            
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-6">
              <StyledView className="flex-row items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
                <StyledView className="flex-row items-center">
                  <Icon name="bell" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('pushNotifications')}
                  </StyledText>
                </StyledView>
                
                <StyledSwitch
                  value={false}
                  onValueChange={handleNotificationToggle}
                  trackColor={{ false: '#767577', true: '#0284c7' }}
                  thumbColor="#f4f3f4"
                />
              </StyledView>
              
              <StyledView className="flex-row items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
                <StyledView className="flex-row items-center">
                  <Icon name="email" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('emailNotifications')}
                  </StyledText>
                </StyledView>
                
                <StyledSwitch
                  value={false}
                  onValueChange={handleNotificationToggle}
                  trackColor={{ false: '#767577', true: '#0284c7' }}
                  thumbColor="#f4f3f4"
                />
              </StyledView>
              
              <StyledView className="flex-row items-center justify-between p-4">
                <StyledView className="flex-row items-center">
                  <Icon name="tag" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('promotionalNotifications')}
                  </StyledText>
                </StyledView>
                
                <StyledSwitch
                  value={false}
                  onValueChange={handleNotificationToggle}
                  trackColor={{ false: '#767577', true: '#0284c7' }}
                  thumbColor="#f4f3f4"
                />
              </StyledView>
            </StyledView>
            
            <StyledText className="text-neutral-500 dark:text-neutral-400 text-xs uppercase mb-2">
              {t('about')}
            </StyledText>
            
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-6">
              <StyledTouchableOpacity
                onPress={() => {
                  dispatch(showToast({
                    message: t('privacyPolicyNotAvailable'),
                    type: 'info',
                  }));
                }}
                className="flex-row items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700"
              >
                <StyledView className="flex-row items-center">
                  <Icon name="shield-account" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('privacyPolicy')}
                  </StyledText>
                </StyledView>
                
                <Icon name="chevron-right" size={20} color="#6b7280" />
              </StyledTouchableOpacity>
              
              <StyledTouchableOpacity
                onPress={() => {
                  dispatch(showToast({
                    message: t('termsAndConditionsNotAvailable'),
                    type: 'info',
                  }));
                }}
                className="flex-row items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700"
              >
                <StyledView className="flex-row items-center">
                  <Icon name="file-document" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('termsAndConditions')}
                  </StyledText>
                </StyledView>
                
                <Icon name="chevron-right" size={20} color="#6b7280" />
              </StyledTouchableOpacity>
              
              <StyledTouchableOpacity
                onPress={() => {
                  dispatch(showToast({
                    message: t('aboutUsNotAvailable'),
                    type: 'info',
                  }));
                }}
                className="flex-row items-center justify-between p-4"
              >
                <StyledView className="flex-row items-center">
                  <Icon name="information" size={20} color="#0284c7" />
                  <StyledText className="text-neutral-900 dark:text-white ml-3">
                    {t('aboutUs')}
                  </StyledText>
                </StyledView>
                
                <Icon name="chevron-right" size={20} color="#6b7280" />
              </StyledTouchableOpacity>
            </StyledView>
            
            <StyledView className="items-center mt-4">
              <StyledText className="text-neutral-500 dark:text-neutral-400 text-sm">
                {t('appVersion')} 1.0.0
              </StyledText>
            </StyledView>
          </StyledView>
        </AnimatedView>
      </StyledScrollView>
    </StyledView>
  );
};

export default SettingsScreen;
