import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, Image, TouchableOpacity } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Button from '../../components/Button';
import Input from '../../components/Input';
import LoadingIndicator from '../../components/LoadingIndicator';
import EmptyState from '../../components/EmptyState';
import { fetchCart, updateCartItem, applyCouponCode } from '../../store/slices/cartSlice';
import { ROUTES } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledImage = styled(Image);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledFlatList = styled(FlatList);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const CartScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { items, total, loading, error, discount } = useSelector((state) => state.cart);
  const { isAuthenticated } = useSelector((state) => state.auth);
  
  const [couponCode, setCouponCode] = useState('');
  const [couponLoading, setCouponLoading] = useState(false);
  
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchCart());
    }
  }, [isAuthenticated]);
  
  const handleQuantityChange = (lineId, quantity) => {
    dispatch(updateCartItem({ lineId, quantity }));
  };
  
  const handleApplyCoupon = () => {
    if (!couponCode) return;
    
    setCouponLoading(true);
    dispatch(applyCouponCode(couponCode))
      .finally(() => {
        setCouponLoading(false);
      });
  };
  
  const handleCheckout = () => {
    navigation.navigate(ROUTES.CHECKOUT);
  };
  
  const renderCartItem = ({ item, index }) => {
    // Convert base64 image to URI
    const imageUri = item.product_image
      ? `data:image/jpeg;base64,${item.product_image}`
      : 'https://via.placeholder.com/100';
    
    return (
      <AnimatedView
        animation="fadeInUp"
        delay={index * 100}
        duration={500}
        className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4"
      >
        <StyledView className="flex-row p-3">
          <StyledImage
            source={{ uri: imageUri }}
            className="w-20 h-20 rounded-md"
            resizeMode="cover"
          />
          
          <StyledView className="flex-1 ml-3">
            <StyledText
              numberOfLines={2}
              className="text-neutral-900 dark:text-white font-medium text-base mb-1"
            >
              {item.product_name}
            </StyledText>
            
            <StyledText className="text-primary-600 dark:text-primary-400 font-bold">
              ₹{item.price_unit.toFixed(2)}
            </StyledText>
            
            <StyledView className="flex-row items-center mt-2">
              <StyledTouchableOpacity
                onPress={() => handleQuantityChange(item.id, Math.max(1, item.product_uom_qty - 1))}
                className="border border-neutral-300 dark:border-neutral-700 rounded-l-lg p-1"
              >
                <Icon name="minus" size={16} color="#6b7280" />
              </StyledTouchableOpacity>
              
              <StyledView className="border-t border-b border-neutral-300 dark:border-neutral-700 py-1 px-3">
                <StyledText className="text-neutral-900 dark:text-white text-sm min-w-[20px] text-center">
                  {item.product_uom_qty}
                </StyledText>
              </StyledView>
              
              <StyledTouchableOpacity
                onPress={() => handleQuantityChange(item.id, item.product_uom_qty + 1)}
                className="border border-neutral-300 dark:border-neutral-700 rounded-r-lg p-1"
              >
                <Icon name="plus" size={16} color="#6b7280" />
              </StyledTouchableOpacity>
              
              <StyledText className="ml-auto text-neutral-900 dark:text-white font-medium">
                ₹{(item.price_unit * item.product_uom_qty).toFixed(2)}
              </StyledText>
            </StyledView>
          </StyledView>
        </StyledView>
      </AnimatedView>
    );
  };
  
  if (!isAuthenticated) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('cart')} showBack={false} />
        
        <EmptyState
          title={t('loginRequired')}
          message={t('loginToViewCart')}
          buttonTitle={t('login')}
          onButtonPress={() => navigation.navigate('Auth', { screen: ROUTES.LOGIN })}
        />
      </StyledView>
    );
  }
  
  if (loading && !items.length) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('cart')} showBack={false} />
        <LoadingIndicator fullScreen />
      </StyledView>
    );
  }
  
  if (!items.length) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('cart')} showBack={false} />
        
        <EmptyState
          title={t('emptyCart')}
          message={t('emptyCartDescription')}
          buttonTitle={t('continueShopping')}
          onButtonPress={() => navigation.navigate('HomeTab')}
        />
      </StyledView>
    );
  }
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('cart')} showBack={false} />
      
      <StyledFlatList
        data={items}
        renderItem={renderCartItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerClassName="p-4"
        showsVerticalScrollIndicator={false}
      />
      
      <StyledView className="p-4 border-t border-neutral-200 dark:border-neutral-700">
        <StyledView className="flex-row items-center mb-4">
          <Input
            placeholder={t('couponCode')}
            value={couponCode}
            onChangeText={setCouponCode}
            className="flex-1 mb-0"
          />
          
          <Button
            title={t('apply')}
            onPress={handleApplyCoupon}
            loading={couponLoading}
            size="small"
            className="ml-2"
          />
        </StyledView>
        
        <StyledView className="mb-4">
          <StyledView className="flex-row justify-between mb-2">
            <StyledText className="text-neutral-600 dark:text-neutral-400">
              {t('subtotal')}
            </StyledText>
            <StyledText className="text-neutral-900 dark:text-white font-medium">
              ₹{(total + discount).toFixed(2)}
            </StyledText>
          </StyledView>
          
          {discount > 0 && (
            <StyledView className="flex-row justify-between mb-2">
              <StyledText className="text-neutral-600 dark:text-neutral-400">
                {t('discount')}
              </StyledText>
              <StyledText className="text-green-600 dark:text-green-400 font-medium">
                -₹{discount.toFixed(2)}
              </StyledText>
            </StyledView>
          )}
          
          <StyledView className="flex-row justify-between">
            <StyledText className="text-neutral-900 dark:text-white font-bold">
              {t('total')}
            </StyledText>
            <StyledText className="text-primary-600 dark:text-primary-400 font-bold text-lg">
              ₹{total.toFixed(2)}
            </StyledText>
          </StyledView>
        </StyledView>
        
        <Button
          title={t('proceedToCheckout')}
          onPress={handleCheckout}
          fullWidth
        />
      </StyledView>
    </StyledView>
  );
};

export default CartScreen;
