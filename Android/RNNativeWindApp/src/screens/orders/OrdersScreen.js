import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, RefreshControl } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import LoadingIndicator from '../../components/LoadingIndicator';
import EmptyState from '../../components/EmptyState';
import { getOrders } from '../../api/odooApi';
import { showToast } from '../../store/slices/uiSlice';
import { ROUTES, ORDER_STATUS } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledFlatList = styled(FlatList);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const OrdersScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { isAuthenticated, sessionId } = useSelector((state) => state.auth);
  const { theme } = useSelector((state) => state.ui);
  
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  
  useEffect(() => {
    if (isAuthenticated) {
      loadOrders();
    }
  }, [isAuthenticated]);
  
  const loadOrders = async () => {
    if (!isAuthenticated) return;
    
    setLoading(true);
    try {
      const response = await getOrders(sessionId);
      if (response.success) {
        setOrders(response.orders || []);
      } else {
        dispatch(showToast({
          message: response.error || t('failedToLoadOrders'),
          type: 'error',
        }));
      }
    } catch (error) {
      dispatch(showToast({
        message: error.message || t('failedToLoadOrders'),
        type: 'error',
      }));
    } finally {
      setLoading(false);
    }
  };
  
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };
  
  const navigateToOrderDetails = (orderId) => {
    navigation.navigate(ROUTES.ORDER_DETAILS, { orderId });
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case ORDER_STATUS.PENDING:
        return 'bg-warning-default text-white';
      case ORDER_STATUS.CONFIRMED:
        return 'bg-info-default text-white';
      case ORDER_STATUS.SHIPPED:
        return 'bg-primary-600 text-white';
      case ORDER_STATUS.DELIVERED:
        return 'bg-success-default text-white';
      case ORDER_STATUS.CANCELLED:
        return 'bg-error-default text-white';
      default:
        return 'bg-neutral-400 text-white';
    }
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case ORDER_STATUS.PENDING:
        return 'clock-outline';
      case ORDER_STATUS.CONFIRMED:
        return 'check-circle-outline';
      case ORDER_STATUS.SHIPPED:
        return 'truck-delivery-outline';
      case ORDER_STATUS.DELIVERED:
        return 'package-variant-closed-check';
      case ORDER_STATUS.CANCELLED:
        return 'close-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };
  
  const renderOrderItem = ({ item, index }) => {
    const statusColor = getStatusColor(item.state);
    const statusIcon = getStatusIcon(item.state);
    
    // Format date
    const orderDate = new Date(item.date_order);
    const formattedDate = orderDate.toLocaleDateString();
    
    return (
      <AnimatedView
        animation="fadeInUp"
        delay={index * 100}
        duration={500}
      >
        <StyledTouchableOpacity
          onPress={() => navigateToOrderDetails(item.id)}
          className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4"
        >
          <StyledView className="border-b border-neutral-200 dark:border-neutral-700 p-3 flex-row justify-between items-center">
            <StyledText className="text-neutral-900 dark:text-white font-medium">
              {t('orderNumber')}: {item.name}
            </StyledText>
            
            <StyledView className={`px-2 py-1 rounded-full ${statusColor}`}>
              <StyledText className="text-xs font-medium">
                {t(item.state)}
              </StyledText>
            </StyledView>
          </StyledView>
          
          <StyledView className="p-3">
            <StyledView className="flex-row justify-between mb-2">
              <StyledText className="text-neutral-600 dark:text-neutral-400">
                {t('orderDate')}
              </StyledText>
              <StyledText className="text-neutral-900 dark:text-white">
                {formattedDate}
              </StyledText>
            </StyledView>
            
            <StyledView className="flex-row justify-between mb-2">
              <StyledText className="text-neutral-600 dark:text-neutral-400">
                {t('items')}
              </StyledText>
              <StyledText className="text-neutral-900 dark:text-white">
                {item.order_line.length}
              </StyledText>
            </StyledView>
            
            <StyledView className="flex-row justify-between">
              <StyledText className="text-neutral-600 dark:text-neutral-400">
                {t('total')}
              </StyledText>
              <StyledText className="text-primary-600 dark:text-primary-400 font-bold">
                ₹{item.amount_total.toFixed(2)}
              </StyledText>
            </StyledView>
          </StyledView>
          
          <StyledView className="bg-neutral-100 dark:bg-neutral-700 p-3 flex-row justify-between items-center">
            <StyledView className="flex-row items-center">
              <Icon name={statusIcon} size={16} color="#6b7280" />
              <StyledText className="text-neutral-600 dark:text-neutral-300 ml-1">
                {t(item.state)}
              </StyledText>
            </StyledView>
            
            <StyledView className="flex-row items-center">
              <StyledText className="text-primary-600 dark:text-primary-400 mr-1">
                {t('viewDetails')}
              </StyledText>
              <Icon name="chevron-right" size={16} color="#0284c7" />
            </StyledView>
          </StyledView>
        </StyledTouchableOpacity>
      </AnimatedView>
    );
  };
  
  if (!isAuthenticated) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('orders')} showBack={false} />
        
        <EmptyState
          title={t('loginRequired')}
          message={t('loginToViewOrders')}
          buttonTitle={t('login')}
          onButtonPress={() => navigation.navigate('Auth', { screen: ROUTES.LOGIN })}
        />
      </StyledView>
    );
  }
  
  if (loading && !refreshing && !orders.length) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('orders')} showBack={false} />
        <LoadingIndicator fullScreen />
      </StyledView>
    );
  }
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('orders')} showBack={false} />
      
      <StyledFlatList
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerClassName="p-4"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme === 'dark' ? '#ffffff' : '#0284c7']}
            tintColor={theme === 'dark' ? '#ffffff' : '#0284c7'}
          />
        }
        ListEmptyComponent={
          <EmptyState
            title={t('noOrders')}
            message={t('noOrdersDescription')}
            buttonTitle={t('continueShopping')}
            onButtonPress={() => navigation.navigate('HomeTab')}
          />
        }
      />
    </StyledView>
  );
};

export default OrdersScreen;
