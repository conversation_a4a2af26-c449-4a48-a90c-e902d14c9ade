import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Image, TouchableOpacity } from 'react-native';
import { styled } from 'nativewind';
import { useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-reanimated';

import Header from '../../components/Header';
import Button from '../../components/Button';
import LoadingIndicator from '../../components/LoadingIndicator';
import { getOrderDetails } from '../../api/odooApi';
import { showToast } from '../../store/slices/uiSlice';
import { ORDER_STATUS } from '../../constants';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledScrollView = styled(ScrollView);
const StyledImage = styled(Image);
const StyledTouchableOpacity = styled(TouchableOpacity);
const AnimatedView = Animatable.createAnimatableComponent(styled(View));

const OrderDetailsScreen = () => {
  const route = useRoute();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  const { orderId } = route.params || {};
  const { sessionId } = useSelector((state) => state.auth);
  
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    loadOrderDetails();
  }, [orderId]);
  
  const loadOrderDetails = async () => {
    if (!orderId) return;
    
    setLoading(true);
    try {
      const response = await getOrderDetails(orderId, sessionId);
      if (response.success) {
        setOrder(response.order);
      } else {
        dispatch(showToast({
          message: response.error || t('failedToLoadOrderDetails'),
          type: 'error',
        }));
      }
    } catch (error) {
      dispatch(showToast({
        message: error.message || t('failedToLoadOrderDetails'),
        type: 'error',
      }));
    } finally {
      setLoading(false);
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case ORDER_STATUS.PENDING:
        return 'bg-warning-default text-white';
      case ORDER_STATUS.CONFIRMED:
        return 'bg-info-default text-white';
      case ORDER_STATUS.SHIPPED:
        return 'bg-primary-600 text-white';
      case ORDER_STATUS.DELIVERED:
        return 'bg-success-default text-white';
      case ORDER_STATUS.CANCELLED:
        return 'bg-error-default text-white';
      default:
        return 'bg-neutral-400 text-white';
    }
  };
  
  const getStatusStepCompleted = (orderStatus, step) => {
    const statusOrder = [
      ORDER_STATUS.PENDING,
      ORDER_STATUS.CONFIRMED,
      ORDER_STATUS.SHIPPED,
      ORDER_STATUS.DELIVERED,
    ];
    
    if (orderStatus === ORDER_STATUS.CANCELLED) {
      return false;
    }
    
    const orderStatusIndex = statusOrder.indexOf(orderStatus);
    const stepIndex = statusOrder.indexOf(step);
    
    return stepIndex <= orderStatusIndex;
  };
  
  if (loading) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('orderDetails')} />
        <LoadingIndicator fullScreen />
      </StyledView>
    );
  }
  
  if (!order) {
    return (
      <StyledView className="flex-1 bg-white dark:bg-neutral-900">
        <Header title={t('orderDetails')} />
        <StyledView className="flex-1 items-center justify-center p-6">
          <StyledText className="text-neutral-900 dark:text-white text-lg text-center mb-4">
            {t('orderNotFound')}
          </StyledText>
          <Button
            title={t('retry')}
            onPress={loadOrderDetails}
          />
        </StyledView>
      </StyledView>
    );
  }
  
  // Format date
  const orderDate = new Date(order.date_order);
  const formattedDate = orderDate.toLocaleDateString();
  const formattedTime = orderDate.toLocaleTimeString();
  
  return (
    <StyledView className="flex-1 bg-white dark:bg-neutral-900">
      <Header title={t('orderDetails')} />
      
      <StyledScrollView className="flex-1">
        <StyledView className="p-4">
          <AnimatedView animation="fadeIn" duration={300}>
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4">
              <StyledView className="border-b border-neutral-200 dark:border-neutral-700 p-3 flex-row justify-between items-center">
                <StyledText className="text-neutral-900 dark:text-white font-medium">
                  {t('orderNumber')}: {order.name}
                </StyledText>
                
                <StyledView className={`px-2 py-1 rounded-full ${getStatusColor(order.state)}`}>
                  <StyledText className="text-xs font-medium">
                    {t(order.state)}
                  </StyledText>
                </StyledView>
              </StyledView>
              
              <StyledView className="p-3">
                <StyledView className="flex-row justify-between mb-2">
                  <StyledText className="text-neutral-600 dark:text-neutral-400">
                    {t('orderDate')}
                  </StyledText>
                  <StyledText className="text-neutral-900 dark:text-white">
                    {formattedDate} {formattedTime}
                  </StyledText>
                </StyledView>
                
                <StyledView className="flex-row justify-between mb-2">
                  <StyledText className="text-neutral-600 dark:text-neutral-400">
                    {t('paymentMethod')}
                  </StyledText>
                  <StyledText className="text-neutral-900 dark:text-white">
                    {order.payment_method || t('notSpecified')}
                  </StyledText>
                </StyledView>
                
                <StyledView className="flex-row justify-between">
                  <StyledText className="text-neutral-600 dark:text-neutral-400">
                    {t('paymentStatus')}
                  </StyledText>
                  <StyledText 
                    className={
                      order.payment_status === 'paid' 
                        ? 'text-success-default' 
                        : 'text-warning-default'
                    }
                  >
                    {order.payment_status === 'paid' ? t('paid') : t('pending')}
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeIn" duration={300} delay={100}>
            <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-3">
              {t('orderStatus')}
            </StyledText>
            
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4 p-4">
              {order.state !== ORDER_STATUS.CANCELLED ? (
                <StyledView className="mb-2">
                  <StyledView className="flex-row items-center mb-4">
                    <StyledView 
                      className={`w-8 h-8 rounded-full items-center justify-center ${
                        getStatusStepCompleted(order.state, ORDER_STATUS.PENDING)
                          ? 'bg-primary-600'
                          : 'bg-neutral-300 dark:bg-neutral-600'
                      }`}
                    >
                      <Icon 
                        name="check" 
                        size={16} 
                        color="#ffffff" 
                      />
                    </StyledView>
                    
                    <StyledView className="ml-3 flex-1">
                      <StyledText className="text-neutral-900 dark:text-white font-medium">
                        {t('orderPlaced')}
                      </StyledText>
                      <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                        {formattedDate}
                      </StyledText>
                    </StyledView>
                  </StyledView>
                  
                  <StyledView className="flex-row items-center mb-4">
                    <StyledView 
                      className={`w-8 h-8 rounded-full items-center justify-center ${
                        getStatusStepCompleted(order.state, ORDER_STATUS.CONFIRMED)
                          ? 'bg-primary-600'
                          : 'bg-neutral-300 dark:bg-neutral-600'
                      }`}
                    >
                      {getStatusStepCompleted(order.state, ORDER_STATUS.CONFIRMED) ? (
                        <Icon name="check" size={16} color="#ffffff" />
                      ) : (
                        <Icon name="clock-outline" size={16} color="#ffffff" />
                      )}
                    </StyledView>
                    
                    <StyledView className="ml-3 flex-1">
                      <StyledText className="text-neutral-900 dark:text-white font-medium">
                        {t('orderConfirmed')}
                      </StyledText>
                      {getStatusStepCompleted(order.state, ORDER_STATUS.CONFIRMED) ? (
                        <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                          {formattedDate}
                        </StyledText>
                      ) : (
                        <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                          {t('pending')}
                        </StyledText>
                      )}
                    </StyledView>
                  </StyledView>
                  
                  <StyledView className="flex-row items-center mb-4">
                    <StyledView 
                      className={`w-8 h-8 rounded-full items-center justify-center ${
                        getStatusStepCompleted(order.state, ORDER_STATUS.SHIPPED)
                          ? 'bg-primary-600'
                          : 'bg-neutral-300 dark:bg-neutral-600'
                      }`}
                    >
                      {getStatusStepCompleted(order.state, ORDER_STATUS.SHIPPED) ? (
                        <Icon name="check" size={16} color="#ffffff" />
                      ) : (
                        <Icon name="truck-outline" size={16} color="#ffffff" />
                      )}
                    </StyledView>
                    
                    <StyledView className="ml-3 flex-1">
                      <StyledText className="text-neutral-900 dark:text-white font-medium">
                        {t('orderShipped')}
                      </StyledText>
                      {getStatusStepCompleted(order.state, ORDER_STATUS.SHIPPED) ? (
                        <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                          {formattedDate}
                        </StyledText>
                      ) : (
                        <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                          {t('pending')}
                        </StyledText>
                      )}
                    </StyledView>
                  </StyledView>
                  
                  <StyledView className="flex-row items-center">
                    <StyledView 
                      className={`w-8 h-8 rounded-full items-center justify-center ${
                        getStatusStepCompleted(order.state, ORDER_STATUS.DELIVERED)
                          ? 'bg-primary-600'
                          : 'bg-neutral-300 dark:bg-neutral-600'
                      }`}
                    >
                      {getStatusStepCompleted(order.state, ORDER_STATUS.DELIVERED) ? (
                        <Icon name="check" size={16} color="#ffffff" />
                      ) : (
                        <Icon name="package-variant-closed" size={16} color="#ffffff" />
                      )}
                    </StyledView>
                    
                    <StyledView className="ml-3 flex-1">
                      <StyledText className="text-neutral-900 dark:text-white font-medium">
                        {t('orderDelivered')}
                      </StyledText>
                      {getStatusStepCompleted(order.state, ORDER_STATUS.DELIVERED) ? (
                        <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                          {formattedDate}
                        </StyledText>
                      ) : (
                        <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                          {t('pending')}
                        </StyledText>
                      )}
                    </StyledView>
                  </StyledView>
                </StyledView>
              ) : (
                <StyledView className="flex-row items-center">
                  <StyledView className="w-8 h-8 rounded-full items-center justify-center bg-error-default">
                    <Icon name="close" size={16} color="#ffffff" />
                  </StyledView>
                  
                  <StyledView className="ml-3 flex-1">
                    <StyledText className="text-neutral-900 dark:text-white font-medium">
                      {t('orderCancelled')}
                    </StyledText>
                    <StyledText className="text-neutral-600 dark:text-neutral-400 text-xs">
                      {formattedDate}
                    </StyledText>
                  </StyledView>
                </StyledView>
              )}
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeIn" duration={300} delay={200}>
            <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-3">
              {t('orderItems')}
            </StyledText>
            
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4">
              {order.order_line.map((item, index) => {
                // Convert base64 image to URI if available
                const imageUri = item.product_image
                  ? `data:image/jpeg;base64,${item.product_image}`
                  : 'https://via.placeholder.com/100';
                
                return (
                  <StyledView 
                    key={item.id.toString()}
                    className={`p-3 flex-row ${
                      index < order.order_line.length - 1
                        ? 'border-b border-neutral-200 dark:border-neutral-700'
                        : ''
                    }`}
                  >
                    <StyledImage
                      source={{ uri: imageUri }}
                      className="w-16 h-16 rounded-md"
                      resizeMode="cover"
                    />
                    
                    <StyledView className="ml-3 flex-1 justify-center">
                      <StyledText 
                        numberOfLines={2}
                        className="text-neutral-900 dark:text-white font-medium mb-1"
                      >
                        {item.product_name}
                      </StyledText>
                      
                      <StyledView className="flex-row justify-between">
                        <StyledText className="text-neutral-600 dark:text-neutral-400">
                          {t('quantity')}: {item.product_uom_qty}
                        </StyledText>
                        
                        <StyledText className="text-primary-600 dark:text-primary-400 font-medium">
                          ₹{(item.price_unit * item.product_uom_qty).toFixed(2)}
                        </StyledText>
                      </StyledView>
                    </StyledView>
                  </StyledView>
                );
              })}
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeIn" duration={300} delay={300}>
            <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-3">
              {t('shippingAddress')}
            </StyledText>
            
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4 p-3">
              <StyledText className="text-neutral-900 dark:text-white font-medium mb-1">
                {order.partner_shipping_id?.name || order.partner_id?.name || ''}
              </StyledText>
              
              <StyledText className="text-neutral-600 dark:text-neutral-400">
                {order.partner_shipping_id?.street || order.partner_id?.street || ''}
              </StyledText>
              
              <StyledText className="text-neutral-600 dark:text-neutral-400">
                {`${order.partner_shipping_id?.city || order.partner_id?.city || ''}, ${order.partner_shipping_id?.state_id?.[1] || order.partner_id?.state_id?.[1] || ''} ${order.partner_shipping_id?.zip || order.partner_id?.zip || ''}`}
              </StyledText>
              
              <StyledText className="text-neutral-600 dark:text-neutral-400">
                {order.partner_shipping_id?.country_id?.[1] || order.partner_id?.country_id?.[1] || ''}
              </StyledText>
              
              <StyledText className="text-neutral-600 dark:text-neutral-400 mt-1">
                {`${t('phone')}: ${order.partner_shipping_id?.phone || order.partner_id?.phone || ''}`}
              </StyledText>
            </StyledView>
          </AnimatedView>
          
          <AnimatedView animation="fadeIn" duration={300} delay={400}>
            <StyledText className="text-lg font-bold text-neutral-900 dark:text-white mb-3">
              {t('paymentSummary')}
            </StyledText>
            
            <StyledView className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden border border-neutral-200 dark:border-neutral-700 mb-4 p-3">
              <StyledView className="flex-row justify-between mb-2">
                <StyledText className="text-neutral-600 dark:text-neutral-400">
                  {t('subtotal')}
                </StyledText>
                <StyledText className="text-neutral-900 dark:text-white">
                  ₹{order.amount_untaxed.toFixed(2)}
                </StyledText>
              </StyledView>
              
              <StyledView className="flex-row justify-between mb-2">
                <StyledText className="text-neutral-600 dark:text-neutral-400">
                  {t('tax')}
                </StyledText>
                <StyledText className="text-neutral-900 dark:text-white">
                  ₹{(order.amount_tax || 0).toFixed(2)}
                </StyledText>
              </StyledView>
              
              {order.amount_discount > 0 && (
                <StyledView className="flex-row justify-between mb-2">
                  <StyledText className="text-neutral-600 dark:text-neutral-400">
                    {t('discount')}
                  </StyledText>
                  <StyledText className="text-success-default">
                    -₹{order.amount_discount.toFixed(2)}
                  </StyledText>
                </StyledView>
              )}
              
              <StyledView className="flex-row justify-between mb-2">
                <StyledText className="text-neutral-600 dark:text-neutral-400">
                  {t('shipping')}
                </StyledText>
                <StyledText className="text-neutral-900 dark:text-white">
                  ₹{(order.amount_delivery || 0).toFixed(2)}
                </StyledText>
              </StyledView>
              
              <StyledView className="border-t border-neutral-200 dark:border-neutral-700 mt-2 pt-2">
                <StyledView className="flex-row justify-between">
                  <StyledText className="text-neutral-900 dark:text-white font-bold">
                    {t('total')}
                  </StyledText>
                  <StyledText className="text-primary-600 dark:text-primary-400 font-bold">
                    ₹{order.amount_total.toFixed(2)}
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>
          </AnimatedView>
        </StyledView>
      </StyledScrollView>
      
      {order.state === ORDER_STATUS.SHIPPED && (
        <StyledView className="p-4 border-t border-neutral-200 dark:border-neutral-700">
          <Button
            title={t('trackOrder')}
            onPress={() => {
              // Implement tracking functionality
              dispatch(showToast({
                message: t('trackingNotAvailable'),
                type: 'info',
              }));
            }}
            fullWidth
          />
        </StyledView>
      )}
    </StyledView>
  );
};

export default OrderDetailsScreen;
