import React, { useEffect } from 'react';
import { View, Text, StatusBar, Animated, Easing } from 'react-native';
import { styled } from 'nativewind';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { ROUTES } from '../constants';
import { trackScreenView } from '../utils/analytics';

const StyledView = styled(View);
const StyledText = styled(Text);
const AnimatedView = styled(Animated.View);

const SplashScreen = () => {
  const navigation = useNavigation();
  const { isAuthenticated } = useSelector((state) => state.auth);

  // Animation values
  const logoScale = new Animated.Value(0.3);
  const logoOpacity = new Animated.Value(0);
  const textOpacity = new Animated.Value(0);

  useEffect(() => {
    // Track screen view
    trackScreenView('Splash');

    // Start animations
    Animated.sequence([
      // Fade in and scale up logo
      Animated.parallel([
        Animated.timing(logoScale, {
          toValue: 1,
          duration: 800,
          easing: Easing.elastic(1),
          useNativeDriver: true,
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),

      // Fade in text
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 400,
        delay: 200,
        useNativeDriver: true,
      }),
    ]).start();

    // Navigate after delay
    const timer = setTimeout(() => {
      if (isAuthenticated) {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Root' }],
        });
      } else {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Root' }],
        });
      }
    }, 2500);

    return () => clearTimeout(timer);
  }, [isAuthenticated, navigation]);

  return (
    <StyledView className="flex-1 bg-primary-600 items-center justify-center">
      <StatusBar barStyle="light-content" backgroundColor="#0284c7" />

      <AnimatedView
        style={{
          transform: [{ scale: logoScale }],
          opacity: logoOpacity,
        }}
      >
        <StyledView className="bg-white rounded-full p-6 items-center justify-center">
          <Icon name="shopping" size={80} color="#0284c7" />
        </StyledView>
      </AnimatedView>

      <AnimatedView
        style={{ opacity: textOpacity }}
        className="mt-6"
      >
        <StyledText className="text-white text-3xl font-bold mb-2 text-center">
          Siddhi eCommerce
        </StyledText>

        <StyledText className="text-white text-base text-center">
          Your one-stop shop for everything
        </StyledText>
      </AnimatedView>
    </StyledView>
  );
};

export default SplashScreen;
