export default {
  // Common
  appName: 'सिद्धि ई-कॉमर्स',
  loading: 'लोड हो रहा है...',
  retry: 'पुनः प्रयास करें',
  cancel: 'रद्द करें',
  confirm: 'पुष्टि करें',
  save: 'सहेजें',
  delete: 'हटाएं',
  edit: 'संपादित करें',
  search: 'खोजें',
  
  // Auth
  login: 'लॉगिन',
  logout: 'लॉगआउट',
  register: 'रजिस्टर',
  email: 'ईमेल',
  username: 'उपयोगकर्ता नाम',
  password: 'पासवर्ड',
  forgotPassword: 'पासवर्ड भूल गए?',
  dontHaveAccount: 'खाता नहीं है?',
  alreadyHaveAccount: 'पहले से ही खाता है?',
  
  // Home
  home: 'होम',
  featuredProducts: 'विशेष उत्पाद',
  newArrivals: 'नए आगमन',
  viewAll: 'सभी देखें',
  categories: 'श्रेणियाँ',
  
  // Products
  products: 'उत्पाद',
  product: 'उत्पाद',
  price: 'कीमत',
  quantity: 'मात्रा',
  description: 'विवरण',
  addToCart: 'कार्ट में जोड़ें',
  buyNow: 'अभी खरीदें',
  outOfStock: 'स्टॉक में नहीं है',
  variants: 'विविधताएँ',
  selectVariant: 'विविधता चुनें',
  
  // Cart
  cart: 'कार्ट',
  yourCart: 'आपका कार्ट',
  emptyCart: 'आपका कार्ट खाली है',
  continueShopping: 'खरीदारी जारी रखें',
  proceedToCheckout: 'चेकआउट करें',
  subtotal: 'उप-कुल',
  discount: 'छूट',
  tax: 'कर',
  shipping: 'शिपिंग',
  total: 'कुल',
  applyCoupon: 'कूपन लागू करें',
  couponCode: 'कूपन कोड',
  apply: 'लागू करें',
  
  // Checkout
  checkout: 'चेकआउट',
  shippingAddress: 'शिपिंग पता',
  billingAddress: 'बिलिंग पता',
  sameAsShipping: 'शिपिंग पते के समान',
  paymentMethod: 'भुगतान विधि',
  placeOrder: 'ऑर्डर करें',
  firstName: 'पहला नाम',
  lastName: 'अंतिम नाम',
  address: 'पता',
  city: 'शहर',
  state: 'राज्य',
  zipCode: 'पिन कोड',
  country: 'देश',
  phone: 'फोन',
  
  // Orders
  orders: 'ऑर्डर',
  myOrders: 'मेरे ऑर्डर',
  orderDetails: 'ऑर्डर विवरण',
  orderNumber: 'ऑर्डर नंबर',
  orderDate: 'ऑर्डर तिथि',
  orderStatus: 'ऑर्डर स्थिति',
  trackOrder: 'ऑर्डर ट्रैक करें',
  
  // Payment
  payment: 'भुगतान',
  paymentDetails: 'भुगतान विवरण',
  paymentMethod: 'भुगतान विधि',
  paymentStatus: 'भुगतान स्थिति',
  razorpay: 'रेज़रपे',
  upi: 'यूपीआई',
  creditCard: 'क्रेडिट कार्ड',
  debitCard: 'डेबिट कार्ड',
  netBanking: 'नेट बैंकिंग',
  wallet: 'वॉलेट',
  
  // Profile
  profile: 'प्रोफाइल',
  myProfile: 'मेरी प्रोफाइल',
  editProfile: 'प्रोफाइल संपादित करें',
  changePassword: 'पासवर्ड बदलें',
  addresses: 'पते',
  addAddress: 'पता जोड़ें',
  
  // Settings
  settings: 'सेटिंग्स',
  language: 'भाषा',
  theme: 'थीम',
  darkMode: 'डार्क मोड',
  lightMode: 'लाइट मोड',
  notifications: 'सूचनाएं',
  
  // Errors
  error: 'त्रुटि',
  somethingWentWrong: 'कुछ गलत हो गया',
  networkError: 'नेटवर्क त्रुटि',
  tryAgain: 'कृपया पुनः प्रयास करें',
  
  // Success
  success: 'सफलता',
  orderPlaced: 'ऑर्डर सफलतापूर्वक दिया गया',
  paymentSuccessful: 'भुगतान सफल',
  
  // Misc
  contactUs: 'संपर्क करें',
  aboutUs: 'हमारे बारे में',
  termsAndConditions: 'नियम और शर्तें',
  privacyPolicy: 'गोपनीयता नीति',
  faq: 'अक्सर पूछे जाने वाले प्रश्न',
  help: 'सहायता',
};
