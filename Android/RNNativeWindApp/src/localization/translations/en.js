export default {
  // Common
  appName: 'Siddhi eCommerce',
  loading: 'Loading...',
  retry: 'Retry',
  cancel: 'Cancel',
  confirm: 'Confirm',
  save: 'Save',
  delete: 'Delete',
  edit: 'Edit',
  search: 'Search',
  
  // Auth
  login: 'Login',
  logout: 'Logout',
  register: 'Register',
  email: 'Email',
  username: '<PERSON>rna<PERSON>',
  password: 'Password',
  forgotPassword: 'Forgot Password?',
  dontHaveAccount: 'Don\'t have an account?',
  alreadyHaveAccount: 'Already have an account?',
  
  // Home
  home: 'Home',
  featuredProducts: 'Featured Products',
  newArrivals: 'New Arrivals',
  viewAll: 'View All',
  categories: 'Categories',
  
  // Products
  products: 'Products',
  product: 'Product',
  price: 'Price',
  quantity: 'Quantity',
  description: 'Description',
  addToCart: 'Add to Cart',
  buyNow: 'Buy Now',
  outOfStock: 'Out of Stock',
  variants: 'Variants',
  selectVariant: 'Select Variant',
  
  // Cart
  cart: 'Cart',
  yourCart: 'Your Cart',
  emptyCart: 'Your cart is empty',
  continueShopping: 'Continue Shopping',
  proceedToCheckout: 'Proceed to Checkout',
  subtotal: 'Subtotal',
  discount: 'Discount',
  tax: 'Tax',
  shipping: 'Shipping',
  total: 'Total',
  applyCoupon: 'Apply Coupon',
  couponCode: 'Coupon Code',
  apply: 'Apply',
  
  // Checkout
  checkout: 'Checkout',
  shippingAddress: 'Shipping Address',
  billingAddress: 'Billing Address',
  sameAsShipping: 'Same as shipping address',
  paymentMethod: 'Payment Method',
  placeOrder: 'Place Order',
  firstName: 'First Name',
  lastName: 'Last Name',
  address: 'Address',
  city: 'City',
  state: 'State',
  zipCode: 'ZIP Code',
  country: 'Country',
  phone: 'Phone',
  
  // Orders
  orders: 'Orders',
  myOrders: 'My Orders',
  orderDetails: 'Order Details',
  orderNumber: 'Order Number',
  orderDate: 'Order Date',
  orderStatus: 'Order Status',
  trackOrder: 'Track Order',
  
  // Payment
  payment: 'Payment',
  paymentDetails: 'Payment Details',
  paymentMethod: 'Payment Method',
  paymentStatus: 'Payment Status',
  razorpay: 'Razorpay',
  upi: 'UPI',
  creditCard: 'Credit Card',
  debitCard: 'Debit Card',
  netBanking: 'Net Banking',
  wallet: 'Wallet',
  
  // Profile
  profile: 'Profile',
  myProfile: 'My Profile',
  editProfile: 'Edit Profile',
  changePassword: 'Change Password',
  addresses: 'Addresses',
  addAddress: 'Add Address',
  
  // Settings
  settings: 'Settings',
  language: 'Language',
  theme: 'Theme',
  darkMode: 'Dark Mode',
  lightMode: 'Light Mode',
  notifications: 'Notifications',
  
  // Errors
  error: 'Error',
  somethingWentWrong: 'Something went wrong',
  networkError: 'Network error',
  tryAgain: 'Please try again',
  
  // Success
  success: 'Success',
  orderPlaced: 'Order placed successfully',
  paymentSuccessful: 'Payment successful',
  
  // Misc
  contactUs: 'Contact Us',
  aboutUs: 'About Us',
  termsAndConditions: 'Terms and Conditions',
  privacyPolicy: 'Privacy Policy',
  faq: 'FAQ',
  help: 'Help',
};
