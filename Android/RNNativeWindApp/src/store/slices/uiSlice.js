import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  theme: 'light',
  language: 'en',
  isLoading: false,
  toast: {
    visible: false,
    message: '',
    type: 'info', // 'info', 'success', 'error', 'warning'
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action) => {
      state.theme = action.payload;
    },
    setLanguage: (state, action) => {
      state.language = action.payload;
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    showToast: (state, action) => {
      state.toast = {
        visible: true,
        message: action.payload.message,
        type: action.payload.type || 'info',
      };
    },
    hideToast: (state) => {
      state.toast.visible = false;
    },
  },
});

export const { setTheme, setLanguage, setLoading, showToast, hideToast } = uiSlice.actions;
export default uiSlice.reducer;
