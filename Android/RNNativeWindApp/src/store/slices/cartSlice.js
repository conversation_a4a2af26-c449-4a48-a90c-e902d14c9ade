import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getCart, addToCart, updateCart, applyCoupon } from '../../api/odooApi';

// Async thunks
export const fetchCart = createAsyncThunk(
  'cart/fetchCart',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.sessionId) {
        return rejectWithValue('User not authenticated');
      }
      
      const response = await getCart(auth.sessionId);
      if (response.success) {
        return response.cart;
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const addItemToCart = createAsyncThunk(
  'cart/addItem',
  async ({ productId, quantity }, { getState, rejectWithValue, dispatch }) => {
    try {
      const { auth } = getState();
      if (!auth.sessionId) {
        return rejectWithValue('User not authenticated');
      }
      
      const response = await addToCart(productId, quantity, auth.sessionId);
      if (response.success) {
        dispatch(fetchCart());
        return response.cart;
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateCartItem = createAsyncThunk(
  'cart/updateItem',
  async ({ lineId, quantity }, { getState, rejectWithValue, dispatch }) => {
    try {
      const { auth } = getState();
      if (!auth.sessionId) {
        return rejectWithValue('User not authenticated');
      }
      
      const response = await updateCart(lineId, quantity, auth.sessionId);
      if (response.success) {
        dispatch(fetchCart());
        return response.cart;
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const applyCouponCode = createAsyncThunk(
  'cart/applyCoupon',
  async (couponCode, { getState, rejectWithValue, dispatch }) => {
    try {
      const { auth } = getState();
      if (!auth.sessionId) {
        return rejectWithValue('User not authenticated');
      }
      
      const response = await applyCoupon(couponCode, auth.sessionId);
      if (response.success) {
        dispatch(fetchCart());
        return response.result;
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  items: [],
  total: 0,
  count: 0,
  coupon: null,
  discount: 0,
  loading: false,
  error: null,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearCart: (state) => {
      state.items = [];
      state.total = 0;
      state.count = 0;
      state.coupon = null;
      state.discount = 0;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch cart
      .addCase(fetchCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.order_lines || [];
        state.total = action.payload.amount_total || 0;
        state.count = action.payload.cart_quantity || 0;
        state.discount = action.payload.amount_discount || 0;
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch cart';
      })
      
      // Add item to cart
      .addCase(addItemToCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addItemToCart.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(addItemToCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to add item to cart';
      })
      
      // Update cart item
      .addCase(updateCartItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCartItem.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateCartItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update cart item';
      })
      
      // Apply coupon
      .addCase(applyCouponCode.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(applyCouponCode.fulfilled, (state, action) => {
        state.loading = false;
        state.coupon = action.payload.coupon_code || null;
      })
      .addCase(applyCouponCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to apply coupon';
      });
  },
});

export const { clearCart, clearError } = cartSlice.actions;
export default cartSlice.reducer;
