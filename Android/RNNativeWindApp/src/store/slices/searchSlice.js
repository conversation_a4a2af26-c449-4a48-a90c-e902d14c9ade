import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { searchProductsApi } from '../../api/odooApi';
import { MMKV } from 'react-native-mmkv';

// Initialize storage for search history
const storage = new MMKV();
const SEARCH_HISTORY_KEY = 'search_history';

// Get search history from storage
const getStoredSearchHistory = () => {
  const history = storage.getString(SEARCH_HISTORY_KEY);
  return history ? JSON.parse(history) : [];
};

// Save search history to storage
const saveSearchHistory = (history) => {
  storage.set(SEARCH_HISTORY_KEY, JSON.stringify(history));
};

// Async thunk for searching products
export const searchProducts = createAsyncThunk(
  'search/searchProducts',
  async (query, { rejectWithValue }) => {
    try {
      const response = await searchProductsApi(query);
      if (response.success) {
        return response.products;
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  query: '',
  searchResults: [],
  searchHistory: getStoredSearchHistory(),
  loading: false,
  error: null,
};

const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setQuery: (state, action) => {
      state.query = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    addToSearchHistory: (state, action) => {
      const query = action.payload;
      // Remove if already exists
      state.searchHistory = state.searchHistory.filter(item => item !== query);
      // Add to beginning of array
      state.searchHistory.unshift(query);
      // Limit to 10 items
      state.searchHistory = state.searchHistory.slice(0, 10);
      // Save to storage
      saveSearchHistory(state.searchHistory);
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
      saveSearchHistory([]);
    },
    removeFromSearchHistory: (state, action) => {
      state.searchHistory = state.searchHistory.filter(item => item !== action.payload);
      saveSearchHistory(state.searchHistory);
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(searchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to search products';
      });
  },
});

export const {
  setQuery,
  clearSearchResults,
  addToSearchHistory,
  clearSearchHistory,
  removeFromSearchHistory,
  clearError,
} = searchSlice.actions;

export default searchSlice.reducer;
