import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getProductCategories, getProducts, getProductDetails, getProductAttributes } from '../../api/odooApi';

// Async thunks
export const fetchCategories = createAsyncThunk(
  'products/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getProductCategories();
      if (response.success) {
        return response.categories;
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (params = {}, { rejectWithValue, getState }) => {
    try {
      const { products } = getState();
      const {
        categoryId = products.currentCategoryId,
        page = 0,
        limit = products.pageSize,
        filters = products.filters,
        search = products.searchQuery,
        sortBy = products.sortBy,
        sortOrder = products.sortOrder,
        resetFilters = false
      } = params;

      // If resetFilters is true, use empty filters
      const filtersToUse = resetFilters ? {} : filters;

      const response = await getProducts({
        categoryId,
        page,
        limit,
        filters: filtersToUse,
        search,
        sortBy,
        sortOrder
      });

      if (response.success) {
        return {
          products: response.products,
          categoryId,
          page,
          totalCount: response.totalCount,
          hasMore: response.hasMore,
          filters: filtersToUse,
          search,
          sortBy,
          sortOrder
        };
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchProductAttributes = createAsyncThunk(
  'products/fetchProductAttributes',
  async (categoryId = null, { rejectWithValue }) => {
    try {
      const response = await getProductAttributes(categoryId);
      if (response.success) {
        return response.attributes;
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchProductDetails = createAsyncThunk(
  'products/fetchProductDetails',
  async (productId, { rejectWithValue }) => {
    try {
      const response = await getProductDetails(productId);
      if (response.success) {
        return {
          product: response.product,
          variants: response.variants,
        };
      } else {
        return rejectWithValue(response.error);
      }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  categories: [],
  products: [],
  currentProduct: null,
  variants: [],
  attributes: [],
  currentCategoryId: null,
  page: 0,
  pageSize: 20,
  totalCount: 0,
  hasMore: true,
  filters: {},
  searchQuery: '',
  sortBy: 'name',
  sortOrder: 'asc',
  scrollPosition: 0,
  loading: false,
  error: null,
};

const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearProducts: (state) => {
      state.products = [];
      state.page = 0;
      state.hasMore = true;
    },
    clearCurrentProduct: (state) => {
      state.currentProduct = null;
      state.variants = [];
    },
    setFilters: (state, action) => {
      state.filters = action.payload;
      state.page = 0; // Reset pagination when filters change
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
      state.page = 0; // Reset pagination when search changes
    },
    setSorting: (state, action) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
      state.page = 0; // Reset pagination when sorting changes
    },
    setPageSize: (state, action) => {
      state.pageSize = action.payload;
      state.page = 0; // Reset pagination when page size changes
    },
    saveScrollPosition: (state, action) => {
      state.scrollPosition = action.payload;
    },
    resetFilters: (state) => {
      state.filters = {};
      state.page = 0;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch categories
      .addCase(fetchCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch categories';
      })

      // Fetch product attributes
      .addCase(fetchProductAttributes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductAttributes.fulfilled, (state, action) => {
        state.loading = false;
        state.attributes = action.payload;
      })
      .addCase(fetchProductAttributes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch product attributes';
      })

      // Fetch products
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;

        // If it's a new category, search, filter, or refresh, replace products
        if (
          state.currentCategoryId !== action.payload.categoryId ||
          action.payload.page === 0 ||
          state.searchQuery !== action.payload.search ||
          JSON.stringify(state.filters) !== JSON.stringify(action.payload.filters) ||
          state.sortBy !== action.payload.sortBy ||
          state.sortOrder !== action.payload.sortOrder
        ) {
          state.products = action.payload.products;
        } else {
          // Otherwise append products for pagination
          state.products = [...state.products, ...action.payload.products];
        }

        state.currentCategoryId = action.payload.categoryId;
        state.page = action.payload.page;
        state.totalCount = action.payload.totalCount;
        state.hasMore = action.payload.hasMore;
        state.filters = action.payload.filters;
        state.searchQuery = action.payload.search;
        state.sortBy = action.payload.sortBy;
        state.sortOrder = action.payload.sortOrder;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch products';
      })

      // Fetch product details
      .addCase(fetchProductDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.currentProduct = action.payload.product;
        state.variants = action.payload.variants;
      })
      .addCase(fetchProductDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch product details';
      });
  },
});

export const {
  clearProducts,
  clearCurrentProduct,
  setFilters,
  setSearchQuery,
  setSorting,
  setPageSize,
  saveScrollPosition,
  resetFilters,
  clearError
} = productsSlice.actions;

export default productsSlice.reducer;
