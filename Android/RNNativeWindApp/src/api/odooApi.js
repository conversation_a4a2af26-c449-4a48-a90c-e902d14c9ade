import axios from 'axios';
import { API } from '../constants';
import { fetchWithCache, generateCacheKey } from '../utils/apiCache';
import { getDataWithOfflineFallback, queueOfflineAction } from '../utils/offlineStorage';
import { checkNetworkConnection } from '../utils/networkMonitor';

// Odoo API configuration
const ODOO_URL = API.BASE_URL;
const DB_NAME = API.DB_NAME;

// Create axios instance for Odoo API
const odooApi = axios.create({
  baseURL: ODOO_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Authentication
export const authenticate = async (login, password) => {
  try {
    const response = await odooApi.post('/web/session/authenticate', {
      jsonrpc: '2.0',
      params: {
        db: DB_NAME,
        login,
        password,
      },
    });

    if (response.data.result) {
      return {
        success: true,
        uid: response.data.result.uid,
        sessionId: response.headers['set-cookie'],
        userData: response.data.result,
      };
    } else {
      return {
        success: false,
        error: 'Authentication failed',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Get product categories
export const getProductCategories = async () => {
  const fetchCategories = async () => {
    try {
      const response = await odooApi.post('/web/dataset/call_kw', {
        jsonrpc: '2.0',
        method: 'call',
        params: {
          model: 'product.public.category',
          method: 'search_read',
          args: [
            [['website_published', '=', true]],
            ['id', 'name', 'parent_id', 'image_1920', 'sequence']
          ],
          kwargs: {
            context: { lang: 'en_US' },
          },
        },
      });

      return {
        success: true,
        categories: response.data.result,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  };

  // Use cache for categories (1 hour expiry)
  const cacheKey = generateCacheKey('categories');
  return fetchWithCache(fetchCategories, cacheKey, 60 * 60 * 1000);
};

// Get products with advanced filtering
export const getProducts = async (params = {}) => {
  const {
    categoryId = null,
    page = 0,
    limit = 20,
    filters = {},
    search = '',
    sortBy = 'name',
    sortOrder = 'asc'
  } = params;

  const fetchProducts = async () => {
    try {
      // Build domain with category and search
      let domain = [['website_published', '=', true]];

      // Add category filter
      if (categoryId) {
        domain.push(['public_categ_ids', 'child_of', categoryId]);
      }

      // Add search filter
      if (search) {
        domain.push('|', ['name', 'ilike', search], ['default_code', 'ilike', search]);
      }

      // Add attribute filters
      Object.entries(filters).forEach(([attributeId, values]) => {
        if (Array.isArray(values) && values.length > 0) {
          // For multi-select attributes
          const attributeConditions = values.map(value => [
            'attribute_line_ids.value_ids', 'in', [parseInt(value)]
          ]);
          domain.push('|', ...attributeConditions);
        } else if (values) {
          // For single-select attributes
          domain.push(['attribute_line_ids.value_ids', 'in', [parseInt(values)]]);
        }
      });

      // Get product count for pagination
      const countResponse = await odooApi.post('/web/dataset/call_kw', {
        jsonrpc: '2.0',
        method: 'call',
        params: {
          model: 'product.template',
          method: 'search_count',
          args: [domain],
          kwargs: {
            context: { lang: 'en_US' },
          },
        },
      });

      // Get products with sorting
      const response = await odooApi.post('/web/dataset/call_kw', {
        jsonrpc: '2.0',
        method: 'call',
        params: {
          model: 'product.template',
          method: 'search_read',
          args: [
            domain,
            [
              'id', 'name', 'list_price', 'description_sale', 'image_1920',
              'public_categ_ids', 'website_url', 'product_variant_ids',
              'default_code', 'attribute_line_ids'
            ]
          ],
          kwargs: {
            context: { lang: 'en_US' },
            offset: page * limit,
            limit: limit,
            order: `${sortBy} ${sortOrder}`,
          },
        },
      });

      return {
        success: true,
        products: response.data.result,
        totalCount: countResponse.data.result,
        hasMore: (page + 1) * limit < countResponse.data.result,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  };

  // Use cache for products (30 minutes expiry)
  // First page of products is cached, subsequent pages are not
  if (page === 0 && !search && Object.keys(filters).length === 0) {
    const cacheKey = generateCacheKey('products', { categoryId, limit, sortBy, sortOrder });
    return fetchWithCache(fetchProducts, cacheKey, 30 * 60 * 1000);
  }

  return fetchProducts();
};

// Search products API
export const searchProductsApi = async (query) => {
  try {
    const domain = [
      ['website_published', '=', true],
      '|', ['name', 'ilike', query], ['default_code', 'ilike', query]
    ];

    const response = await odooApi.post('/web/dataset/call_kw', {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        model: 'product.template',
        method: 'search_read',
        args: [
          domain,
          ['id', 'name', 'list_price', 'default_code']
        ],
        kwargs: {
          context: { lang: 'en_US' },
          limit: 10,
        },
      },
    });

    return {
      success: true,
      products: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Get product attributes for filtering
export const getProductAttributes = async (categoryId = null) => {
  const fetchAttributes = async () => {
    try {
      // Get all product attributes
      const attributesResponse = await odooApi.post('/web/dataset/call_kw', {
        jsonrpc: '2.0',
        method: 'call',
        params: {
          model: 'product.attribute',
          method: 'search_read',
          args: [
            [['create_variant', '=', true]],
            ['id', 'name', 'display_type', 'create_variant']
          ],
          kwargs: {
            context: { lang: 'en_US' },
          },
        },
      });

      // Get attribute values for each attribute
      const attributes = [];
      for (const attribute of attributesResponse.data.result) {
        const valuesResponse = await odooApi.post('/web/dataset/call_kw', {
          jsonrpc: '2.0',
          method: 'call',
          params: {
            model: 'product.attribute.value',
            method: 'search_read',
            args: [
              [['attribute_id', '=', attribute.id]],
              ['id', 'name', 'html_color', 'sequence']
            ],
            kwargs: {
              context: { lang: 'en_US' },
              order: 'sequence',
            },
          },
        });

        // Get product count for each attribute value in this category
        const values = [];
        for (const value of valuesResponse.data.result) {
          let domain = [['website_published', '=', true], ['attribute_line_ids.value_ids', 'in', [value.id]]];
          if (categoryId) {
            domain.push(['public_categ_ids', 'child_of', categoryId]);
          }

          const countResponse = await odooApi.post('/web/dataset/call_kw', {
            jsonrpc: '2.0',
            method: 'call',
            params: {
              model: 'product.template',
              method: 'search_count',
              args: [domain],
              kwargs: {
                context: { lang: 'en_US' },
              },
            },
          });

          if (countResponse.data.result > 0) {
            values.push({
              id: value.id,
              name: value.name,
              htmlColor: value.html_color,
              count: countResponse.data.result,
            });
          }
        }

        // Only add attributes that have values with products
        if (values.length > 0) {
          attributes.push({
            id: attribute.id,
            name: attribute.name,
            displayType: attribute.display_type,
            type: attribute.display_type === 'color' ? 'color' :
                  attribute.display_type === 'select' ? 'single' : 'multi',
            options: values,
          });
        }
      }

      return {
        success: true,
        attributes,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  };

  // Use cache for attributes (1 hour expiry)
  const cacheKey = generateCacheKey('product_attributes', { categoryId });
  return fetchWithCache(fetchAttributes, cacheKey, 60 * 60 * 1000);
};

// Get product details
export const getProductDetails = async (productId) => {
  const fetchProductDetails = async () => {
    try {
      const response = await odooApi.post('/web/dataset/call_kw', {
        jsonrpc: '2.0',
        method: 'call',
        params: {
          model: 'product.template',
          method: 'read',
          args: [
            [productId],
            ['id', 'name', 'list_price', 'description', 'description_sale', 'image_1920', 'product_variant_ids', 'attribute_line_ids']
          ],
          kwargs: {
            context: { lang: 'en_US' },
          },
        },
      });

      // Get product variants
      const variantIds = response.data.result[0].product_variant_ids;
      const variantsResponse = await odooApi.post('/web/dataset/call_kw', {
        jsonrpc: '2.0',
        method: 'call',
        params: {
          model: 'product.product',
          method: 'read',
          args: [
            variantIds,
            ['id', 'name', 'list_price', 'image_1920', 'attribute_value_ids']
          ],
          kwargs: {
            context: { lang: 'en_US' },
          },
        },
      });

      return {
        success: true,
        product: response.data.result[0],
        variants: variantsResponse.data.result,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  };

  // Use cache for product details (1 hour expiry)
  const cacheKey = generateCacheKey('product_details', { productId });
  return fetchWithCache(fetchProductDetails, cacheKey, 60 * 60 * 1000);
};

// Add to cart
export const addToCart = async (productId, quantity = 1, sessionId) => {
  // Check network connection
  const isConnected = await checkNetworkConnection();

  if (!isConnected) {
    // Queue the action for later execution
    queueOfflineAction('ADD_TO_CART', { productId, quantity, sessionId });

    return {
      success: true,
      cart: null,
      isOffline: true,
      message: 'Added to cart. Will be synchronized when online.',
    };
  }

  try {
    const response = await odooApi.post('/shop/cart/update_json', {
      jsonrpc: '2.0',
      params: {
        product_id: productId,
        add_qty: quantity,
      },
    }, {
      headers: {
        'Cookie': sessionId,
      }
    });

    return {
      success: true,
      cart: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Get cart
export const getCart = async (sessionId) => {
  const fetchCart = async () => {
    try {
      const response = await odooApi.post('/shop/cart/json', {
        jsonrpc: '2.0',
      }, {
        headers: {
          'Cookie': sessionId,
        }
      });

      return {
        success: true,
        cart: response.data.result,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  };

  // Use offline fallback for cart data
  return getDataWithOfflineFallback(
    fetchCart,
    `cart_${sessionId}`,
    30 * 60 * 1000 // 30 minutes expiry
  );
};

// Update cart
export const updateCart = async (lineId, quantity, sessionId) => {
  try {
    const response = await odooApi.post('/shop/cart/update_json', {
      jsonrpc: '2.0',
      params: {
        line_id: lineId,
        set_qty: quantity,
      },
    }, {
      headers: {
        'Cookie': sessionId,
      }
    });

    return {
      success: true,
      cart: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Apply coupon
export const applyCoupon = async (couponCode, sessionId) => {
  try {
    const response = await odooApi.post('/shop/coupon/apply', {
      jsonrpc: '2.0',
      params: {
        promo: couponCode,
      },
    }, {
      headers: {
        'Cookie': sessionId,
      }
    });

    return {
      success: true,
      result: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Get orders
export const getOrders = async (sessionId) => {
  try {
    const response = await odooApi.post('/my/orders/json', {
      jsonrpc: '2.0',
    }, {
      headers: {
        'Cookie': sessionId,
      }
    });

    return {
      success: true,
      orders: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Get order details
export const getOrderDetails = async (orderId, sessionId) => {
  try {
    const response = await odooApi.post(`/my/orders/${orderId}/json`, {
      jsonrpc: '2.0',
    }, {
      headers: {
        'Cookie': sessionId,
      }
    });

    return {
      success: true,
      order: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Checkout
export const checkout = async (shippingInfo, billingInfo, sessionId) => {
  try {
    const response = await odooApi.post('/shop/checkout', {
      jsonrpc: '2.0',
      params: {
        ...shippingInfo,
        ...billingInfo,
      },
    }, {
      headers: {
        'Cookie': sessionId,
      }
    });

    return {
      success: true,
      result: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Process payment
export const processPayment = async (paymentMethod, orderId, sessionId) => {
  try {
    const response = await odooApi.post('/shop/payment/transaction', {
      jsonrpc: '2.0',
      params: {
        acquirer_id: paymentMethod,
        order_id: orderId,
      },
    }, {
      headers: {
        'Cookie': sessionId,
      }
    });

    return {
      success: true,
      result: response.data.result,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

export default {
  authenticate,
  getProductCategories,
  getProducts,
  getProductDetails,
  addToCart,
  getCart,
  updateCart,
  applyCoupon,
  getOrders,
  getOrderDetails,
  checkout,
  processPayment,
};
