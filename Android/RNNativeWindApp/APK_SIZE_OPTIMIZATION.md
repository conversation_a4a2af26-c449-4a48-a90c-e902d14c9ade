# APK Size Optimization Guide

This document outlines the techniques used to reduce the APK size of the Siddhi eCommerce React Native application.

## Implemented Optimizations

The following optimizations have been implemented in the project:

### 1. JavaScript Engine Optimization

- **Hermes JavaScript Engine**: Enabled Hermes for better performance and smaller bundle size
  - Location: `android/gradle.properties` - `hermesEnabled=true`

### 2. Code Minification and Obfuscation

- **ProGuard/R8**: Enabled code shrinking, obfuscation, and optimization
  - Location: `android/app/build.gradle` - `minifyEnabled true`
  - Custom rules: `android/app/proguard-rules.pro`

- **Resource Shrinking**: Removes unused resources
  - Location: `android/app/build.gradle` - `shrinkResources true`

### 3. Image Optimization

- **Image Compression**: Automated script to optimize PNG and JPEG files
  - Script: `optimize-images.sh`
  - Uses `pngquant` and `jpegoptim` for lossless/lossy compression

### 4. Build Configuration Optimizations

- **ABI Splits**: Creates separate APKs for different CPU architectures
  - Location: `android/app/build.gradle` - `splits { abi { ... } }`

- **R8 Full Mode**: More aggressive code optimization
  - Location: `android/gradle.properties` - `android.enableR8.fullMode=true`

- **Non-Transitive R Class**: Reduces R class size
  - Location: `android/gradle.properties` - `android.nonTransitiveRClass=true`

### 5. Bundle Optimization

- **Production Bundle**: Generates optimized JavaScript bundle
  - Command: `npx react-native bundle --platform android --dev false ...`
  - Script: `build-optimized-apk.sh`

## Size Comparison

| Build Type | Original Size | Optimized Size | Reduction |
|------------|--------------|----------------|-----------|
| Debug APK  | 172MB        | ~40-60MB*      | ~65-75%   |
| Release APK| N/A          | ~15-25MB*      | N/A       |

*Actual size may vary based on content and assets

## Additional Optimization Techniques

These techniques can be implemented for further size reduction:

### 1. Asset Management

- **On-Demand Resources**: Load non-critical assets from a server instead of bundling them
- **Vector Graphics**: Use SVG instead of PNG where possible
- **Font Subsetting**: Include only the characters you need from font files

### 2. Native Module Optimization

- **Selective Native Modules**: Only include the native modules you actually use
- **Dynamic Feature Modules**: Split app into base and feature modules (Android App Bundle)

### 3. Code Splitting

- **Dynamic Imports**: Load components only when needed
- **Tree Shaking**: Remove unused JavaScript code

## How to Build an Optimized APK

Run the provided script:

```bash
./build-optimized-apk.sh
```

This script will:
1. Optimize image assets
2. Clean the project
3. Generate an optimized JavaScript bundle
4. Remove unused resources
5. Build both release and debug APKs with all optimizations enabled

## References

- [React Native Performance Guide](https://reactnative.dev/docs/performance)
- [Android Developer Guide - Reduce APK Size](https://developer.android.com/topic/performance/reduce-apk-size)
- [Hermes Engine Documentation](https://reactnative.dev/docs/hermes)
- [ProGuard Manual](https://www.guardsquare.com/manual/home)
- [ChatGPT Reference Article](https://chatgpt.com/share/6809a3d0-146c-8007-bdda-eb473dcf1ed6)
