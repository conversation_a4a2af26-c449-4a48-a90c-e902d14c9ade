{"name": "RNNativeWindApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "start-remote": "react-native start --host 0.0.0.0", "android-remote": "react-native run-android --host 0.0.0.0", "docker-build": "docker-compose build", "docker-start": "docker-compose up", "test": "jest", "bundle-android": "mkdir -p android/app/src/main/assets && react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "bundle-ios": "react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle --assets-dest ios", "clean-android": "cd android && ./gradlew clean && cd ..", "build-android": "npm run bundle-android && cd android && ./gradlew assembleDebug && cd ..", "postinstall": "patch-package"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.8.4", "i18next": "^25.0.0", "nativewind": "^4.0.1", "react": "19.0.0", "react-i18next": "^15.4.1", "react-native": "0.79.1", "react-native-config": "^1.5.5", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.25.0", "react-native-mmkv": "^3.2.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-upi-payment": "^1.0.5", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@testing-library/react-native": "^12.4.3", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "tailwindcss": "^3.3.2", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "jest": {"preset": "react-native", "setupFiles": ["./jest.setup.js"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|react-native-vector-icons|react-native-reanimated|@react-navigation)/)"], "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"], "collectCoverage": true, "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/assets/**", "!src/constants/**", "!**/node_modules/**"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}}