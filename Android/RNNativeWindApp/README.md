# Siddhi eCommerce App

A React Native mobile application for eCommerce with Odoo 18 integration, built using React Native CLI and NativeWind.

## Features

- **Authentication**: Login, Register, and Forgot Password
- **Product Browsing**: Categories, Product Listings, and Product Details
- **Shopping Cart**: Add to Cart, Update Quantity, Apply Coupon
- **Checkout Flow**: Shipping Address, Billing Address, Order Review
- **Payment Integration**: Razorpay and UPI Payment
- **Order Management**: Order History and Order Details
- **User Profile**: Edit Profile, Address Management
- **Multi-language Support**: English and Hindi
- **Theme Support**: Light and Dark Mode
- **Odoo Integration**: Connect to Odoo 18 backend
- **Docker Support**: Containerized development environment
- **Remote Development**: Connect from Windows 11 to Ubuntu server

# Getting Started

> **Note**: Make sure you have completed the [Set Up Your Environment](https://reactnative.dev/docs/set-up-your-environment) guide before proceeding.

## Step 1: Start Metro

First, you will need to run **Metro**, the JavaScript build tool for React Native.

To start the Metro dev server, run the following command from the root of your React Native project:

```sh
# Using npm
npm start

# OR using Yarn
yarn start
```

## Step 2: Build and run your app

With Metro running, open a new terminal window/pane from the root of your React Native project, and use one of the following commands to build and run your Android or iOS app:

### Android

```sh
# Using npm
npm run android

# OR using Yarn
yarn android
```

### iOS

For iOS, remember to install CocoaPods dependencies (this only needs to be run on first clone or after updating native deps).

The first time you create a new project, run the Ruby bundler to install CocoaPods itself:

```sh
bundle install
```

Then, and every time you update your native dependencies, run:

```sh
bundle exec pod install
```

For more information, please visit [CocoaPods Getting Started guide](https://guides.cocoapods.org/using/getting-started.html).

```sh
# Using npm
npm run ios

# OR using Yarn
yarn ios
```

If everything is set up correctly, you should see your new app running in the Android Emulator, iOS Simulator, or your connected device.

This is one way to run your app — you can also build it directly from Android Studio or Xcode.

## Step 3: Modify your app

Now that you have successfully run the app, let's make changes!

Open `App.tsx` in your text editor of choice and make some changes. When you save, your app will automatically update and reflect these changes — this is powered by [Fast Refresh](https://reactnative.dev/docs/fast-refresh).

When you want to forcefully reload, for example to reset the state of your app, you can perform a full reload:

- **Android**: Press the <kbd>R</kbd> key twice or select **"Reload"** from the **Dev Menu**, accessed via <kbd>Ctrl</kbd> + <kbd>M</kbd> (Windows/Linux) or <kbd>Cmd ⌘</kbd> + <kbd>M</kbd> (macOS).
- **iOS**: Press <kbd>R</kbd> in iOS Simulator.

## Congratulations! :tada:

You've successfully run and modified your React Native App. :partying_face:

### Now what?

- If you want to add this new React Native code to an existing application, check out the [Integration guide](https://reactnative.dev/docs/integration-with-existing-apps).
- If you're curious to learn more about React Native, check out the [docs](https://reactnative.dev/docs/getting-started).

## Project Structure

```
src/
├── api/               # API services for Odoo integration
├── assets/            # Images, fonts, and other static assets
├── components/        # Reusable UI components
├── constants/         # App constants and configuration
├── localization/      # i18n setup and translations
├── navigation/        # Navigation configuration
├── screens/           # App screens
├── store/             # Redux store setup and slices
└── theme/             # Theme configuration
```

## Odoo Integration

The app connects to Odoo 18 using the JSON-RPC API. The connection details are configured in `src/constants/index.js`.

Before running the app, update the Odoo API configuration:

```javascript
// src/constants/index.js
export const API = {
  BASE_URL: 'http://your-odoo-server:8069',
  TIMEOUT: 10000,
  DB_NAME: 'your_database_name',
};
```

## Payment Integration

- **Razorpay**: Integrated for card payments, net banking, etc.
- **UPI Payment**: Integrated for UPI-based payments

Update the Razorpay API key in `src/screens/payment/PaymentScreen.js`:

```javascript
const options = {
  // ...
  key: 'your_razorpay_key_here',
  // ...
};
```

## Remote Development (Ubuntu Server to Windows 11)

1. Start the Metro bundler with remote access:
   ```bash
   npm run start-remote
   ```

2. Run the Android app with remote access:
   ```bash
   npm run android-remote
   ```

3. Connect to the server from your Windows 11 machine:
   - Make sure ADB is installed on your Windows machine
   - Connect to the Ubuntu server via ADB:
     ```bash
     adb connect <ubuntu-server-ip>:5555
     ```
   - Open the app in your emulator or device

## Docker Development with SSH and Automated Testing

### Setting Up Docker Environment

1. Build and start the Docker container:
   ```bash
   docker-compose up --build
   ```

2. The container will start with:
   - Metro bundler on port 8081
   - SSH server on port 2222 (password: password)
   - Appium server on port 4723
   - Android emulator ports (5554-5557)

### Connecting from Windows to Ubuntu Server

#### SSH Connection

1. Install an SSH client like PuTTY or use Windows Terminal with OpenSSH.

2. Connect to the Docker container:
   ```bash
   ssh -p 2222 root@<server-ip>
   # Password: password
   ```

3. Once connected, you can run commands inside the container.

#### X11 Forwarding (Optional, for GUI)

1. Install an X server for Windows like VcXsrv or Xming.

2. Start the X server with "Disable access control" checked.

3. Connect with X11 forwarding:
   ```bash
   ssh -p 2222 -X root@<server-ip>
   ```

4. You can now run GUI applications from the container.

### Building the App

1. Connect to the container via SSH.

2. Run the automated build script:
   ```bash
   cd /app
   ./run-automated-test.sh
   ```

3. This script will:
   - Build the app with proper bundle
   - Start an Android emulator
   - Install the app on the emulator
   - Run automated tests using Appium
   - Generate a test report and screenshot

### Manual Testing

1. Connect to the Docker container from your Windows 11 machine:
   - Make sure ADB is installed on your Windows machine
   - Connect to the Docker container via ADB:
     ```bash
     adb connect <server-ip>:5555
     ```
   - Open the app in your emulator or device

# Troubleshooting

If you're having issues getting the above steps to work, see the [Troubleshooting](https://reactnative.dev/docs/troubleshooting) page.

# Learn More

To learn more about React Native, take a look at the following resources:

- [React Native Website](https://reactnative.dev) - learn more about React Native.
- [Getting Started](https://reactnative.dev/docs/environment-setup) - an **overview** of React Native and how setup your environment.
- [Learn the Basics](https://reactnative.dev/docs/getting-started) - a **guided tour** of the React Native **basics**.
- [Blog](https://reactnative.dev/blog) - read the latest official React Native **Blog** posts.
- [`@facebook/react-native`](https://github.com/facebook/react-native) - the Open Source; GitHub **repository** for React Native.
