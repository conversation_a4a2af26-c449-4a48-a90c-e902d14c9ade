[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/var/run/supervisord.pid

[program:xvfb]
command=Xvfb :1 -screen 0 1280x800x24
autorestart=true
stdout_logfile=/var/log/xvfb.log
stderr_logfile=/var/log/xvfb.err

[program:x11vnc]
command=x11vnc -display :1 -nopw -forever -shared
autorestart=true
stdout_logfile=/var/log/x11vnc.log
stderr_logfile=/var/log/x11vnc.err
environment=DISPLAY=:1

[program:novnc]
command=/opt/novnc/utils/novnc_proxy --vnc localhost:5900 --listen 6080
autorestart=true
stdout_logfile=/var/log/novnc.log
stderr_logfile=/var/log/novnc.err

[program:emulator]
command=/opt/android-sdk/emulator/emulator -avd web_emulator -no-audio -gpu swiftshader_indirect -no-boot-anim -no-snapshot
autorestart=true
stdout_logfile=/var/log/emulator.log
stderr_logfile=/var/log/emulator.err
environment=DISPLAY=:1
