# Web-Accessible Android Emulator

This setup allows you to run an Android emulator that is accessible through a web browser, making it easy to test your React Native app without a physical device.

## Quick Start

1. Build and start the emulator container:
   ```bash
   ./run-emulator-docker.sh
   ```

2. Access the emulator through your web browser:
   ```
   http://YOUR_SERVER_IP:6080/
   ```

3. Wait for the emulator to fully boot (may take a few minutes)

4. Install the app on the emulator:
   ```bash
   docker exec -it android-web-emulator /app/install-apk-on-emulator.sh
   ```

## Features

- **Web-based access**: Access the Android emulator from any device with a web browser
- **Automated APK installation**: Script to automatically install and launch your app
- **Persistent emulator**: The emulator state is preserved between container restarts
- **Optimized for testing**: Configured for headless server environments

## Troubleshooting

### Emulator doesn't appear in the browser

Check if the container is running:
```bash
docker ps | grep android-web-emulator
```

Check the container logs:
```bash
docker logs android-web-emulator
```

### APK installation fails

Make sure the emulator is fully booted:
```bash
docker exec -it android-web-emulator adb shell getprop sys.boot_completed
```

This should return "1" if the emulator is ready.

Check if the APK exists:
```bash
docker exec -it android-web-emulator ls -la /app/android/app/build/outputs/apk/debug/app-debug.apk
```

### JavaScript bundle issues

If you see "AppRegistryBinding::startSurface failed" error, try manually generating the bundle:
```bash
docker exec -it android-web-emulator bash -c "cd /app && mkdir -p android/app/src/main/assets && npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res"
```

Then reinstall the app:
```bash
docker exec -it android-web-emulator adb install -r /app/android/app/build/outputs/apk/debug/app-debug.apk
```

## Advanced Usage

### Accessing the emulator via ADB

You can connect to the emulator using ADB from your host machine:
```bash
adb connect localhost:5555
```

### Taking screenshots

```bash
docker exec -it android-web-emulator adb shell screencap -p /sdcard/screenshot.png
docker exec -it android-web-emulator adb pull /sdcard/screenshot.png /app/
```

### Recording video

```bash
docker exec -it android-web-emulator adb shell screenrecord /sdcard/recording.mp4
# Press Ctrl+C to stop recording after a few seconds
docker exec -it android-web-emulator adb pull /sdcard/recording.mp4 /app/
```

## Security Considerations

This setup is intended for development and testing purposes only. The VNC server is configured without a password for simplicity. In a production environment, you should:

1. Set a VNC password
2. Use SSL/TLS encryption
3. Restrict access using a firewall
4. Consider using SSH tunneling for secure remote access
