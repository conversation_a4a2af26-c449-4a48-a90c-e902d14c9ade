#!/bin/bash

# Script to optimize images in the React Native project
# This reduces the size of image assets which can significantly reduce APK size

echo "Starting image optimization..."

# Install required tools if not already installed
if ! command -v pngquant &> /dev/null; then
    echo "Installing pngquant..."
    apt-get update && apt-get install -y pngquant
fi

if ! command -v jpegoptim &> /dev/null; then
    echo "Installing jpegoptim..."
    apt-get update && apt-get install -y jpegoptim
fi

# Find and optimize PNG files
echo "Optimizing PNG files..."
find ./src -name "*.png" -exec pngquant --force --ext .png --speed 1 {} \;

# Find and optimize JPG/JPEG files
echo "Optimizing JPG/JPEG files..."
find ./src -name "*.jpg" -o -name "*.jpeg" -exec jpegoptim --strip-all --max=85 {} \;

echo "Image optimization completed!"
