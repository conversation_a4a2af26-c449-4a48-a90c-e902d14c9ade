#!/bin/bash

# <PERSON><PERSON>t to run the Android emulator in Docker

echo "Building and starting the Android emulator in Docker..."

# Build the Docker image
docker build -t android-web-emulator -f Dockerfile.emulator .

# Run the Docker container
docker run -d --name android-web-emulator \
  --privileged \
  -p 6080:6080 \
  -p 5554:5554 \
  -p 5555:5555 \
  -v $(pwd):/app \
  android-web-emulator

echo "Android emulator container started!"
echo "Web access will be available at: http://YOUR_SERVER_IP:6080/vnc.html"
echo "It may take a few minutes for the emulator to fully boot."
echo ""
echo "To check the emulator status:"
echo "  docker logs -f android-web-emulator"
echo ""
echo "To install the APK once the emulator is ready:"
echo "  docker exec -it android-web-emulator /app/install-apk-on-emulator.sh"
