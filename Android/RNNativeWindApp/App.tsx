/**
 * Siddhi eCommerce App with React Native and NativeWind
 *
 * @format
 */

import React, { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { I18nextProvider } from 'react-i18next';

// Import store and navigation
import { store, persistor } from './src/store';
import AppNavigator from './src/navigation';
import i18n from './src/localization/i18n';

// Import components
import Toast from './src/components/Toast';
import NetworkStatusBar from './src/components/NetworkStatusBar';
import ErrorBoundary from './src/components/ErrorBoundary';

// Import services
import { startSyncService } from './src/services/syncService';

function App(): React.JSX.Element {
  useEffect(() => {
    // Start background sync service
    const unsubscribe = startSyncService();

    // Cleanup on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <I18nextProvider i18n={i18n}>
          <SafeAreaProvider>
            <ErrorBoundary>
              <GestureHandlerRootView style={{ flex: 1 }}>
                <AppNavigator />
                <Toast />
                <NetworkStatusBar />
              </GestureHandlerRootView>
            </ErrorBoundary>
          </SafeAreaProvider>
        </I18nextProvider>
      </PersistGate>
    </Provider>
  );
}

export default App;
