#!/bin/bash

# Script to properly fix bundle issues for React Native app

echo "Starting comprehensive bundle fix process..."

# Step 1: Clean up build artifacts
echo "Cleaning up build artifacts..."
cd android && ./gradlew clean && cd ..
rm -rf android/app/build

# Step 2: Fix babel.config.js
echo "Fixing babel.config.js..."
cat > babel.config.js << 'EOF'
module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    "nativewind/babel",
    'react-native-reanimated/plugin',
  ],
};
EOF

# Step 3: Fix global.css
echo "Fixing global.css..."
cat > global.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;
EOF

# Step 4: Fix index.js
echo "Fixing index.js..."
# We'll create a backup first
cp index.js index.js.backup

# Update the import for global.css
sed -i 's|import ./global.css|import "./global.css"|g' index.js

# Step 5: Create assets directory
echo "Creating assets directory..."
mkdir -p android/app/src/main/assets

# Step 6: Clear Metro bundler cache
echo "Clearing Metro bundler cache..."
npx react-native start --reset-cache &
METRO_PID=$!
sleep 10
kill $METRO_PID

# Step 7: Generate the bundle
echo "Generating the bundle..."
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# Step 8: Build the app
echo "Building the app..."
cd android && ./gradlew assembleDebug --exclude-task externalNativeBuildDebug --exclude-task configureCMakeDebug

echo "Build process completed!"
echo "Debug APK is available at: android/app/build/outputs/apk/debug/app-debug.apk"
echo ""
echo "If you still encounter issues, please check the troubleshooting guide in test-apk-instructions.md"
