#!/bin/bash

# Fix bundle issues for React Native app

echo "Starting bundle fix process..."

# Step 1: Clean the project
echo "Cleaning Android project..."
cd android && ./gradlew clean && cd ..

# Step 2: Create assets directory if it doesn't exist
echo "Creating assets directory..."
mkdir -p android/app/src/main/assets

# Step 3: Generate the bundle
echo "Generating bundle..."
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# Step 4: Remove duplicate resources to prevent build errors
echo "Removing duplicate resources..."
rm -rf android/app/src/main/res/drawable-*
rm -rf android/app/src/main/res/raw

# Step 5: Build the debug APK
echo "Building debug APK..."
cd android && ./gradlew assembleDebug && cd ..

echo "Bundle fix process completed!"
echo "Debug APK should be available at: android/app/build/outputs/apk/debug/app-debug.apk"
