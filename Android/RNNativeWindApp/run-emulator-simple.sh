#!/bin/bash

# A simple script to run the Android emulator directly

echo "Starting Android emulator..."

# Install required packages
apt-get update && apt-get install -y x11vnc xvfb novnc websockify

# Set up environment variables
export ANDROID_HOME=/usr/local/android-sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator

# Create a virtual display with Xvfb
Xvfb :1 -screen 0 1280x800x24 &
export DISPLAY=:1

# Wait for Xvfb to start
sleep 2

# Start VNC server
x11vnc -display :1 -nopw -forever -shared -quiet &

# Start noVNC (HTML5 VNC client)
websockify --web=/usr/share/novnc/ 6080 localhost:5900 &

# Wait for display to be ready
sleep 3

# Check if the emulator exists
if ! emulator -list-avds | grep -q "web_emulator"; then
  echo "Creating AVD..."
  echo "no" | avdmanager create avd \
    --name web_emulator \
    --package "system-images;android-33;google_apis;x86_64" \
    --device "pixel_5"
fi

# Start the emulator
emulator -avd web_emulator -no-audio -gpu swiftshader_indirect -no-boot-anim -no-snapshot &
EMULATOR_PID=$!

echo "Emulator starting..."
echo "Web access available at: http://YOUR_SERVER_IP:6080/"
echo "Waiting for emulator to boot..."

# Wait for emulator to boot
adb wait-for-device
adb shell 'while [[ "$(getprop sys.boot_completed)" != "1" ]]; do sleep 1; done'

echo "Emulator is ready!"
echo "To install the APK, run:"
echo "./install-apk-on-emulator.sh"

# Keep script running
wait $EMULATOR_PID
