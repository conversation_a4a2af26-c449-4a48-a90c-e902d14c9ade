#!/bin/bash

# <PERSON>ript to run automated tests on the app

echo "=================================================================="
echo "🚀 Starting automated testing process for Siddhi eCommerce app"
echo "=================================================================="
echo "Time: $(date)"
echo ""

# Build the app with proper bundle
echo "📦 Building the app with proper bundle..."
cd android

# Create assets directory if it doesn't exist
mkdir -p app/src/main/assets

# Generate the bundle
echo "🔧 Generating JavaScript bundle..."
cd ..
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

# Build the APK
echo "🔨 Building the APK..."
cd android
./gradlew assembleDebug --no-daemon

# Check if build was successful
if [ ! -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
  echo "❌ Build failed! APK not found."
  exit 1
fi

echo "✅ APK built successfully at: app/build/outputs/apk/debug/app-debug.apk"
cd ..

# Check if Android emulator is running
echo "📱 Checking emulator status..."
if ! adb devices | grep -q "emulator"; then
  echo "🔄 No Android emulator found. Starting emulator..."
  # Start the emulator in the background
  emulator -avd test_device -no-audio -no-window &
  EMULATOR_PID=$!

  # Wait for emulator to boot
  echo "⏳ Waiting for emulator to boot..."
  adb wait-for-device
  adb shell 'while [[ "$(getprop sys.boot_completed)" != "1" ]]; do sleep 1; done'
  echo "✅ Emulator started and booted."
else
  echo "✅ Emulator already running."
fi

# Install the app on the emulator
echo "📲 Installing the app on the emulator..."
adb install -r android/app/build/outputs/apk/debug/app-debug.apk

# Check if Appium is installed
if ! command -v appium &> /dev/null; then
  echo "🔄 Appium not found. Installing Appium..."
  npm install -g appium
  appium driver install uiautomator2
fi

# Start Appium server
echo "🚀 Starting Appium server..."
appium --log appium.log &
APPIUM_PID=$!

# Wait for Appium to start
echo "⏳ Waiting for Appium to start..."
sleep 5

# Install test dependencies if needed
if [ ! -f "node_modules/webdriverio/package.json" ]; then
  echo "📦 Installing test dependencies..."
  npm install webdriverio
fi

# Run the test
echo "🧪 Running automated tests..."
node automated-test.js
TEST_RESULT=$?

# Stop Appium server
echo "🛑 Stopping Appium server..."
kill $APPIUM_PID

# Stop emulator if we started it
if [ ! -z "$EMULATOR_PID" ]; then
  echo "🛑 Stopping emulator..."
  kill $EMULATOR_PID
fi

echo "=================================================================="
echo "✅ Automated testing process completed!"
echo "=================================================================="

# Return the test result
exit $TEST_RESULT
